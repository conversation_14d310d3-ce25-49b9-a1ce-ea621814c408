Cornell AI history lecture - YouTube
https://www.youtube.com/watch?v=CcP8db8TeKI

Transcript:
(00:00) So, do you want to maybe give a really quick intro of yourself before we get started? Yeah, sure. Um, so, uh, I'm Kan and good to be here today. So, um, I have been working on this AI stuff for a few year. uh and um I have at openai I've been mainly um focusing on 01 preview 01 and most recently deep research and now I'm more on the this agent side of things and so um yeah very excited about reasoning and uh agents and just those things um so okay great um all right let me get started so uh I will talk about some AI stuff today obviously but before
(00:51) that let's actually um start looking at um let's see um this chat PT generated uh image of a flower a budding flower if you stare at this um for like a minute or 10 minutes you don't really see any change does that mean there's no change underlying this process there is and if If you wait long enough, then you'll see some big changes to a full-blown uh rose.
(01:24) And so what I'm trying to get at with this uh toy image is that we are really not good at um perceiving the changes that occur in you know like minute or like even uh days or years but we're pretty good at the minute and hour scale changes. So I think there's probably a evolutionary explanation for this uh where if you can uh perceive the changes in um in the environment uh in the changes in minutes probably very helpful for survival and if you for like a changes in over a year not so much um so I think that probably uh is some some built-in um uh
(02:08) deficiency uh that we probably need to correct. So um why am I talking about this? I think um actually AI is a probably the the fastest moving technology of all time. Even then it doesn't move in like minutes or hours. It still moves over a few years time frame or even a decades. So um I think given this um deficiency I just talked about we might be underestimating the change especially the magnitude of the change uh this AI is bringing about and so um I think at this point I don't have to convince that the AI is important
(02:53) even a few years ago I start my talk like that but now I think that's given but I do want to emphasize that maybe what everyone's thinking about how big of this change is uh is very different uh and if anything I think we might be underestimating it and especially for um this AI which I'm going to argue as a leverage mechanisms for uh individuals and humanity as a whole that aspect uh might be somewhat underestimated I'll get into the details of what what I mean by that but let's build uh intuition slowly at a time starting with this key word leverage. So it's a very important
(03:37) concept and used in a somewhat um casual way informally and sometimes overloaded and and um depends on this context. Uh especially in the Silicon Valley Bay Area this term is used a lot. I think this is such a important word that uh it's worth spending some time and building an intuition around this.
(04:04) So um for me the first encounter of this concept was probably this classical mechanics where uh we have this uh lever. So in this case let's think about this um try to uh put it some pressure force down on the left hand side on the screen and then as an output of that they that we're kind of um lifting up the 1 kilogram uh mass on the right.
(04:30) So the lever if we enlarge this uh lever um and then we can actually um you know lift up heavier objects. So what this means is that the input downward force is the same but by increasing this leverage the output was increased by 3x. And so um this actually is a very general concept and I would like to um pull out u maybe this is my working definition of leverage.
(04:57) It's a mechanism through which a small change or no change in input results in larger change or very large change in output. Um and this is a very general thing and it's not just about the classical mechanics context that we just saw. I think it can be applied to many other places.
(05:17) Um and so this actually is a um you know very important concept and I you know we many people want to increase the output. I want to um contribute more. I want to generate more. Um then the first thing the most natural thing that comes to uh our mind uh is probably how do I work harder? How can I increase my input? Um I want to like sleep less and um um you know things like that.
(05:42) But I think that's there's a certain limit to it. Instead, the more important question is how do I increase the output without actually increasing the input or how can I disconnect the relationship between the input and the output or linear relationship between them and that is getting at uh to the core of this leverage mechanism.
(06:01) And so what you're looking for there is um what leverage mechanism do I have and can I have and that's uh if you want to increase the output that's the question that we have to think about and as a general concept many many um different ways of thinking about this leverage my personal favorite is by uh Naval Rob and from this um book actually I have it um you know in the in here I strongly recommend it.
(06:30) So this is um uh not by his book but it's just a collection of many of his thoughts like uh tweets and so on. According to Naval there are three u types of uh leverage and human labor capital and code and media. So let's think about uh these things um uh individually. So first type is the human labor and this is the oldest type of uh leverage and and as such probably the most familiar one and so as an example um let's think about a scenario where I want to build a pyramid without the leverage I will be building a loan and that's probably quite
(07:10) difficult and with leverage I can hire uh thousands of human workers and so my input uh is probably the same or uh even less I now don't have to work as much but the output is much higher because there are thousands of people working on this. So this is um kind of a uh the leverage type with the permission because I need to ask the permissions of these uh people and uh we still have this uh human labor as one of the main leverage mechanisms in the society. Second type is a capital.
(07:41) So uh let's think about this um scenario where I want to invest in a real estate uh that's worth a million dollar. I only have 200k. So I do borrow 800k from a bank and let's say I get lucky and this thing gets um doubled in valuation to 2 million. Um I it just doubled but my uh return is 400% or 500% but it just went up by like a lot more because I just borrowed more from it.
(08:11) Um and so this is kind of the uh second type and I think it's more of a common common thing for uh 20th century and and so on. So third type is more recent uh thing especially more common in the the area which is the code and so more of a software type and so if I write a code for an app I build it and then there's one user who gets the value n um let's suppose that number is positive and while I'm sleeping one more user downloads it and installs it and gets another value n so I'll put just double without me having to do any additional work so it's possible because it's the software which is uh you know copy and pasted which is a very
(08:51) interesting nature of this and a lot of the recent value has been created uh leveraging this so and media is similar in that um let's say I uh give a lecture to 200 people um those people got some value again assuming that's positive I post it in YouTube any additional view of the same lecture I don't have to do any work but somehow the value goes and the limit is actually um uh pretty much endless. So that's the the new type of leverage too.
(09:26) And historically large wealth creation um utilize these forms of leverage and so um like the 20th centuries uh wealth a lot of them financial industries um leveraged a lot of this capital leverage and then um recently especially in this um area tech companies have leveraged the fact that if you write uh code the output can be multiplied pretty much indefinitely and then Um so those are the wealth creation mechanisms and if you look at the history of wealth um big wealth generated and you can probably identify such a leverage. So but then the upside of the leverage mechanism is also um
(10:09) competed away and so what I mean by that is if something is good then many people realize it and that's going to be um uh competition. So if you start a company that is only leveraging this um the fact that it's a software doesn't leverage other technologies then it's probably much harder to uh succeed now compared to say 20 years ago or maybe um I'm less familiar with this but YouTuber you want to become a YouTuber now probably more difficult than um 10 years uh ago just based on the competition. So I what I'm trying to get at is um when this
(10:44) leverage mechanism is uh just became possible because of some technology much larger values and returns are possible then uh and then it's going to be competed away. So I think it's a very important to think about what are the new leverage mechanisms that are becoming available and so um obviously I'm going to argue that AI is the um relatively new one that is coming into the picture and it's slowly expanding its scope and reach um so to from an individual levels to um you know groups of people and to the point where it can
(11:21) uh benefit entire humanity. Let's maybe start looking at this uh at an individual level. Um I personally use AI or chachd for me is um in a education context. So I uh that's probably the biggest one for me and I think I spend a lot of time especially uh weekends uh just learning about new concepts and um asking now I think talk about this uh even like hours and just learning about new things and so here just again if you think about leverage uh we think about AI as in leverage uh this learning what what is an input and output the input is my time and effort to understand some concept that I don't
(12:03) understand and the output is the conceptual understanding happening uh in my brain and maybe also some knowledge but that's uh less of an important thing. So with the AI this given input results in um you know larger output. So let's say if I'm trying to um learn some specific things about a distributed system um then before this kind of a generative AI I would have to you know Google this and then probably a Wikipedia page which is typically not beginner friendly. So I'll be uh reading it then I don't understand and probably don't feel good and then I'll try to
(12:41) find a introductory course maybe a textbook but then I want only one concept out of this but I kind of have to build around the context like at least terminologies and that's very timeconuming now because the AI can uh contextualize everything I know and generate dynamically just the right amount of materials at the right difficulty I can uh much learn much easier. So I think that's the the the big uh lessons from the learning.
(13:11) So um what I'm saying is the barrier to learn new area is like collapsing essentially. Um this is good and you probably heard about this a lot but is this good just everywhere. I I think there's some uh we have to be careful and have a comprehensive look and then um when everything is um easier to learn, people are learning everything, then there's an opportunity cost of not learning is getting higher.
(13:41) Um so as an extreme example, if you don't use AI, you just don't think about it at all and you just do your thing. you're not lazy, just doing your own thing and everyone else is just learning new things and uh getting better and um so on then you're kind of behind uh in the society.
(14:00) That's the opportunity cost of just you don't contribute to it but just because uh it's um the the relatively um decided what the valuable skill is that's uh what's happening. So um yeah I think the in in just the society the valuable skill is um determined based on um the supply and demand and what is scarce um as opposed to an objective value it provides.
(14:26) So um I think one extreme example is a human vision um and it's u from objective perspective this is a extremely complicated and advanced um feature and if you study the computer vision probably know that oh human vision is incredible like you sometimes recognize uh your friends or in a setting where it probably is very difficult um and you got you sometimes get surprised but this incredible um capability is so abundant um that it's uh having that doesn't help you excelling in the modern society um and so I think um uh similarly just like
(15:11) scarcity is um very important um necessary condition for any u highly valuable skill. So um and I often think about what are some great skills or opportunities to to have and often a good good um rule of thumb is uh whatever evolution did not teach equip us then um that's kind of a good starting point u because if it did everyone has it built in to DNA and that's probably not that uh scarce.
(15:51) So um that's kind of the yeah uh my just some implications of learning getting easier which is not probably not that u obvious and so this acquisition of the new knowledge gets cheap. The scarce factor is the the this motivation to explore and curiosity is kind of the characteristics that probably will be more important.
(16:10) So why I mean it has been always important but I think it's getting more and more um because the learning um cost is going down but not zero. So you still have to overcome this um barrier and learning a new concept is not something um that I would say everyone finds pleasing because you feel challenged and this cognitive um challenge is not um comfortable.
(16:34) And so to overcome that uh curiosity can be like I I know there's a pain but I need to get this because I'm so u curious about it. That's a really strong force. If you're not curious I think there's a way to kind of um have a correction mechanism um which is okay I I I'm going to go through this short-term pain but there will be a long-term um rewards some fulfilling um things happening.
(16:58) So if you build enough of this rewards cycle I think you can get over it. But anyway uh broad more broadly technology changes what is scarce just makes um and just being aware of the such changes um even if you're not directly contributing to it uh is um I think u very important so that's one uh learning affecting learning uh is one way AI is acting as a leverage the other maybe more um uh intuitive one is this uh AI agent uh this is probably the most um interesting saying research area in 2025 and maybe
(17:34) more. Um so uh here AI agent is um kind of combining the uh the two types of leverage mechanisms that we uh saw before. First one's the human labor because AI agent is doing the work for you. So that's kind of the human labor part um as if you hired them.
(17:57) And then the second part is um at least the the the current um AI agents are um you know software uh only and so you can kind of copy and paste if you want um 10 output 10 agents working together just do it if you want 12 then just copy uh two more and you don't have to ask for the permission just permissionless uh form of uh composite leverage mechanism that I think is uh quite profound if you think about it.
(18:26) Um so I think that's going to be um um main's um source of uh wealth generations uh going forward and this thing is very new. I think it's just got started. Um I if you have used uh deep research that's um to me the most uh well functioning uh AI agent as of now. Uh there will be more probably but uh that's at least the the kind of the first working agent for me.
(18:51) uh and so that increases my uh output by a lot and probably many others too. So individuals are quite uh supercharged and this means um you know small teams consist of uh individuals um generating really big values that's becoming more and more common. So in the you might heard about the startups with like 10 people 20 people generating like hundreds of millions of dollars of revenue.
(19:25) uh that probably is un uh just imaginable like 10 years ago but now still uncommon but uh we're seeing this and I think behind the scene uh it's more of a AI acting as a leverage and individuals are just generating a lot more um outputs and so previously if you want to increase the output then you again you want to think about the leverage mechanism and after raising funds and whatever the capital leverage is out of question then you have to really think about this human labor leverage and hire more people but then uh human collaboration especially at larger scale has quite a bit of overhead
(20:00) um there's communication um you know just uh it's very difficult uh problem um and maybe some people don't get along with other people and so just adding one person to say 100 person uh group doesn't mean the output goes up by 1% it can even be um negative or uh can be anything.
(20:25) So um now with supercharged individuals this um overhead uh is becoming less favorable and maybe we'll see more and more of smaller teams generating u uh quite a bit of value and more of a companies might be of this size and obviously there will be big companies but this might be a more common thing and I think so far this has been like a individual level and uh some implications of the group as a result um I think they're slowly uh again this is like a the the flower analogy from the beginning.
(20:57) This is a change that is very big but uh it's so slow that I think it's kind of an under um estimated by many people and it's acting at the humanities level. So let's think about uh this uh so if we think about the the just all of humans here um what is the the task or goals um we might have uh I think this there's no right answer but to me one of the most important things is to continue to generate um value and to thrive and so what is the most um sustainable engines of growth and um value creation uh there many pro but for me the most sustainable engine is um
(21:41) scientific advances um discovering new knowledge and suddenly uh the like you know what you think about as a thought about as a nonresource just becomes resource because now you have a new knowledge to leverage that oil for example is just sticky uh liquid now you know how to do this um burn this and thermodynamic understanding you can uh that's such a valuable resource u many instances like that.
(22:12) So um here um I if you think about from historical perspective since the like 17th century or roughly that time scientific revolution uh the the wealth creation just um uh like really took off and like a hockey stick uh shape of uh economic metric um since then. Um, and back then was there were probably a lot of lowhanging fruits in terms of the scientific progress.
(22:38) Uh, because if you're the first one to do science, then probably there's a lot of easy things to do. Uh, I'm not saying everything was easy because there's other challenges given the context. But still from an objective complexity perspective, this is probably much easier than what is happening now.
(22:58) So advancing science in modern society is a lot more complicated. So maybe you can think about Newtonian versus quantum mechanics or if you want to make a um you advanced chip making advanced computer chips uh that is probably beyond any single humans um capability way beyond that. So it it's getting a lot more complicated and involves u sometimes involves larger collaborations among people and more capital and so on and also in addition to this u increasing complexity of the technology cutting edge technologies human intelligence is not growing u it's
(23:34) I don't even know it's a growing but it's kind of a stagnant compared to the rate at which the scientific complexity increases. So um these factors uh put together I think are kind of the uh bottlenecks in the further advancing this um core mission of uh keep advancing scientific progress.
(24:00) So um we have done great job whenever such um bottleneck happens we find a way to get uh out of it and we build the tools to unblock ourselves from uh achieving the mission. This time I think we should do the same thing with the AI being this tool that will um be the most useful thing and maybe even better a superhuman in the research capability so that we can continue this uh scientific advances and I I think there are many purposes of AI but to me this is the most um important single most important purpose of it is to augment uh in continuing this uh grand mission of scientific
(24:38) advances. So uh now again just from the perspective of the leverage uh let's think about the input and the output. The input is the collective human effort um scientists here and there uh just working together or like implicit together uh and then the output is the scientific uh you know just progress uh of all and so um what how can AI act as a leverage here um I think I'm going to mention like two different things first one is um you know when um actually let's think about this uh we highly uh encourage being a specialist especially
(25:16) in the scientific community. So uh there are small number of people who have a specialized knowledge and they're kind of segregated in different even uh you know locations and communities and and so on. So it's hard to um collaborate uh you might not even know what is available option for collaboration across different uh expertise areas.
(25:44) And so to me this uh if you think about this human knowledge a mental picture I have is a very sharp um and you know high highdimensional space and that's like here and there and just um there's so much uh space uh between them and I think AI is acting as a um kind of an envelope around this uh spiky uh space and connecting all these um you know the specialist knowledge.
(26:15) And if you're familiar with the um optimization, this is like the convex hole, this envelope around this um sharp uh you know corners here and there. And I think this is the um the the one of the roles of uh AI. So this when I was uh working on the deep research, this um wasn't really obvious, but as I work more and more and get more value out of it, this is kind of the mental picture I started building.
(26:42) And so um here I'm trying to get at this um human experts um very specialized but um their you know cooperation has communication physical separations all all these um um overhead and this um AI is kind of making that much much um more uh uh you know efficient and so um what I'm what I think is uh might be happening already is uh because of those um separations um and unable to cooperate in an efficient manner we might have a huge overhang of uh existing knowledge synthesis.
(27:20) So even with um you know just combining the existing knowledge we might be able to get a lot of value and maybe we can call that new knowledge and so those are the uh I think completely unchartered territory just because of how experts have uh grown and the communication bottlenecks of um u many many people and so these are I think the lowhanging fruits uh of AI you know acting as a leverage to advancing the science mission And but I don't think that's the that's enough.
(27:53) Uh we should probably go beyond and maybe just going forward uh we can expect advanced reasoning maybe even better than uh human scientist and then ability to generate new ideas and knowledge. Uh I think this is still rare and maybe I'm hearing some you know big um anecdotes that these are possible um like 03 helping some scientists generate new ideas brainstorming partner but I think it can go a lot more than that and so I would expect that this kind of abilities emerge in the future generations of the model if not already in there and once that happens this will
(28:34) be um you know just non non-stoping um research engine that have works all the time and then it can work together uh across humans and agents and so on. This will be the main um leverage factor going forward how AI can help this uh mission of the scientific progress.
(28:59) So um that's all I have today and I've talked about many different concepts but uh I think AI is just important thing everyone knows that but um I would invite you to think about is this um how big of a change am I um thinking about and uh is there a possibility that I might be underestimating uh that magnitude especially um now that you think about from a new form of leverage.
(29:22) Um, I would invite you think about this. Yeah, that's it. Thanks. Thanks so much. Um, uh, so your final point actually uh reminded me of something we talked about a couple weeks ago in the class called uh do you guys remember what it was called? uh we talked about the singularity and uh uh how AI intelligence uh will reach a level where uh they surpass human intel. Well, you know, obviously we're already at that point.
(30:00) Um do you think it relates to uh concepts such as that? Um, let me actually like uh pause the recording now that it's over and then we can um not not because I'm going to say something. Okay. Yeah. Yeah. Yeah. Um or sorry just sharing actually if you're pausing the also um I can show you what the class looks like. So maybe it's more interactive that way. Yeah, sure. Uh, let me pause it first.