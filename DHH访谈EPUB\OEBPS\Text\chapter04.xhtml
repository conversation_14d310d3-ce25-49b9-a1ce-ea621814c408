<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第四章：Ruby on Rails的诞生与理念</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第04章</div>
        <h1 class="chapter-title">第四章：Ruby on Rails的诞生与理念</h1>
    </div>
    
    <div class="chapter-intro">
        <p>Ruby on Rails不仅仅是一个Web开发框架，它是一种哲学的体现。本章深入探讨Rails的核心理念、设计原则，以及它如何改变了Web开发的面貌。从约定优于配置到不要重复自己，Rails的每一个设计决策都体现了DHH对编程美学和生产力的追求。</p>
    </div>

    <div class="conversation">
        <p><strong>Lex：</strong> 是的，我们能缩小并看看Rails的愿景，宣言，教条吗？是什么让编程语言框架变得伟大？特别是对于Web开发。所以我们谈论了快乐。Ruby的潜在目标。还有什么？</p>

        <p><strong>DHH：</strong> 所以你在看我认为在2012年写出的九个要点。首先，在我们深入它们之前，我想说我写下它的原因是如果你想要一个社区持续，你必须记录它的价值观，你必须记录它的实践。如果你不这样做，最终你会得到足够多的</p>

        <p>新人进来，他们对这个东西应该去哪里有自己的想法。如果我们没有指导灯帮助我们做决定，我们会开始挣扎。我们会开始实际分崩离析。我认为这是各种机构开始分崩离析的关键原因之一。我们忘记了为什么切斯特顿的围栏在那里。</p>

        <p>我们只是说，为什么那个围栏在那里？让我们把它拉出来。哦，它是为了把狼挡在外面。现在我们都死了，哎呀。所以我想写下这些东西。如果我们只是快速一个一个地看它们，你谈到了为程序员快乐优化。我把它放在第一位，向Matz致敬，这很大程度上是关于接受在编写美丽代码和我们想要从系统中得到的其他东西之间偶尔有权衡。</p>

        <p>可能有运行时权衡，可能有性能权衡，但我们无论如何都要这样做。我们也将以许多程序员默认不舒服的方式允许歧义。我实际上在这里给出的例子是在交互式Ruby shell中，你可以玩语言或甚至与你的领域模型交互。你可以以至少我发现的两种方式退出它。</p>

        <p>你可以写exit。砰，你退出程序。你可以写quit。砰，你退出程序。它们做同样的事情。我们只是写了exit和quit，或者构建那个的人写了exit和quit，因为他们知道人类可能选择其中一个或另一个。Python是这个的完美对比。</p>

        <p>在Python交互协议中，如果你写exit，它不会退出。它会给你一个教训。它基本上会告诉你阅读该死的手册。它说使用exit括号或Ctrl-D，即文件结束来退出。我想，一个非常人性化，另一个非常工程师。我以最好的可能方式意味着它们两个。Python是迂腐的。</p>

        <p>Python从一开始声明的价值是应该最好有一个且只有一个方式来做某件事。Ruby是完全相反的。不，我们想要适合不同人类大脑的完整表达，这样看起来语言正在猜测他们想要什么。</p>

        <p><strong>Lex：</strong> 其中一部分也描述了最少惊讶原则，这是一个难以工程到语言中的东西，因为你必须有点，这是一个主观的东西。</p>

        <p><strong>DHH：</strong> 这就是为什么你不能用一种方式做它，这就是为什么我使用exit和quit的例子。对一些人来说，最少惊讶原则会是，"哦，exit。那是我如何退出提示的。"对其他人来说，它会是quit。</p>

        <p>为什么我们不只是两个都做？</p>

        <p><strong>Lex：</strong> 好吧，那么约定优于配置是什么？那是一个大的。</p>

        <p><strong>DHH：</strong> 那是一个大的。那是一个巨大的。它诞生于我在早期对特别是Java框架的挫折，当你在当时为Java设置Web应用程序框架时，字面上写数百甚至数千行XML配置文件并不罕见。哦，我需要这个。</p>

        <p>我希望数据库使用外键作为post_id。不，不，不，我希望它作为post大写ID。哦，不，不，不，你必须做大写PID。有所有这些方式，你可以配置外关系键应该如何在数据库中工作，它们都不重要。我们只需要选择一个，然后那就好了。如果我们选择一个并且我们可以依赖它，它就成为约定。如果它是约定，我们不必配置它。</p>

        <p>如果我们不必配置它，你可以更快地开始你实际关心的事情。所以约定优于配置本质上是采取系统应该预组装的想法。我不只是给你一盒该死的乐高积木并要求你建造千年隼。我给你一个完成的玩具。</p>

        <p>你可以编辑，你可以改变它，它仍然是用乐高积木建造的。你仍然可以取下一些片段并放入一些其他片段，但我给你最终产品。这与大多数程序员喜欢的相反。他们喜欢一盒乐高积木。他们喜欢从头开始把一切放在一起。他们喜欢做所有这些详细的小决定，根本不重要。</p>

        <p>我想把那个提升起来，这样嘿，我不是试图从你那里拿走决定。我只是想让你专注于实际重要的决定，你真正关心的。没有人关心它是post_id还是post ID还是PID。</p>

        <p><strong>Lex：</strong> 是的，伟大的默认值。</p>

        <p><strong>DHH：</strong> 是的。</p>

        <p><strong>Lex：</strong> 这只是一个美妙的事情。你有所有这些愿望，他们要做某种定制的，最美丽的乐高城堡，没有人曾经从这些片段建造过。</p>

        <p>但实际上在大多数情况下要有生产力，你只需要建造基本的东西。然后在那之上是你的创造力来的地方。</p>

        <p><strong>DHH：</strong> 绝对地，我认为这是那些，教条的一部分，很多使用Ruby on Rails的程序员会勉强承认这是一个好事，即使他们不真正喜欢它。</p>

        <p>就像很难击败从头开始用乐高积木建造对程序员的吸引力。那只是我们喜欢的。这就是为什么我们首先是程序员，因为我们喜欢把这些小片段放在一起，但我们可以将那种本能导向堆栈的更有生产力的端。</p>

        <p><strong>Lex：</strong> 好吧，其他一些是什么？</p>

        <p><strong>DHH：</strong> 菜单是omakase。</p>

        <p>它实际上来自伟大的默认值真正重要的同一原则。如果你看现在JavaScript生态系统的所有错误，例如，它是没有人负责菜单。有十亿种不同的菜肴，你可以配置你的定制特定配置，但没有人做工作确保它们都适合在一起。</p>

        <p>所以你在JavaScript生态系统中有所有这些独特的问题，例如，可能有25种主要的只是做控制器层的方式。然后有多少种与数据库交谈的方式。所以你得到这个n乘n乘n的排列，没有人使用同样的东西。如果他们使用同样的东西，他们只使用同样的东西大约五分钟。所以我们没有保留的智慧。</p>

        <p>我们建立不了持久的技能。Rails走完全相反的方式说，你知道什么？Rails不只是一个白色框架。它是解决Web问题的完整尝试。它是解决你需要构建伟大Web应用程序的一切的完整尝试。</p>

        <p>那个拼图的每一片理想地应该在盒子里预配置，预组装。如果你想稍后改变其中一些片段，那很棒。但在第一天，你会得到一个由真正关心每一片成分的厨师设计的完整菜单，你会享受它。那又是那些许多程序员认为我知道更好的事情之一。</p>

        <p>他们在某种超本地意义上确实如此。每个程序员都知道更好。这就是Ruby建立的基础。每个程序员在他们的特定情况下都知道更好。也许他们可以做一些危险的事情。也许他们认为他们知道更好，然后他们炸掉他们的脚，然后他们真正会知道更好，因为他们已经炸掉他们的脚一次，不会再这样做。但omakase的菜单就是那个。</p>

        <p><strong>Lex：</strong> 所以你总的来说看到单体的价值？</p>

        <p><strong>DHH：</strong> 是的，集成系统。</p>

        <p><strong>Lex：</strong> 集成。</p>

        <p><strong>DHH：</strong> 有人想到整个问题。这是我自从术语被创造以来一直在对微服务进行十字军东征的原因之一。微服务诞生于本质上一个好想法。</p>

        <p>当你有数千名工程师在数百万行代码上工作时，你在Netflix规模上做什么？没有人可以一次在他们的头脑中保持整个系统。你必须把它分解。微服务可以是一个合理的方式来做那个。当你在Netflix规模时，当你将那个模式应用到20个程序员在50万行代码的代码库上工作的团队时，你是白痴。</p>

        <p>你只是不需要将方法调用变成网络调用。这是分布式编程的第一规则。不要分布你的编程。它使一切更难。作为程序员你必须考虑的所有失败条件当涉及网络电缆时变得无限更难。</p>

        <p>所以我讨厌过早分解的想法，微服务正是那个。单体说。让我们尝试专注于构建单个人类实际可以理解的整个系统，并通过压缩所有概念将那个范式推到尽可能远，这样更多的它将适合单个操作人类的内存。然后我们可以有一个系统，我实际上可以理解所有Basecamp。我实际上可以理解所有HEY。</p>

        <p>这两个系统都刚好超过十万行代码。我见过人们做这个也许两倍，也许三倍那个规模，然后它开始分解。一旦你到达肯定超过50万行代码的北方，没有个人人类可以做它，那时你进入也许某种程度的微服务可以有意义。</p>

        <p><strong>Lex：</strong> Basecamp和HEY都是十万？</p>

        <p><strong>DHH：</strong> 大约十万行代码。</p>

        <p><strong>Lex：</strong> 哇，它很小。</p>

        <p><strong>DHH：</strong> 是的。考虑到Basecamp我认为有大约420个屏幕，不同的方式和配置的事实。</p>

        <p><strong>Lex：</strong> 啊，你包括前端吗？</p>

        <p><strong>DHH：</strong> 不，那是Ruby代码。好吧，它是前端，在某种意义上，一些Ruby代码。</p>

        <p>它对前端有益，但它不是JavaScript，例如。现在我们可能稍后谈论的另一件事是我们实际上为我们所有的应用程序写很少的JavaScript。HEY，这是Gmail竞争对手。Gmail我认为发布28兆字节的未压缩JavaScript。如果你压缩它，我认为大约是6兆字节，28兆字节。想想那是多少行代码。</p>

        <p>当HEY启动时，我们发布了40千字节。它试图解决同样的问题。如果你做不同的事情，你可以用28兆字节的未压缩JavaScript或40千字节解决电子邮件客户端问题。但那通过本质上同样的问题来。这就是为什么我激烈地战斗分离前端和后端。</p>

        <p>在我看来，这是对Web开发的伟大犯罪之一。我们仍然在为此赎罪。我们分离和分割了什么是和应该是统一的问题解决机制。当你在前端和后端上工作时，你理解整个系统。</p>

        <p>你不会进入这些分解的阵营，最终你最终得到像GraphQL这样的东西。</p>

        <p><strong>Lex：</strong> 好吧，让我们快速浏览教条的其余部分。没有一个范式。</p>

        <p><strong>DHH：</strong> 没有一个范式涉及Ruby在其核心是一个激烈的面向对象编程语言的事实，但它也是一个函数式编程语言。</p>

        <p>我告诉你的这个五次，你基本上可以做这些匿名函数调用，你可以将它们链接在一起，非常符合真正的函数式编程语言如何工作的精神。Ruby甚至通过使字符串不可变而更接近函数式编程的规模。有来自所有不同学科和软件开发的所有不同范式的想法可以适合在一起。</p>

        <p>例如，Smalltalk。只有面向对象，就是这样。Ruby试图主要是面向对象，但借用一点函数式编程，一点命令式编程，能够做所有那些。Rails试图做同样的事情。我们不只是要选择一个范式并通过一切运行它。</p>

        <p>面向对象在它的中心，但邀请所有这些其他学科是可以的，被启发是可以的。混音是可以的。我实际上认为Rails的主要好处之一是它是一个混音。我没有发明所有这些想法。我没有想出active record。</p>

        <p>我没有想出分割应用程序的NBC方式。我采取了我从每个不同阵营学到和拾起的所有伟大想法，我把它们放在一起。不是因为会有一个单一的总体一切理论，而是我会有一个连贯的单元，结合了来自各处的最好的。</p>

        <p><strong>Lex：</strong> 那个想法与单体系统的美丽有点紧张吗？</p>

        <p><strong>DHH：</strong> 我认为单体可以被认为是相当宽敞的，相当像一个大帐篷，单体实际上需要借用一点函数式编程来解决那种学科擅长的问题类型，那种范式擅长解决。如果你也想要面向对象在其核心。</p>

        <p>我实际上认为当我看函数式编程语言时，有很多可爱的。然后我看到当他们正在解决的问题的一部分要求改变某些东西时，他们必须经历的一些疯狂扭曲。你说，神圣的狗屎，这是90%问题的伟大范式。然后当你试图解决最后10%时，你完全扭曲自己。</p>

        <p><strong>Lex：</strong> 哦，崇尚美丽代码是下一个。</p>

        <p><strong>DHH：</strong> 我们已经详细谈论了那个，这里有一个真正总结Ruby on Rails的领域特定语言质量的伟大例子，你可以让代码实际上愉快地写和读。这对我来说真的很有趣，因为正如我们谈论的，当我开始学习编程时，它甚至不是一个考虑。我甚至不知道那可能是前提的一部分，那可能是解决方案的一部分。写代码可以感觉像写诗一样好。</p>

        <p><strong>Lex：</strong> class Project, ApplicationRecord belongs_to :account has many participants. Class_name, Person, validates_presence_of :name.</p>

        <p><strong>DHH：</strong> 看，你可以读出来。你甚至没有改变。</p>

        <p><strong>Lex：</strong> 任何像俳句或什么的东西。</p>

        <p><strong>DHH：</strong> 对吧？那不美丽吗？</p>

        <p><strong>Lex：</strong> 是的，它很好。它真的很好。它有直觉的性质。好吧，所以我有具体的问题。</p>

        <p>我的意思是ActiveRecord，只是采取那个切线。那必须是你最喜欢的功能。</p>

        <p><strong>DHH：</strong> 它是Rails的皇冠宝石。它真的是。它是如何与Ruby on Rails工作的定义特征。它诞生于有趣的争议水平，因为它实际上使用了马丁·福勒在"企业应用架构模式"中描述的模式。</p>

        <p>任何在业务系统上工作的人的最伟大书籍之一。如果你没有读过它，你必须立即拿起它。"企业应用架构模式"，我认为它在2001年出版。它是我多次阅读的极少数编程书籍之一。它令人难以置信。</p>

        <p>在其中，马丁描述了一堆如何构建业务系统的不同模式。ActiveRecord在那里有点像脚注。模式字面上叫做active record。你可以查找它。</p>

        <p><strong>Lex：</strong> 好的。</p>

        <p><strong>DHH：</strong> 它叫做active record。我甚至没有足够创造性来想出我自己的名字。但它允许创建，数据库和面向对象的结合，以很多程序员发现有点令人反感的方式。</p>

        <p>他们实际上不想用SQL污染那种编程的美丽面向对象性质。前几天Uncle Bob有一个关于SQL是有史以来最糟糕的东西的咆哮。巴巴，好吧，好的，无论如何，我不在乎。这是实用的。我们正在制作CRUD应用程序。</p>

        <p>你从HTML表单中取东西，你把它们粘到该死的数据库中。它不比那更复杂。你在光谱的那两端之间放的抽象越多，你只是在愚弄自己。这就是我们正在做的。我们正在与SQL数据库交谈。</p>

        <p>顺便说一下，快速旁白，SQL是那些经受了NoSQL数据库结构化列表数据十年的冲击并仍然占主导地位的东西之一。SQL是投资你的时间学习的好东西。每个与Web工作的程序都应该在相当程度上知道SQL。即使他们与ORM，光学关系映射或active record一起工作。你仍然需要理解SQL。</p>

        <p>Active record做的不是那么多试图在不同类型的范式后面抽象SQL。它只是让它写起来不那么麻烦。让它更适合在其他领域模型之上构建领域模型，以你不必手写每个该死的SQL语句的方式。</p>

        <p><strong>Lex：</strong> 我们只是说active record是ORM，这是一个层，使与数据库通信直观和人类可解释。</p>

        <p><strong>DHH：</strong> 甚至比那更简单。它将表转换为类，将行转换为对象。我实际上认为SQL大部分很容易理解。你也可以写一些SQL高尔夫。那很难理解。但SQL在其基础上，对SQL的很多批评是它是为人类消费而写的。</p>

        <p>它实际上相当冗长，特别是如果你一遍又一遍地做插入之类的事情。它相当冗长插入到表括号，枚举你想插入的每一列，值，括号，与那列匹配的每个值。手写SQL变得乏味，但它实际上非常人类可读。</p>

        <p>Active record只是把那个乏味拿走。它使得以人类可描述语言不能的方式组合事物成为可能。它将事物组合成方法，你可以组合这些方法，你可以围绕它们构建结构。所以我不讨厌SQL，我讨厌编程中的很多事情。我试图摆脱它们。SQL真的不是其中之一。</p>

        <p>它只是一种感觉，我不想一遍又一遍地写同样的东西。它是一个，我们能稍微简洁一点吗？我们能稍微更好地匹配光学方向，而不试图隐藏我们将这些对象持久化到数据库的事实。那是我认为很多ORM出错的地方。他们试图生活在对象的纯世界中。</p>

        <p>从不考虑那些对象必须一致到SQL数据库，然后他们想出了来回翻译的复杂方式。Active record说，你知道什么？只是接受它。这个记录，这个对象不会被保存到某个NoSQL数据库。它不会被保存。它会被保存到SQL数据库。所以只是围绕那个构建整个事情。</p>

        <p>它会有属性。那些属性会响应数据库中的列。它不比那个东西让它如此更复杂。</p>

        <p><strong>Lex：</strong> 是的，但我应该说，所以我个人喜欢SQL，因为我是算法人，所以我喜欢优化。</p>

        <p>我喜欢知道数据库实际如何工作，所以我可以匹配SQL查询和表的设计，这样有，你知道，最优的，从表中挤出最优性能。好吧，基于那个表实际使用的方式。所以我的意思是，我认为那推到这样的点，就像学习理解SQL有价值。我想知道，因为我开始看active record，它看起来真的很棒。</p>

        <p>那会让你懒惰吗？不是你，而是一个滚进来并开始使用Rails的人，你可能可以逃脱从不真正学习SQL，对吧？</p>

        <p><strong>DHH：</strong> 只要你想停留在能力的入门级。这实际上是我与Rails的总体使命，将进入门槛降低到如此之低，以至于某人可以开始在他们的浏览器上看到东西，而基本上不理解任何东西。</p>

        <p>是的，他们可以运行Rail的新博客，运行几个生成器，他们有整个系统，他们不理解任何东西。但这是学习更多的邀请。我被激怒的地方，这与AI讨论联系回来，是当那被变成这个模因，程序员不再必须有能力。我的意思是AI会弄清楚它。生成器会弄清楚它。</p>

        <p>我不需要知道SQL，active record会从我这里抽象它。不，不，不，伙计，等等。这里的路径是能力。我试图教你事情。我理解我不能在五分钟内教你一切。没有人曾经在任何值得的事情上变得好，可以在五分钟内被教一切。</p>

        <p>如果你想成为一个完全全面的Web应用程序开发者，那需要年。但你实际上可以在几天内变得有些生产力。你肯定可以在几天内有乐趣。你会在几分钟和几小时内有乐趣。随着时间的推移，我可以教你更多一点。Active record说，是的，是的。</p>

        <p>好吧，从这里开始，然后下周我们会做SQL课。</p>

        <p><strong>Lex：</strong> 实际上你有这个我喜欢的美丽表达，一个伟大的编程语言。像Ruby有一个软坡道，坡道到无穷大。</p>

        <p><strong>DHH：</strong> 那完全正确。</p>

        <p><strong>Lex：</strong> 所以是的。它超级可访问，超级容易开始。</p>

        <p><strong>DHH：</strong> 它从不停止。</p>

        <p>总是有更多要学习的。这是我仍然有乐趣编程的原因之一。我仍然在学习新事物。我仍然可以结合新事物。Web作为领域足够深。你永远不会学会所有它。</p>

        <p><strong>Lex：</strong> 提供锋利的刀。</p>

        <p><strong>DHH：</strong> 这是一个好的，因为说这个的另一种方式，说这个的相反方式，Java方式的说法是不要提供脚枪，对吧？我不想给你锋利的刀。你是孩子。你不能处理锋利的刀。这里是钝黄油刀。切你该死的牛排，对吧？那是一个非常令人沮丧的体验。你想要锋利的刀，即使你可能能够割伤自己。我以Matz信任人类的同样方式信任人类。</p>

        <p>也许你切掉一个手指。好吧，你不会再这样做。谢天谢地，那是一个虚拟的思考手指。它会重新长出来。你的能力会增长。用锋利的工具工作更有趣。</p>

        <p><strong>Lex：</strong> 那实际上有助于到无穷大的坡道。</p>

        <p><strong>DHH：</strong> 是的，对学习</p>

        <p><strong>Lex：</strong> 重视集成系统。</p>

        <p><strong>DHH：</strong> 我们有点触及了那个。</p>

        <p>这是Rails试图解决Web的整个问题，不只是一个小组件。它不是留给你一堆片段。你必须自己把它们放在一起。</p>

        <p><strong>Lex：</strong> 进步胜过稳定。</p>

        <p><strong>DHH：</strong> 你知道什么？如果有一个过时的，可能是那个。</p>

        <p>在这个阶段，Rails在许多，许多代中一直非常稳定。最后一个主要版本Rails 8对任何运行Rail 7的人基本上是无升级。Rail 7对任何运行Rail 6的人几乎是无升级。我曾经认为需要更多流失来获得进步，以保持在新东西的前沿。我在经历2010年代JavaScript社区的屈辱之前写了这个。</p>

        <p>在那里，似乎稳定性不仅不被重视，它实际上被鄙视，流失本身是我们应该追求的价值。如果你三个月后仍然与同一个框架工作，你是白痴。我看到那个，我实际上退缩了。</p>

        <p>如果我今天要写教条，我会不同地写那个。我不会说进步胜过稳定。</p>

        <p><strong>Lex：</strong> 好吧，也许它是编程语言年龄的函数。</p>

        <p><strong>DHH：</strong> 也许，或者对问题的更深理解。我认为技术如此迷人的部分是我们有这种感知，一切不断移动如此快。</p>

        <p>不，它不是。一切以冰川速度移动。偶尔有范式转换，就像现在AI正在发生的。就像2007年iPhone引入时发生的，就像95年互联网发生的。那基本上是我职业生涯的总和。三件事改变了。</p>

        <p>其间的其他一切都是增量小改进。你可以识别2003年写的Rails应用程序。我知道，因为我当时写的Basecamp仍然在运行，在ARR中赚数百万美元，为客户服务，在当时启动的初始版本上。如果我稍微眯眼，它看起来像我今天会写的Rails代码。所以大多数事情甚至在计算中都不改变。</p>

        <p>那实际上是好事。我们看到JavaScript生态系统。当每个人对持续流失变得疯狂时会发生什么？事情不经常改变。</p>

        <p><strong>Lex：</strong> 顺便说一下，在那个小切线上。你只是有点可见地，口头地改变了你15年前的想法。</p>

        <p><strong>DHH：</strong> 是的。</p>

        <p><strong>Lex：</strong> 那很有趣。</p>

        <p>你注意到自己多年来相当多地改变想法吗？</p>

        <p><strong>DHH：</strong> 我会说，哦，是的。然后也，哦，不，在这种意义上，绝对有关于人性，关于机构，关于编程，关于业务的基本事情，我改变了想法。然后我也有几乎更有趣的经历，我认为我改变了想法，我尝试了新方式，意识到为什么我首先有原始意见，然后回到它。所以它两种方式都发生。后一部分的例子，例如，</p>

        <p>是37signals的经理。很长时间，我会对工程经理作为小型甚至中型公司的不必要负担进行咆哮。在某个时候，我实际上开始有点怀疑自己。我开始想，你知道什么？也许所有程序员确实需要每周与他们的工程经理进行一对一治疗会话，以成为完整的个人。</p>

        <p>所以我们尝试了几年，我们雇佣了一些非常好的工程经理，他们以你应该做的方式做工程管理，以到处都做的方式。在那之后，我想，不，不，我是对的。这是正确的。我们不应该有经理。不是每个程序员都需要每周与工程经理进行治疗会话。我们不需要这些和最少的预定聚会。</p>

        <p>我们不需要所有这些会议。我们只需要让人们该死的独自工作他们享受的问题，长时间不间断的时间。那是找到快乐的地方。那是找到生产力的地方。如果你能逃脱它，你绝对应该。</p>

        <p>工程管理是当那个分解时的必要邪恶。</p>

        <p><strong>Lex：</strong> 那么经理的案例是什么？</p>

        <p><strong>DHH：</strong> 经理的案例是如果你确实有很多人。有一堆工作有点只是出现。一对一是程序员需要某人检查的一个例子。有另一个理想化版本，某人需要指导初级的职业，例如，给他们重定向反馈和所有这些其他东西。</p>

        <p>这不是在抽象中，我不同意其中一些事情，但在实践中，我发现他们经常创造比他们解决的更多问题。这里的一个好例子是你能从不比你更好你的工作的人那里得到反馈吗？你得到一些反馈。你可以得到关于你如何在工作中出现的反馈。</p>

        <p>你对其他人有礼貌吗？你是好沟通者吗？好吧，是的。但你不能得到关于你的工作的反馈，那更重要。如果你希望在职业中进步，你在比你更好你的工作的人下面和与他们一起工作更重要。我曾经与之工作的每个程序员都对在那个指标上在他们的职业中进步，在他们的手艺上变得更好比他们在拾起中层经理可以教他们的指针更感兴趣。</p>

        <p>那不是说它没有价值。那不是说成为更好的人或更好的沟通者没有价值。当然有所有那些事情。但如果我必须选择其中一个或另一个，我更高地重视能力。就像那又是，我警告这个一百万次，因为我知道人们有时听到什么。</p>

        <p>他们听到天才混蛋就很好，那很棒，如果某人只是真的擅长他们做的事情，你应该原谅各种恶意行为。我根本不是说那个。我说的是能力的历史是从比你更好的人学习的历史，那种关系应该优先于所有其他，当引入工程经理时，那种关系被稍微放在一边。</p>

        <p>现在有趣的是这个对话与我们之前谈论的事情联系回来。大多数工程经理实际上是前程序员。他们至少在某种程度上没有程序，但我一次又一次看到的是他们失去他们的触觉，他们对它的感觉非常，非常快，变成尖头发老板非常，非常快，他们真的擅长检查更新。</p>

        <p>只是看我们在项目A上在哪里，如果你需要任何东西或我们准备交付。好吧，是的。也，不。闭嘴，让我该死的独自。让我编程，然后我会出现错误。我会与其他程序员交谈，我可以与他们切磋，我们可以学到一些东西。我可以与他们翻转问题，我们可以前进。如果你回顾计算机行业的历史，所有发生的伟大创新，它都是由没有工程经理的小团队完成的。只是充满高技能个人。</p>

        <p>你有约翰·卡马克在这里。我曾经如此仰望它的软件。不仅因为我喜欢快速，不仅因为我喜欢他们正在做的事情，而且因为他分享了一些关于公司如何工作的。没有经理，或者也许他们有一个商业人员做一些商业东西，但那只是为了得到报酬。</p>

        <p>其他一切基本上只是设计师和程序员。他们大约有八个，他们创造了该死的"Quake II"。所以你为什么又需要所有这些人？你为什么又需要所有这些经理？我认为，再次，在某个规模上它确实分解。很难只是有十万程序员四处狂奔，没有任何产品妈妈或爸爸告诉他们做什么。</p>

        <p>我理解那个。然后即使我说那个，我也不理解它。是的，因为如果你看像Gmail这样的东西，例如，有像Buchheit在当时谷歌的副项目。甚至所有这些巨大公司的如此多的持久长期价值是由没有经理的人创造的。</p>

        <p>那不是意外，那是直接因果关系。所以我在某种程度上多年来对这种管理概念变得更加激进，至少对我自己和知道我是谁以及我想如何工作。因为这个的另一部分是我不想成为经理，也许这只是我投射我是内向的人，不喜欢每周与人们进行一对一通话的事实。</p>

        <p>但它也概括了我如何能够推进我的职业。我直到我有一扇我可以关闭的门，没有人可以打扰我连续六小时，我才真正与Ruby或其他方面达到下一个水平。</p>

        <p><strong>Lex：</strong> 所以在公司中，可能原因之一是雇佣经理非常容易，经理也从你那里委托责任。</p>

        <p>所以如果你只是有一堆程序员四处跑，你有点响应，就像它是工作，它是智力工作，必须处理正在进行的每个问题的第一原则。所以经理就像你可以放松，哦，我会被照顾。但他们然后雇佣他们自己的经理，它只是乘以乘以乘以。</p>

        <p>我希望如果一些伟大的公司在美国会有，如果有像一个额外的侧分支，我们总是可以运行，也许物理学家可以想出如何分割模拟，所有经理都被移除。也只是在那个分支中，只是PR和通信人员也。</p>

        <p>甚至律师，只是工程师，让我们看看，然后我们合并它回来。</p>

        <p><strong>DHH：</strong> 我基本上在37signals运行那个分支20年。我已经实验了分叉回到另一边。我已经实验了在员工上有全职律师。我已经实验了有工程经理。</p>

        <p>我可以告诉你，当那些个人或那些角色都没有时，50、60人的生活要好得多。这从来不是关于个人。这是关于角色。那些角色都不在你的组织全职。偶尔你需要经理。偶尔你需要律师。我可以偶尔扮演经理的角色，好的。然后我可以将它设置回零。这几乎像云服务。</p>

        <p>我想要一个经理服务，我可以这周调用七小时，然后我想在接下来的三个月将它降到零。</p>

        <p><strong>Lex：</strong> 是的。我读。我不知道这是否仍然是情况，Basecamp是LLC，没有CFO，像全职会计师。那是升级吗？</p>

        <p><strong>DHH：</strong> 所以最有趣的。这些天，我们确实有财务主管。我认为我们前19年的生活没有。</p>

        <p>我们基本上只是逃脱了会计师做我们的账簿，以你会做小冰淇淋店的同样方式，除了我们随着时间的推移会做数亿美元的收入。规模似乎古怪。在某个时候，你也可以爱上你自己的古怪到实际上不健康的程度。</p>

        <p>我肯定随着时间的推移这样做了，我们应该有某人安装或更勤奋地数豆子，更早一点。这是只是疯狂盈利和销售基本上可以有无限边际的软件的祝福的一部分，你有点可以逃脱一堆你也许不应该的东西。</p>

        <p>部分教我这个教训的是当我们意识到我们没有在我们有Nexus的不同美国州收集销售税。我们花了大约两年和500万美元的和解和清理才摆脱那个混乱。在那之后，我说，好吧，好的，我们可以雇佣财务人员。</p>

        <p>我们现在有一个美妙的财务人员，Ron，他实际上最终替换了其他东西。我们曾经有全职数据分析人员，他会做各种洞察挖掘，为什么人们注册这个东西。我们运行了10年，意识到，你知道什么？如果我可以有数据分析人员或会计师，我选择会计师。</p>

        <p><strong>Lex：</strong> 我在如此多的水平上喜欢这个。</p>

        <p>我们能停留在你给出的建议，小团队更好吗？我认为那真的更少。更少是更多。你之前说什么？更糟糕是更好。好吧，对不起。</p>

        <p><strong>DHH：</strong> 更糟糕更好在技术采用上很多时候。我认为实际上来自同样的事情。它来自许多伟大突破不仅仅是由小团队而是由个人创造的事实。</p>

        <p>个人写一些东西。个人在某个参数上写一些东西，他们做的是更糟糕的。当然，当一个人必须制作一个巨大公司有数百甚至数千开发者可以在那个问题上工作时，它更糟糕。</p>

        <p>但在如此多其他参数中，那种更糟糕是价值，那种更少是价值。在我们2006年写的Getting Real中。我们谈论这种更少软件的概念。当我们2004年开始Basecamp时，人们会一直问我们，你们不害怕微软吗？他们有如此多更多资源。他们有如此多更多程序员。</p>

        <p>如果他们喜欢你这里的小利基，他们出现，他们只是向问题投掷一千程序员怎么办？我的答案也许部分因为我像24岁是首先，不，世界上没有关心。但真正的答案是他们不会产生同样的东西。你不能用一千人的团队产生Basecamp是的那种软件。你会构建一千人构建的那种软件。</p>

        <p>那根本不是同样的事情。如此多的主要突破在最终用户系统中，也在开源系统和基本系统中，他们由个人或非常小的团队完成。甚至所有这些苹果的经典历史总是像，好吧，它是大组织，但然后你有实际上在突破上工作的团队。它是四个人。它是八个人。</p>

        <p>它从来不是200。</p>

        <p><strong>Lex：</strong> 大团队似乎减慢事情。</p>

        <p><strong>DHH：</strong> 是的。</p>

        <p><strong>Lex：</strong> 这如此迷人，部分是经理事情。</p>

        <p><strong>DHH：</strong> 因为人类不扩展。人类之间的沟通肯定不扩展。你基本上每次添加新节点时得到网络成本效应，它指数上升。</p>

        <p>这也许是为什么我变得如此喜欢在Basecamp没有经理的关键事情，因为我们的默认团队大小是二。一个程序员，一个设计师，一个功能。当你在那种规模水平上操作时，你不需要复杂性。你不需要高级方法论。你不需要多层管理，因为你可以只是做。小团队的魔力是他们只是做。</p>

        <p>他们不必争论，因为我们不必设定方向，我们不必担心路线图。我们可以只是坐下来制作一些东西，然后看它是否好。当你可以逃脱只是制作东西时，你不必计划。</p>

        <p>如果你可以摆脱计划，你可以跟随从代码，从产品，从你在那一刻工作的东西中出现的真理。当你落后一步时，你对伟大的下一步是什么知道得更多，而不是如果你试图提前18个月映射所有步骤。我们如何从这里到非常远的地方？你知道什么？那很难提前想象，因为人类在那方面非常糟糕。</p>

        <p>也许AI有一天会比我们好得多，但人类可以迈一步或把一只脚放在另一只前面。那不那么难，那允许你摆脱所有那种复杂性。所以过程变得简单得多。你需要更少的人，它复合。你需要更少的过程。</p>

        <p>你需要在会议中浪费更少时间。你可以只是度过这些长时间光荣的日子和周，不间断的时间解决你关心的真正问题，那些有价值的，你会发现那是市场实际想要的。没有人因为背后有巨大公司而购买某些东西。大多数时候。他们购买某些东西因为它好。</p>

        <p>你得到好东西的方式是你不坐着开会。你尝试东西。你构建东西。</p>

        <p><strong>Lex：</strong> 真的是有点令人难以置信，一个人，诚实地一个人可以在100小时的深度工作，专注工作中做什么，甚至更少。</p>

        <p><strong>DHH：</strong> 所以我会告诉你这个，我准确地跟踪了我在Basecamp第一版上花费的小时数。</p>

        <p>我这样做是因为当时我在为杰森做合同工作，他付我钱，我要说每小时15美元。那是我们刚开始时我得到的报酬。我认为他已经将我的工资提高到光荣的25美元。但我在向他开账单，我知道Basecamp第一版的发票是400小时。那是2004年一个唯一个人创建整个系统所需要的，然后继续总收入数亿美元并继续做得非常好。</p>

        <p>一个人只是我设置一切。那个故事的一部分是Ruby。那个故事的一部分是Rails。但很多也只是我加杰森，加瑞安，加马特。那是当时整个公司。我们可以用如此小的团队创造持续价值的东西，因为我们是小团队。不是尽管如此。小不是垫脚石。</p>

        <p>这是人们进入他们头脑的另一件事。这是"重新工作"的大主题之一。它给企业家许可拥抱成为小团队。不是作为路点，不是像我试图成为一千人。不，我实际上喜欢成为小团队。小团队更有趣。</p>

        <p>如果你问几乎任何人，我确信Tobi也会这样说，即使在他的规模上，构建某些东西的纯粹享受是与小团队一起构建它的享受。现在当你有巨大公司时，你可以在不同规模上产生影响。我完全认识到那个，我看到它的吸引力。</p>

        <p>但在实际构建事物中，总是小团队，总是。</p>

        <p><strong>Lex：</strong> 你如何保护小团队。Basecamp成功地保持小。一直是龙，你必须击退。那就像基本上你赚很多钱。有增长的诱惑。所以你如何不增长？</p>

        <p><strong>DHH：</strong> 不要接受风险投资。</p>

        <p><strong>Lex：</strong> 好吧，那是第一步。</p>

        <p><strong>DHH：</strong> 那是第一点。</p>

        <p><strong>Lex：</strong> 首先，</p>

        <p><strong>DHH：</strong> 第二点每个人都接受风险投资。</p>

        <p><strong>Lex：</strong> 所以你已经去了。</p>

        <p><strong>DHH：</strong> 我的意思是那一直是最长时间的答案。因为问题不只是风险投资，它是其他人的钱。一旦你接受其他人的钱，完全可以理解，他们想要回报，他们更愿意有尽可能大的回报。因为不是他们坐在代码中。</p>

        <p>不是他们从构建某些东西中得到日常满足。从编辑器中凿出美丽的代码诗，对吧？他们不得到那种满足。他们得到满足也许从看到一些好东西放入世界，那是公平的。但他们肯定也从更高回报中得到满足。</p>

        <p>肯定在风险投资中有这种感觉，在风险投资中声明，你接受钱的整个点是达到十亿美元或更多。现在那个的路径通常确实通过运行既定的剧本，然后当涉及软件时，企业销售剧本是那个剧本。</p>

        <p>如果你正在做B2B软件SaaS，你会试图找到产品市场契合。你有它的第二个，你会放弃你的小型和中型账户，用巨大的销售力量追逐大鲸鱼，到那时你是一千人，生活糟糕。</p>

        <p><strong>Lex：</strong> 话虽如此，我的意思是人们只是对此好奇。我有机会了解杰夫·贝佐斯。</p>

        <p>他投资了Basecamp，不是控制？</p>

        <p><strong>DHH：</strong> 他买了二级市场。所以这是有趣的事情，当投资有这两个双重含义。通常当人们想到投资时，他们认为你投入增长资本，因为你希望业务雇佣更多人做更多研发，这样他们可以增长更大。贝佐斯实际上没有这样做。</p>

        <p>他直接从杰森和我那里购买了所有权股份，那次购买的100%收益进入了我和杰森的银行账户，个人银行账户。没有一分钱进入公司账户，因为我们不需要钱来增长。我们需要的或我们肯定享受的是在某种程度上也许信任投票，但更多的是从桌子上拿一点的安全性，是我们敢于拒绝风险投资的大钱。它本质上是对想要从想要将公司带到我们不想去的巨大的人那里接受更大支票的疫苗</p>

        <p>从那些然后想要将公司带到我们不想去的巨大的人。所以杰夫给了杰森和我足够的钱，我们舒适地拒绝所有这些人，以这样的方式，如果它在六个月后翻肚皮，我们不会踢自己并说，我们有值得数百万的东西，现在我们什么都没有，我必须再次担心租金和杂货。</p>

        <p><strong>Lex：</strong> 这是信任投票。</p>

        <p>我想知道从，我很想听到杰夫的故事的一面，就像为什么，因为他不需要钱。所以它真的，我认为它可能只是相信人们并想要在世界上创造酷东西并从中赚钱，但不像。</p>

        <p><strong>DHH：</strong> 100%杰夫的动机不是回报，因为他实际上有一个团队。他的私人办公室运行这些投资，他们对我们给他的投资推介做了计算，这如此荒谬，以至于杰森和我在写下我们的指标时笑得屁股都掉了。我想，没有人会付这个。没有人会给我们这个收入金额的倍数。那很好。</p>

        <p>我的意思是我们接受电话本质上出于对杰夫·贝佐斯甚至想看我们的敬畏，就像，你知道什么？我们不想要风险投资。我们不需要其他人的钱，但就像让我们给他一个没有理智的人实际上会说是的胡说八道数字。然后我的意思是，我们可以各自走自己的路。他的投资团队说，"杰夫，没门。</p>

        <p>这在经济上根本没有意义。他们要求太多钱，收入太少。"杰夫只是说，"我不在乎。我想投资这个家伙。"因为对他当时来说，这是零钱，对吧？就像杰森和我每人得到几百万美元。</p>

        <p>我的意思是无论那天日元和美元之间的货币波动可能为他的净值移动了我们投资的10倍。杰夫似乎真正有兴趣在有趣的人，有趣的公司周围，帮助某人走距离。我实际上回顾那种关系有一些遗憾，因为我以我有点羞耻的方式理所当然地接受了那种信任投票，多年来我对亚马逊做的一些事情更加批评，我现在觉得是有道理的。所以那只是处理它的一部分。</p>

        <p>但在经济意义上，他给了我们那种信心。他给了我们经济信心，但然后他也给了我们也许当时运行美国最重要互联网业务的CEO的信心。出现在我们的电话中，我们会与他有，就像一年一次，基本上只是说，是的，你们正在做很棒的东西。你们应该继续做很棒的东西。我读了你们的书，它很棒。</p>

        <p>你们启动了这个东西，它很棒。你们应该做更多那个。我实际上不知道如何运行你们的业务。你们知道它。</p>

        <p><strong>Lex：</strong> 所以书出去了。我只是如此，从粉丝角度，我对杰夫·贝佐斯如何能够看到好奇，因为对我来说，你和杰森，就像特别是技术空间的人类。</p>

        <p>杰夫能够看到那个的事实，对吧？看到那个有多难？</p>

        <p><strong>DHH：</strong> 他肯定很早就看到了它。我认为这是杰夫比几乎任何其他人都做得更好的事情。他如此提前发现那个机会，在任何其他人甚至睁开眼睛看它之前，或者肯定他愿意如此早期和如此努力地押注它，比任何其他人都是。</p>

        <p>他只是一次又一次地正确。我的意思是，我们不是他做的唯一投资。肯定亚马逊有极其长期的愿景。比我曾经有勇气保持的要长得多。就像我认为自己是长期思考者。与杰夫正在玩的游戏相比，我在玩孩子的游戏。</p>

        <p>就像当我看亚马逊在网络繁荣和萧条周围的经济学时，它们看起来荒谬。就像他们失去如此多钱。他们如此被市场讨厌，他们...没有人相信它会变成现在的样子，但杰夫做了。以那种我真正渴望的信念水平。我认为那是我从那种关系中带走的主要事情之一，你可以相信自己到那种程度，对抗那些几率。那是荒谬的。</p>

        <p>他在如此多倍我们的水平上做了那个，如果我怀疑自己，那是可悲的。</p>

        <p><strong>Lex：</strong> 是的，我认为亚马逊是那些公司之一。我的意思是，它多年来受到一堆批评。这是关于人类的事情，我们不那么欣赏，我们理所当然地接受一个东西带来的积极，真的很快，然后我们只是开始批评那个东西。这是飞机上的Wi-Fi。</p>

        <p><strong>DHH：</strong> 那正是它。</p>

        <p><strong>Lex：</strong> 但我认为亚马逊，可以说亚马逊是过去一百年最伟大的公司之一</p>

        <p><strong>DHH：</strong> 当然，我认为这是一个容易的案例。我也认为的是，成为过去一百年最伟大公司之一你付出的代价是很多诋毁者，很多推回，很多批评，这实际上是宇宙中恢复的秩序。</p>

        <p>我在互联网上一直以来最喜欢的老师之一是凯西·塞拉。我不知道你是否知道她的工作，但她实际上在残酷的互联网赶走她之前的几年是Vern。但她写了一个叫做创造热情用户的博客，她在我的大脑中刻下了这个宇宙平衡的概念。</p>

        <p>如果你正在创造很多人喜欢的有价值的东西，你必须创造一个相等和相反的仇恨者力量。你不能有喜欢你做的事情的人而不也有讨厌你做的事情的人。从那个逃脱的唯一方法是平庸。如果你如此无聊和如此无趣，以至于没有人在乎你是否存在，是的，你不得到仇恨者，但你也不得到真正享受你的工作的人的影响。</p>

        <p>我认为亚马逊只是在大规模上，对吧？他们为技术为商业带来如此多价值和变化，他们必须简单地有黑洞大小的仇恨者，否则宇宙简单地会翻倒。</p>
    </div>
</body>
</html>