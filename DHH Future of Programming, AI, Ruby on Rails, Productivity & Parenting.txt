DHH: Future of Programming, AI, Ruby on Rails, Productivity & Parenting | <PERSON> Podcast #474 - YouTube
https://www.youtube.com/watch?v=vagyIcmIGOQ

Transcript:
(00:00) - No one anywhere who's serious believes that cookie banners does anything good for anyone, yet we've been unable to get rid of it. This is the thing that really gets me about cookie banners too. It's not just the EU. It's the entire world. You can't hide from cookie banners anywhere on this planet.
(00:20) If you go to goddam Mars on one of Elon's rockets and you try to access a webpage, you'll still see a cookie banner. No one in the universe is safe from this nonsense. It sometimes feels like we're barely better off. Like webpages aren't that different from what they were in the late '90s, early 2000s. They're still just forms. They still just right to databases.
(00:38) A lot of people, I think, are very uncomfortable with the fact that they are essentially CRUD monkeys. They just make systems that create read, update or delete row in a database, and they have to compensate for that existential dread by overcomplicating things. That's a huge part of the satisfaction of driving a race car is driving in at the edge of adhesion, as we call it.
(01:03) Where you're essentially just a tiny movement away from spinning out. Doesn't take much, then the car starts rotating. Once it starts rotating, you lose grip and you're going for the wall. That balance of danger and skill is what's so intoxicating. - The following is a conversation with <PERSON> He<PERSON>mei<PERSON> <PERSON><PERSON>, also known as DHH.
(01:31) He is a legend in the programming and tech world, brilliant and insightful, sometimes controversial, and always fun to talk to. He's the creator of Ruby on Rails, which is an influential web development framework behind many websites used by millions of people, including Shopify, GitHub, and Airbnb. He is the co-owner and CTO of 37signals that created Basecamp, HEY and ONCE.
(01:57) He is a "New York Times" bestselling author, together with his co-author, Jason Fried of four books, "Rework," "Remote," "Getting Real," and "It Doesn't Have to Be Crazy at Work." And on top of that, he's also a race car driver, including being a class winner at the legendary 24-hour Le Mans Race. This is a Lex Fridman Podcast.
(02:21) To support it, please check out our sponsors in the description and consider subscribing to this channel. And now, dear friends, here's DHH. For someone who became a legendary programmer, you officially got into programming late in life, and I guess that's because you tried to learn how to program a few times and you failed.
(02:49) So can you tell me the the full story, the saga of your failures to learn programming? Was Commodore 64 involved? - Commodore 64 was the inspiration. I really wanted a Commodore 64. That was the first computer I ever sat down in front. And the way I sat down in front of it was I was five years old and there was this one kid on my street who had a Commodore 64.
(03:07) No one else had a computer, so we were all the kids just getting over there and we were all playing "Yie Ar Kung-Fu." I dunno if you've ever seen that game. It was one of the original fighting games. It's really a great game. And I was playing that for the first time at five years old.
(03:24) And we were like seven kids sitting up in this one kid's bedroom, all taking our turn to play the game. And I just found that unbelievably interesting. And I begged and I begged and I begged my dad, could I get a computer? And he finally comes home. He's like, "I got your computer." I was like, yes, my own Commodore 64. And he pulls out this black green and blue keyboard.
(03:47) That's an Amstrad 464. I was like, dad, what's this? - The disappointment. - This is not a Commodore 64. But it was a computer. So I got my first computer at essentially six years old that Amstrad 464. And of course, the first thing I wanted to do, I wanted to play video games.
(04:11) And I think the computer, which he by the way, had traded for TV and a stereo recorder or something like that. Came with like two games. One was this "Frogger" game where you had to escape from underground. It was actually kind of dark. Like this frog, you're trying to get it out from underground. And I was pretty bad at it. And I only had those two games.
(04:27) And then I wanted more games. And one way to get more games when you're a kid who don't have a lot of money, I can't just buy a bunch of games, is to type them in yourself. Back in '84, '85, magazines would literally print source code at the back of their magazines and you could just sit and type it in. So I tried to do that and it would take like two hours to print this game into the Amstrad.
(04:53) And of course I'd make some spelling mistake along the way and something wouldn't work, and the whole thing. I wasn't that good of English. I was born in Denmark. So I was really trying to get into it because I wanted all these games. I didn't have the money to buy 'em.
(05:10) And I tried quite hard for quite a while to get into it, but it just never clicked. And then I discovered the magic of piracy. And after that I kind of basically just took some time off from learning to program because, well, now suddenly I had access to all sorts of games. So that was the first attempt, like around six, seven years old. And what's funny is I remember these fragments.
(05:30) I remember not understanding the purpose of a variable. If there's a thing and you sign something, why would you assign another thing to it? So for some reason, I understand it constants. A constants made sense to me, but variables didn't. Then maybe I'm 11 to 12, I've gotten into the Amiga at this point.
(05:48) The Amiga, by the way, still perhaps my favorite computer of all time. I mean, this is one of those things where you're like, people get older and they're like, "Oh, the music from the '80s amazing." To me, even as someone who loves computers, who loved new computers, the Amiga was this magical machine that was made by the same company that produced the Commodore 64. And I got the Amiga 500, I think in '87.
(06:16) - Look at this sexy thing that is a sexy machine right there. - This is from an age by the way, where computing wasn't global in the same sense. The different territories had different computers that were popular. The Amiga was really popular in Europe, but it wasn't very popular at all in the US as far as I understand it. It wasn't popular in Japan.
(06:34) There were just different machines. The Apple II was a big thing in the US. I'd never even heard of Apple in the '80s in Copenhagen. But the Amiga 500 was the machine that brought me to want to try it again. And you know what's funny? The reason I wanted to try it again was I remembered the first time to learn.
(06:52) And then there was this programming language that was literally called EasyAMOS, like the easy version of AMOS. I'm like, if it's EasyAMOS, how hard can it be? I gotta be able to figure this out. And this time I tried harder. I got into conditionals, I got into loops, I got into all these things and I still, I couldn't do it.
(07:16) And on the second attempt, I really got to the point of like, maybe I'm not smart enough, maybe programming is just not for me, maybe it's too much math. Like I like math in this sort of superficial way. I don't like it in the deep way. That's some on my perhaps slightly nerdier friends did who I had tremendous respect for. Like I'm not that person. I'm not the math geek who's gonna figure it all out.
(07:38) So after that attempt with EasyAMOS and failing to even get, I don't even think I completed one even very basic game. I thought the program's just not for me. I'm gonna have to do something else. I still love computers. I still love video games. I actually at that time had already begun making friends with people who knew how to program, who weren't even programming EasyAMOS. They were programming freaking assembler.
(08:02) And I would sit down and just go, how do you, the moves and the memories and the copies, how do you even do this? I don't even understand how you go from this to Amiga demos, for example. That was the big thing with the Amiga. It had this wonderful demo scene in Europe.
(08:23) It's this really interesting period of time in Amiga's history where you had all these programmers spread out mostly all over Europe who would compete on graphic competitions, where you could probably bring one of these different... - On that thing? - On this thing, they would make these little almost like music videos, combining some midi music, combining some cool graphics. And they would do all of it in like 4K, four kilobytes.
(08:48) That is not 4Ks of revolution, four kilobytes of memory. And I just thought that was such a cool scene. This was obviously pre-internet. It was even pre BBS, bulletin board systems to some extent. It was you swap your demo software with someone else by sending them a disk in the mail, like the 3.5s. And I was enamored with that whole scene.
(09:17) I was enamored with what they were able to create and I just wanted to be a part of it even though I kind of didn't have any skills to contribute. And that's how I got into running BBSs. I didn't learn programming then and I wouldn't learn programming until much later, until I was almost 20 years old. The bulletin board systems existed in this funny space where they were partly a service to the demo scenes allowing all these demo groups to distribute their amazing demos.
(09:40) And then it was also a place to trade piracy software, pirated software. And I ended up starting one of those when I was 14 years old in my tiny little bedroom in Copenhagen. I had my, at that point, Amiga 4000. I had three telephone lines coming in into my tiny room. - [Lex] Nice. - Which is funny because again, I'm 14 years old.
(10:05) By the time I was in Sterling, my third line, you had to get someone from the telephone company to come do it. I get this guy and he's just looking around like, "What is this? Why the hell is a 14-year-old having three phone lines into their tiny little bedroom? What's going on here? Why are all these modems blinking red and black and making funny sounds?" - Did your parents know? - They did and they didn't. They knew I had the phone lines.
(10:29) They knew I had the computer. I don't think they really understood that I was trading pirated software that was both illegal, and whatever else was going on. - Oh, we should probably say that in Europe, maybe you can comment on this in, especially in Eastern Europe, but Europe in general, piracy I think was more acceptable than it was in the United States. I don't know there maybe it's just my upbringing. - Even that conversation wasn't present.
(10:54) I never spoke to anyone growing up in Denmark. - The piracy is wrong. - Had any moral qualms whatsoever about piracy. It was just completely accepted that you're a kid, you won a lot of games. You don't have a lot of money, what do you do? You trade. - Yeah. - Some people would occasionally buy a game.
(11:11) I mean, I once bought a second Master System, and I bought one game. Because that was what I could afford. I got "After Burner II." I dunno if you've ever played that game. It's pretty bad implementation on the second Master System, but it was like 600 crowners. And I was making money at that time doing newspaper delivery. I had to do that for a month to afford one game.
(11:37) I like video games way too much to wait a month just to get one game. So piracy was just the way you did it. And that was how I got into running this bulletin board system, being part of the demo scene, being part of the piracy scene to some extent. And then also at some point realizing, oh, you can actually also make money on this and this can fund buying more phone lines and buying more modems and buying more Amigas.
(12:01) Oh yeah, that was one of the demo parties. These were amazing things. - What am I looking at? - Isn't that amazing? - Look at all those CRT monitors. - All these CRT monitors. Again, when I was 14, I don't understand fully why my parents allowed this. But I traveled from Copenhagen, the capital of Denmark to Aarhus this tiny little town in Jutland on the train with a bunch of dudes who were like late teens and their 20s.
(12:28) I'm 14 years old. I'm lugging my 14-inch CRT monitor with my computer in the back to go to the party. That was what it was called. That was the biggest demo scene party at that time. And it was exactly as you see in that picture, thousands of people just lining up with their computers, programming demos all day long, and trading these things back and forth. - That's kind of awesome. Not gonna lie. It's a little ridiculous.
(12:52) - It's totally awesome. And I miss it in ways where the internet has connected people in some ways, but the connection you get from sitting right next to someone else who has their own CRT monitor who's lugged at halfway around the country to get there is truly special. Because it was also just this burst of creativity.
(13:10) You're constantly running around. You're constantly surrounded by people who are really good at what they could do. They're really good at programming computers. It's infectious. It was part of that pang I felt then going like, oh man, why can't I figure this out? I mean, why can't I even figure out EasyAMOS? It's kind of frustrating.
(13:28) - But on your third attempt, you a little more successful? - So third attempt is when I start getting it, this is when I start helping out, let's say build things for the internet. So around '95, I think it is, or '96, I discovered the internet. Actually, in ninth grade. That was my first experience. I went to some university in Denmark.
(13:52) And in ninth grade, we had this excursion and they sat us down in front of a computer and the computer had Netscape Navigator, the first version, or maybe it was even the precursor to that. And they had a text editor. And us kids just got like, "Hey, built something on the internet.
(14:12) " And it was just HTML and the first thing you do is like, oh, I can make the text blink by just putting in this tag and saving it. That moment, that was actually when I reawaken the urge to wanna learn the program because I got a positive experience. All the other experiences I had with programming was I'd spend hours typing something in, I'd click Run and it wouldn't work.
(14:34) And I'd get an error message that made no sense to me as a kid, either at six or seven or at 12. And here I am sitting in front of a computer connected to the internet and I'm making text blink. I'm making it larger. I'm turning it into an h1 or an h2. And these guys out here, "We just did it for like an hour and a half.
(14:53) " And suddenly I go, oh, I can make things for the internet that someone in Germany can be able to access and see, and I don't have to ask anyone for permission. This is super cool. I gotta do more of this. So I got into to the internet. I got into working with HTML and I still had all these friends from these demo parties. And I started working with them on creating gaming websites.
(15:11) I'd rather buy the video games, I'd review him. This was another good way of getting new video games was to walk down to some store and say like, hey, I'm a journalist. I'm like this 15-year-old kid. And they're looking at you, "You're a journalist?" Yeah, can I borrow some games? Because this was when games moved on to the PlayStation and these other things.
(15:28) You couldn't just as easily pirate, at least not at first. So I went down there, did all that, and that started the journey of the internet for me. It started working on these gaming websites, working with programmers, figuring out that I could do something, I could work on the HTML part.
(15:46) It's not really programming, but it kind of smells like it. You're talking to a computer. You're making it put text on the screen and you're communicating with someone halfway around the world. So that became my pathway back into programming. And then slowly I picked up more and more of it. First website I did with someone, one of these programs from the demo scene that was dynamic was ASP.NET.
(16:08) I wasn't even actually called .NET. That was what we started on. And then we moved on to PHP, and PHP was when I finally got it. When it finally clicked. When conditionals and loops and variables and all of that stuff started to make sense enough to me that I thought I can do this.
(16:30) - So would it be fair to say that we wouldn't have DHH without PHP, and therefore you owe all of your success to PHP? - 100% that's true. And it's even better than that because as PHP to me didn't just give a start in terms of making my own web applications, it actually gave me a bar. In many ways, I think the pinnacle of developer, web developer ergonomics is late '90s PHP, you write this script, you FTP it to a server and instantly it's deployed. Instantly it's available.
(17:00) You change anything in that file and you reload. Boom, it's right there. There's no web servers, there's no setup. There's just an Apache that runs mod PHP. And it was essentially the easiest way to get a dynamic webpage up and going. And this is one of the things I've been chasing that high for basically the rest of my career.
(17:24) That it was so easy to make things for the internet in the mid to late '90s. How did we lose the sensibilities that allowed us to not just work this way but get new people into the industry to give them that was success experiences that I had. Adding a freaking blink tag to an HTML page, FTPing a PHP page to an Apache web server without knowing really anything about anything, without knowing anything about frameworks, without knowing anything about setup.
(17:52) All of that stuff have really taken us to a place where it sometimes feels like we're barely better off. Like webpages aren't that different from what they were in the late '90s, early 2000s. They're still just forms. They still just write to databases. A lot of people, I think are very uncomfortable with the fact that they are essentially CRUD monkeys.
(18:12) They just make systems that create read, update or delete row in a database, and they have to compensate for that existential dread by overcomplicating things. Now that's a bit of a character. There's more to it. And there's things you can learn from more sophisticated ways of thinking about this. But there's still an ideal here, which is why I was so happy you had Pieter Levels on, because he still basically works like this.
(18:37) And I look at that and go like, man, that's amazing. - Yeah, you're chasing that high. He's been high all along using PHP, jQuery and SQLite. - I think it's amazing because he's proving that this isn't just a nostalgic dream. He's actually doing it. He's running all these businesses. Now some of that is, as he would admit up first upfront, is that he's just one guy.
(19:03) And you could do different things when you just one guy. When you're working in a team, when I started working on the team, when I started working with Jason Fried on Basecamp, we at first didn't use version control together. I used version control for myself.
(19:23) And then I thought, do you know what? Designers, ah, they probably not smart enough to figure out CBS. And therefore I just like, no, no, no, you just FTP it up. You just FTP it. I knew they knew how to do FTP. And then after the third time I had overridden their changes. I was like, goddammit. I gotta teach Jason CBS to not do that again. But I think there's still way more truth to the fact that we can work the way we did in the '90s, work the way Pieter works today, even in the team context.
(19:50) And that we've been far too willing to hand over far too much of our developer ergonomics to the merchants of complexity. - And you've been chasing that with Rails 8. So how do you bring all the cool features of a modern framework and make it no build, make it as easy to create something and to ship it as it was in the '90s with just PHP? It's very difficult for me to beat the Pieter Levels approach. It's so easy to just ship some PHP.
(20:22) - And it should be. Why should it be harder than that? Our computers today are almost infinitely faster than what they were in the '90s. So shouldn't we be able to work in even easier ways? We should be looking back on the '90s ago, like, oh, that was way too complicated.
(20:43) Now we have more sophisticated technology that's way faster and it allows us to work in these easier to use ways. But that's not true. But now you can see the line I draw in my work with Ruby on Rails, and especially with Rails 8. No build to me is reaching back to that '90s feeling and going, now we can do some of those things without giving up on all the progress. Because I do think you can get too nostalgic.
(21:08) I do think you can start just fantasizing that everything was better in the '90s. It wasn't. I mean, I was there. There was a lot of things that sucked. And if we can somehow find a way to combine the advantages and advances we've had over the past 20 years with that ease of developer ergonomics, we can win.
(21:29) No build is a rejection of the part of web development I've hated the most in the past 10, 15 years, which is the JavaScript scene. And I don't say that as someone who hate JavaScript. I mean, I often joke that JavaScript. It's my second favorite program language. It's a very distant second. Ruby is by far and away number one, but I actually like JavaScript. I don't think it's a bad language.
(21:49) It gets a lot of flack. People add a string of two plus a one and it gives something nonsense. And I just go like, yeah, but why would you do that? Just don't do that. The language is actually quite lovely, especially the modern version. ES6 that really introduced a proper class syntax to it. So I could work with JavaScript in many of the same ways that I love working with Ruby made things so much better.
(22:13) But in the early 2010s until quite recently, all of that advancement happened in pre-processing, happened in built pipelines. The browsers couldn't speak a dialect of JavaScript that was pleasant to work with. So everyone started to pre-filing their JavaScript to be able to use more modern ways of programming with a browser that was seen as stuck with an ancient version of JavaScript that no one actually wanted to work with.
(22:45) And that made sense to me, but it was also deeply unpleasant. And I remember thinking during that time, the dark ages, as I refer to them with JavaScript, that this cannot be the final destination. There's no way that we have managed to turn the internet into such an unpleasant place to work. Where I would start working on a project in JavaScript using webpack and all of these dependencies.
(23:10) And I would put it down for literally five minutes and the thing wouldn't compile anymore. The amount of churn that the JavaScript community, especially with its frameworks and its tooling went through in the decade from 2010 to 2020 was absurd. And you had to be trapped inside of that asylum to not realize what an utterly perverse situation we had landed ourselves in.
(23:42) Why does everything break all the time? I mean, the joke wouldn't be just that the software would break, that would annoy me personally. But then I'd go on Hacker News and I'd see some thread on the latest JavaScript release of some framework. And the thread would be like, someone would ask, "Well, aren't we using the thing we just used three months ago?" And people would be like, "That thing is so outdated. That's so three months ago.
(24:03) You gotta get with the new program. We're completely rewriting everything for the oomph-teen time." And anything you've learned in the framework you've been spending the last amount of time on, it's all useless. You gotta throw everything out and you gotta start over.
(24:20) Why aren't you doing it, stupid idiot? - Is that a kind of mass hysteria that took over the developer community you think? Like where you have to keep creating new frameworks and new frameworks. And are we past that dark age? - I think we're getting out of it, and we're getting out of it because browsers have gotten so much better. There was a stagnation in browser technology.
(24:39) Some of it was an overhang all the way back from IE5. So IE5 essentially put the whole internet development experience into a deep freeze, because Microsoft won the browser wars in the mid 2000s, and then they basically disbanded their browser development team. 'Cause they're like, "All right, job done. We don't need any more innovation on the internet.
(25:00) Can we just go back to writing Windows forms or something now that we control everything?" And it really wasn't until obviously Firefox kind of kindled a little bit of something. Then Chrome got into the scene and Google got serious about moving to web forward. That you had a kindling of maybe the browser could be better. Maybe the browser wasn't frozen in time in 2005.
(25:24) Maybe the browser could actually evolve like at the development platform that it is. But then what happened was you had a lot of smart people who poured in to the web because the web turned out to be the greatest application development platform of all time. This was where all the money was being made. This was where all the billionaires were being minted.
(25:47) This was where the Facebooks and whatever of the world came to be. So you had all of this brain power applied to the problem of how to work with the web. And there were some very smart people with some, I'm sure very good ideas who did not have programmer happiness as their motivation number one. They had other priorities and those priorities allowed them to discount and even rationalize the complexity they were injecting everywhere. Some of that complexity came from organizational structure.
(26:24) When you have a company like Facebook, for example, that does depend on the web and wanna push it forward, but have sliced the development role job into these tiny little niches. I'm a front-end glob pipeline configurator. Oh yeah, well, I'm a front-end whatever engineer. And suddenly the web developer was no longer one person.
(26:48) It was 15 different roles. That in itself injected a ton of complexity. But I also wanna give it the bold case here, which was that some of that complexity was necessary to get to where we are today. That the complexity was a bridge. It wasn't the destination, but we had to cross that bridge to get to where we are today, where browsers are frankly incredible.
(27:14) The JavaScript you can write in a text file and then serve on a web server for a browser to ingest is amazing. It's actually a really good experience. You don't need any pre-processing. You could just write text files, send them to a browser, and you have an incredible development. - And we should also say that it can kind of be broken, at least the HTML, but even the JavaScript could be a little bit broken and it kind of still works like maybe a half-ass works.
(27:41) But like, just the amount of mess of smelly code that a browser has to deal with is insane. - This is one of the hardest problems in computing today is to parse the entire internet. Because thankfully for us as web developers, but perhaps not so much for the browser developers, every webpage that has ever been created minus the brief period with Flash still runs today.
(28:07) The webpage I did in ninth grade would render on a modern browser today, 30 years later. - That's crazy. - That is completely crazy. When you think about the amount of evolution we've had with the web, how much better we've made it, how many more standards browsers have adopted. It's essentially an Apollo project today to create a new browser, which is why it doesn't happen very often, which is why even companies like Microsoft had to throw into towel and say, "We can't do it." Now I actually don't think that's good for the web.
(28:34) There is the danger of the monoculture if we just get a single browser engine that runs everything, and we are in danger of that. I love the fact that the Ladybird project, for example, is trying to make a new browser engine from scratch. I've supported that project. I would encourage people to look into that.
(28:52) It's really a wonderful - Nice. - thing. It's staffed by a bunch of people who worked on other browser projects in the past. - Truly independent web browser. - We really need that. But I can hold that thought in my head. At the same time, I hold the thought in my head that Google's Chrome was pivotal to the web surviving as the premier web development platform.
(29:19) If it had not been for Google and their entire business depending on a thriving open web, Apple, Microsoft, I think would've been just as fine to see the web go away to disappear into being something that's just served native web applications or native mobile applications and native desktop applications that they could completely control. So I have all sorts of problems with Google, but it's not Chrome.
(29:47) Chrome is a complete gift to web developers everywhere, to the web as a development platform. And they deserve an enormous amount of credit, I think for that. Even if it's entangled with their business model and half of Chrome is code that spies on you or informs targeted ads. And a bunch of things, I'm not a big fan of. I can divorce that from the fact that we need champions in the corner of the web who have trillions of dollars of market cap value riding on the open web. - We're gonna take tangents upon a tangents upon a tangents.
(30:20) So let's go to Chrome. I think Chrome positive impact on humanities is immeasurable for reasons that you just described. On the technology front, the features that present the competition they created, it's spurred on this wonderful flourishing of web technologies. But anyway, I have to ask you about the recent stuff with the DOJ, trying to split up Chrome and Google.
(30:44) Do you think this is a good idea? Do you think this does harm? - It's a disaster. And I say that as someone who's been very sympathetic to the antitrust fight, because I do think we have antitrust problems in technology. But the one place where we don't have them, by and large, is with browsers, is with the tools we use to access the open web.
(31:08) First of all, we have Firefox. Now Firefox is not doing all that great. And Firefox has been propped up by Google for many years to deter from exactly what's going on with the DOJ that they were the only game in town. Apple has Safari. I have a bunch of problems with Apple too, but I love Safari. I love the fact that we have a premier browser running on a premier operating system that people can't turn the web into just a Chrome experience.
(31:40) But I also think that the open web needs this trillion dollar champion, or at least benefits from it. Maybe it doesn't need it, but it certainly benefits from it. And of all the things that are wrong with monopoly formation in technology, Chrome is the last thing.
(32:00) And this is why I get so frustrated sometimes about the anti or the monopoly fight that there are real problems. And we should be focusing on the premier problems first. Like the toll booths on our mobile phones. They're far bigger problems. It's not the open web. It's not the tools that we use to access the open web. If I don't want to use Chrome, if my customers of my businesses that run on the internet don't wanna use Chrome, they don't have to.
(32:22) We're never forced to go through it. The open internet is still open. So I think it's a real shame that the DOJ has chosen to pursue Google in this way. I do think there are other things you can nail Google for and their ad monopoly maybe, or the shenanigans done in controlling both sides of the ad ledger that they both controlled the supply and the demand.
(32:45) There are problems. Chrome, isn't it? And you end up making the web much worse. And this is the thing we always gotta remember when we think about legislation, when we think about monopoly fights is you may not like how things look today. And you may wanna do something about it, but you may also make it worse.
(33:05) The good intentions behind the GDPR in Europe currently has amounted to what? Cookie banners that everyone on the internet hates. That helps no one do anything better, anything more efficient that saves no privacy in any way, shape or form has been a complete boondoggle that has only enriched lawyers and accountants and bureaucrats.
(33:30) - Yeah, you said that the cookie banner is a monument for why Europe is losing is doing the worst of all the regions in tech. - It's a monument to good intentions leading straight to hell. And the Europe is actually world class in good intentions leading straight to hell.
(33:56) - So hell is the cookie accept button that you have to accept all cookies. That's what hell looks like over and over. You don't actually ever get to the web page. - Just on a human scale, try to imagine how many hours every day are wasted clicking that away. And how much harm we've done to the web as a platform that people enjoy because of them. The internet is ugly in part because of cookie banners.
(34:21) Cookie banners were supposed to save us from advertisement, and advertisement can make the web ugly. There's plenty of examples of that, but cookie banners made the entire internet ugly in one felt swoop. And that's a complete tragedy. But what's even worse, and this is why I call it out as a monument to everything the EU gets wrong, is that we have known this for a decade.
(34:46) No one anywhere who's serious believes that cookie banners does anything good for anyone. Yet we've been unable to get rid of it. There's this one piece of legislation that's now I think 10 or 12 years old. It's complete failure on every conceivable metric. Everyone hates it universally, yet we can't seem to do anything about it.
(35:07) That's a bankruptcy declaration for any body of bureaucrats who pretend or pretend to make things better for not just citizens, but people around the world. This is the thing that really gets me about cookie banners too. It's not just the EU. It's the entire world. You can't hide from cookie banners anywhere on this planet.
(35:27) If you go to goddamn Mars on one of Elon's rockets and you try to access a webpage, you still see a cookie banner. No one in the universe is safe from this nonsense. - Probably the interface on the rocket. - It'll be slower. You'll have basically 150 second ping time. So it'll take you 45 seconds just to get through the cookie banners from Mars.
(35:49) - All right, let's walk back up the stack of this recursive tangents we've been taking. So Chrome, we should say, at least in my opinion, is not winning unfairly. It's winning in the fairway by just being better. - It is, if I was gonna steal man the other side just for a half second, people would say, well, maybe yes, most people do sort of begrudgingly agree that is a pretty good browser.
(36:16) But then they'll say the reason it got dominance was distribution. And the reason it got distribution was because Google also controls Android, and therefore can make Chrome the default browser on all these phones. Now I don't buy that.
(36:34) And the reason I don't buy that is because on Android, you are actually allowed to ship a different browser that has a browser engine that's not the same as Chrome. Unlike on iOS, where if you wanna ship a browser, Chrome, for example, ships for iOS, but it's not Chrome, it's Safari wrapped in a dress. And every single alternative browser on iOS have to use the Safari web engine. That's not competition. That's not what happened on Android.
(37:00) Again, I think there are some nuances to it, but if you zoom out and you look at all the problems we have with big tech, Chrome is not it. Chrome won on merits. I begrudgingly have switched to Chrome on that realization alone. As a web developer, I just prefer it. I like Firefox in many ways. I like the ethos of it, but Chrome is a better browser than Firefox full stop. - And by the way, we've never mentioned Edge.
(37:24) Edge is also a good browser - Because it's also Chrome in a dress. - But it never gets to love. I don't think I've ever used Bing, and I'm sure Bing is really nice. - Maybe you have, because you know what? Is Bing in a dress? - What? - DuckDuckGo. Which is actually - What? - the search engine that I use.
(37:41) DuckDuckGo gets its search results from Bing, or at least it used to. If they change that, that would be news to me. - Well, maybe everything is just a wrap or a dress. Everything is wearing a dress underneath. There's some other. - There's some of that. - Turtles, all the dress is all the way down.
(38:01) Okay, what were we talking about? They, we got there from JavaScript and from you learning how to program. So eventually, the big success story is when you built a bunch of stuff with PHP and you were like actually shipping things. And that's when the Ruby story came. So what your big love affair with programming began there? So can you take me there? What is Ruby? Tell the story of Ruby.
(38:27) Explain Ruby to me. - PHP was what converted me from just being able to fondle HTML and turn out some web pages to actually being able to produce web applications myself. So I owe a tremendous gratitude to PHP in that regard. But I never thought of PHP as a calling. I never thought, I'm a professional programmer who writes PHP, that's who I am and that's what I do.
(38:59) I thought of PHP as a tool I needed to smack the computer with until it produced web applications I wanted. It was very much a means to an end. I didn't fall in love with PHP. I'm very grateful that it taught me the basics of programming. And I'm very grateful that it set the bar for the economics. But it really wasn't until Ruby that I started thinking of myself as a programmer.
(39:24) And the way that came about was that the first time I ever got hired as a professional programmer to write code was actually by Jason Fried, my business partner still. All the way back in 2001, I had been working on these gaming websites in PHP for essentially 18 months at that point. No one had been paying me to do code in that regard. And I connect with Jason Fried over an email sent from Copenhagen, Denmark to Chicago, Illinois to a person who didn't know who I was.
(39:53) I was just offering solicited advice. Jason had asked a question on the internet and I had sent him the answer and he was asking in PHP. And I'd sent him the answer to that question. And we started talking and then we started working. Which by the way, is a miracle of what the internet can allow.
(40:12) How can a kid in Copenhagen who's never met this guy in Chicago connect just over email and start working together? And by the way, we're still working together now 24 years later. That's incredible. But we started working together and we started working together on some client projects. Jason would do the design, 37signals would do design, I would bring the programming PHP.
(40:30) And after we'd work on I think two or three client projects together in PHP, we kept hitting the same problem. That whenever you work with a client, you start that project off an email. Oh yeah, let's work together. Here's what we're building. And you start trading more and more emails. And before a few weeks have passed, you gotta add someone to the project.
(40:53) They don't have the emails. They don't have the context. You send it, where's the latest file? Oh, I've uploaded on the FTP. It's like finalfinal_v06_2.0, right? That's the one to get. It's just a mess. A beautiful mess in some ways, a mess that still runs the vast majority of projects to this day. Email is the lowest common denominator. That's wonderful.
(41:17) But we had dropped the ball a couple of times in serious ways with customers and we thought we can do better. We know how to make web applications. Can't we just make a system that's better than email for managing projects? It can't be that hard. We've been doing blogs. We've been doing to-do lists.
(41:35) Let's put some of these together and just make a system where everything that anyone involved in the project needs is on one page. And it has to be simple enough that I'm not gonna run a seminar teaching you how to use the system. I'm just gonna give you the login code. You're gonna jump into it. So that's Basecamp. And when we started working on Basecamp, I for the first time in the experience I had with Jason had the freedom of technology choice. There was no client telling me, "Yeah, PHP, that sounds good.
(42:04) We know PHP. Can you build it in PHP?" I had free reigns. And at that time I'd been reading IEEE magazine and a couple of other magazines back from the early 2000s where Dave Thomas and Martin Fowler had been writing about programming patterns and how to write better code. And these two guys, in particular, were both using Ruby to explain their concepts because Ruby looked like pseudo code.
(42:40) Whether you were programming in C or Java or PHP, all three constituencies could understand Ruby because it basically just read link English. So these guys were using Ruby to describe the concepts. And first of all, I would read these articles for just the concepts they were explaining. And I'd be like, what is this programming language? I mean, I like the concept you're explaining, but I also wanna see the programming language.
(43:03) Why haven't I heard of this? So I started looking into Ruby and I realized at that time, Ruby might not be known by anyone, but it's actually been around for a long time. Matz, the Japanese creator of Ruby had started working on Ruby back in '93. Before the internet was even a thing. And here I am in 2003, 10 years later, picking up what seems like this hidden gem that's just laying in obscurity in plain sight.
(43:33) But Dave Thomas and Martin Fowler, I think successfully put me and a handful of other people on the trail of a programming language that hadn't been used much in the west, but could be. So I picked up Ruby and I thought, this is very different. First of all, where are all the semicolons? I'd been programming in PHP, in ASP, I'd even done some Pascal, I'd looked at some C. There were semicolons everywhere.
(44:05) And that was the first thing that struck me is where are the damn semicolons? And I started thinking, actually, why do we have semicolons in programming? They're to tell the interpreter that there's a new line of instructions, but I don't need 'em as a human. How? Oh, someone is looking out for the human here, not for the machine. So that really got me interested.
(44:28) And then I thought to myself, do you know what? I know PHP quite well. I'm not an amazing programmer. I haven't been working in programming for all that long, but maybe I can figure it out. I'm gonna give myself two weeks. I'm gonna write a proof of concept where I talk to a database, I pull some records, I format them a bit, and I display 'em on an HTML page.
(44:55) Can I figure that out in a couple weeks? It took about one weekend and I was completely mesmerized. I was completely mind blown because Ruby was made for my brain like a perfect tailored glove by someone I'd never met. Like, how is this even possible? - We should say maybe like paint the picture of the certain qualities that Ruby has, maybe even compared to PHP.
(45:26) We should also say that there's a ridiculous thing that I'm used to that I forget about that there's dollar signs everywhere. PHP. - Yes, yes, there's line noise. That's what I like to call. - There's line noise. Line noise, that's such a beautiful phrase. Yeah, so there's all these things that look like programs. And with Ruby, I mean there's some similarities in Python there. It just looks kind of like natural language.
(45:46) You can read it normally. - Here's a wild loop that does a five iterations. You can literally type the number five, dot, now I'm calling a method under number five. By the way, that's one of the beautiful aspects of Ruby that primitives like integers are also objects. And you can call 5.times, start brackets.
(46:11) Now you're iterating over the code in that bracket five times, that's it. - [Lex] Okay, that's nice. - That's not just nice, that's exceptional. There's literally no other programming language that I know of that has managed to boil away the line noise that almost every other programming language would inject into a five time iteration over a block of code to that extent. - Wow, that's a really nice, well, thank you for giving that example.
(46:36) That's a beautiful example. Wow, I don't think I know what programming language that does that. That's really nice. - Ruby is full of that. So let me dive into a couple of examples. Because I really think it helps paint the picture. And let me preface this by saying I actually, I like the ethos of Python.
(46:54) I think the Ruby and the Python community share a lot of similarities. They're both dynamic interpreted languages. They're both focused on immediacy and productivity and ease of use in a bunch of ways. But then they're also very different in many other ways. And one of the one ways they're very different is aesthetically. Python, to me, I hope I don't offend people too much. I've said this before, it's ugly.
(47:19) And it's ugly in its space, because it's full of superfluous instructions that are necessary for legacy reasons of when Guido made Python back in '87 that are still here in 2025. And my brain can't cope with that. Let me give you a basic example. When you make a class in Python, the initialize a method, the starting method is def.
(47:45) Okay, fair enough. That's actually the same as Ruby, D-E-F, definition of a method. Then it is underscore, not one, underscore, two, init, underscore, underscore, parentheses start, self, comma, and then the first argument. - [Lex] Yeah, the whole self thing. - I look at that and go, I'm sorry I'm out. I can't do it.
(48:13) It's everything about it offends my sensibilities to the core. Here you have the most important method that all new objects or classes have to implement. And it is one of the most aesthetically offensive ways of typing initialized that I've ever seen anywhere. And you guys are okay with this? - Hey, you're making me, you know, where you're like talking about my marriage or something like this, and I'm not realizing I've been in a toxic relationship all along. Yet, I just get used to it.
(48:39) - That to me, by the way, was the magic of Ruby. It opened my eyes to help beautiful program could be. I didn't know I'd been working in ASP. I'd been working in PHP. I didn't even have the concept that aesthetics beautiful code was something we could optimize for that. Something we could pursue. And even more than that, that we could pursue it above other objectives. That Ruby is as beautiful as it is.
(49:06) It's not an accident and it's not easy. Ruby itself is implemented in C. It's very difficult to parse Ruby code, because Ruby is written for humans and humans are messy creatures. They like things in just the right way. I can't fully explain why the __init___ make me repulse, but it does. And when I look at the Ruby alternative, it's really instructive.
(49:38) So it's def, same part, D-E-F, space, initialize, parentheses. Not even parentheses. If you don't need to call it within the arguments, there's not even a parentheses. That in itself is actually also a major part. If the human doesn't need the additional characters, we're not just gonna put them in because it'd be nicer to parse for the computer.
(49:54) We're gonna get rid of the semicolons. We're gonna get rid of the parentheses. We're gonna get rid of the underscores. We're gonna get rid of all that ugliness, all the line noise and boil it down to its pure essentials. And at the same time, we're not gonna abbreviate.
(50:12) This is a key difference in the aesthetics between Ruby and Python as well. init, short of type, it's only five characters. Initialize is a lot longer, but it looks a lot better and you don't type it very often. So you should look at something pretty. If you don't have to do it all the time, it's okay that it's long. Those kinds of aesthetic evaluations are rife all over the Ruby language.
(50:35) But let me give you an even better example. The if conditional. That's the bedrock of all programming languages. They have the if conditional. If you take most programming languages, they all have if. That's basically the same in almost every language. Space, start parentheses, we all do that.
(50:56) And then you have perhaps, let's say you're calling a object called user.isadmin, close parentheses, close parentheses, start brackets, and here's what we're gonna do if the user is an admin, right? That would be a normal programming language. Ruby doesn't do it like that. Ruby boils almost all of it away. We start with the if. Okay, that's the same.
(51:24) No parenthesis necessary because there's no ambiguity for the human to distinguish that the next part is just a single statement. So you do if, space, user, dot, admin, question mark. No open brackets, no parentheses, no nothing. Next, open line. Here's your conditional. That question mark means nothing to the computer, but it meets something to the human.
(51:58) Ruby put in the predicate method style purely as a communication tool between humans. It's actually more work for the interpreter to be able to see that this question mark is there. Why is this question mark in here? Because it just reads so nicely. If user.admin? That's a very human phrase, but it gets better. You can turn this around.
(52:25) You can have your statement you want to execute before the conditional. You can do user.upgrade. Say you're calling an upgrade method on a user. Space, if, space, user.admin?. We do the thing if the thing is true. Instead of saying, if the thing is true, do the thing. But it gets even better. This is why I love this example with the conditional, because you can keep diving into it. So let's flip it around.
(52:52) user.downgrade if !not user.admin, right? That'd be a typical way of writing it. Ruby goes, that exclamation point is light noise. Why do we have if and then an exclamation point, that's ugly. We could do user.downgrade unless user.admin? That to me is an encapsulation of the incredible beauty that Ruby affords the programmer through ambiguity that is only to serve the human reader and writer.
(53:31) All of these statements we've just discussed, they're the same for the computer. It'll combine down to the same C code. That'll compile down to the same assembly code. It makes no difference whatsoever. In fact, it just makes it harder to write an interpreter. But for the human who gets to choose whether the statement comes before the conditional or the predicate method has, it's just incredible. It reads like poetry at some point.
(53:55) - It's also incredible that, you know, one language designer's creating that, you know, Guido van Rossum also, it's like one person gets to make these extremely difficult decision because you have to think about how does that all get parsed? And you have to think about the thousands, if it's a popular language that millions of people that end up using this.
(54:23) And what they feel, what that question mark for the if statement, what does that feel like for the user? - That's what Matz thought about because he started his entire mission off a different premise than almost every programming language designer that I'd heard, at least articulate the vision. That his number one goal was programmer happiness. That his number one goal was the affordances that would allow programmers to articulate code in ways that not just execute it correctly, but were a joy to write and were a joy to read.
(54:58) And that vision is based on a fundamentally different view of humanity. There's no greater contrast between Matz and James Gosling, the designer of Java. I wanted to listen to James talk about the design of Java. Why was it the way it was? Why was it so rigid? And he was very blunt about it, which, by the way, I really appreciate. And I think Gosling has done a tremendous job with Java. But his view of humanity is rather dark.
(55:24) His view of humanity was programmers at the average are stupid creatures. They cannot be trusted, which sophisticated programming languages because they're gonna shoot their foot off or their handoff. And that would be kind of inconvenient to the regional development office of a mid-tier insurance company writing code that has to last for 20 years.
(55:56) Now it's actually a very Thomas-Sowell view of constrained capacity in humans that I've come to appreciate much later in life. But it's also a very depressing view of programmers that there are just certain programmers who are too dumb to appreciate code poetry. There are too ignorant to learn how to write it well.
(56:17) We need to give them a sandbox where they just won't hurt themselves too much. Matz went the completely opposite direction. He believes in humanity. He believes in the unlimited capacity of programmers to learn and become better. So much so that he's willing to put the stranger at his own level. This is the second part I truly appreciate about Ruby.
(56:43) Ruby allows you to extend base classes. You know how we just talked about 5.times is a way to iterate over a statement five times? That five is obviously a base class. It's a number. Do you know what? You can add your own methods to that. I did, extensively.
(57:06) In Rails, we have something called active support, which is essentially my dialect of Ruby for programming web applications. And I'll give you one example. I've added a method called days to the Number. So if you do 5.days, you get five days in seconds. Because seconds is the way we set cache expiration times and other things like that. So you can say cache expires in 5.days.
(57:33) And you're gonna get whatever's five times - That's nice. - 24 times 60 times 60 is, or whatever the math is, right? Very humanly readable. In a novel programming language, you would type out the seconds and then you would have a little comment above it saying this represent five days. In Ruby, you get to write five days. But even better than that, Matz didn't come up with it.
(57:57) Matz didn't need the five days. I needed that because I needed to expire caches. I was allowed by Matz to extend his story with my own chapters unequal footing, such that a reader Ruby could not tell the difference between the code Matz wrote and the code that I wrote. He trusted me as a complete stranger from Denmark who had never met to mess with his beautiful story.
(58:25) That level of trust is essentially unheard of. I know there are other program languages that allow things with macros and so forth. But none do it in a way like Ruby does it. None does it with an articulated vision of humanity, a trust in humanity like Matz does, that is the opposite end of the spectrum of Java.
(58:47) - Yeah, I mean for my aesthetic sensibilities, just the way you describe 5.days, that's really pleasant to me. Like I could see myself sitting alone, sleep deprived, and just writing that. It's just an easy thing. You can write it in a long way with a comment. You can write multiple lines.
(59:08) You could do, and now with AI, I'm sure it's gonna generate it correctly, but there's something really pleasant about the simplicity of that. I'm not sure what that is. But you're right, there is a good feeling there. And I'm sure we'll talk about happiness from all kinds of philosophical angles, but you know, that is what happiness is made of.
(59:26) That little - Exactly. - good feeling there. - It's the good feeling that come out of a concept compressed to its pure essence. There's nothing you can take away from that statement that's superfluous. - But see, I also wanna push back a little bit 'cause I also a program named Perl a bunch. Just be cool. So like it's not all about compression.
(59:52) - No, you can compress it too far. Perl golf is a thing where you can turn programs into something that's unreadable for humans. Now the great thing about Perl was that it came out before Ruby. Matz was a great student of Wall, was a great student of Perl, was a great student of Python and Smalltalk and Lisp.
(1:00:13) He took inspiration from all of these prior attempts at creating good programming languages and really edited down the very best bits into this. So he was able to learn from his lessons. But what I found incredible about Ruby is that here we are 2025, Ruby has been worked on for over 30 years, and essentially the first draft is 90% of what we're still using.
(1:00:39) There was almost a sense of divine inspiration possible in wherever Matz was writing that initial version of Ruby that transcended time to such a degree that no one has still even begun to reach it. This is the other thing I always find fascinating. I generally believe in the efficient market theory that if someone comes up with a better mousetrap or better idea, others will eventually copy them to such an extent that perhaps the original mousetrap is no longer even remembered. No one has been able to copy that essence of Ruby.
(1:01:11) They borrowed elements, that's totally fine, but Ruby still stands taller than everyone else on these metrics, on this trust in humanity and programmers. - And we should also say like, you know, maybe the perfect programming language that metric and then there's the successful language and those are often different. There's something wonderful about the Brendan Eich story of creating JavaScript.
(1:01:36) There's something truly beautiful about the way JavaScript took over the world. I've recently got to visit the Amazon jungle and just one of my favorite things to do is just to watch the ants take over anything, everything. And it's just like it's a nice distributor system.
(1:01:56) It's a messy thing that doesn't seem to be ordered, but it just works and the machinery of it. - Worse is Better. Worse is Better. I mean that's actually the name of a pattern in software development and other ways of how do is the pattern of Linux. Linux was quantifiably worse than I think it was Minx at the time.
(1:02:17) Other ways of it that were more cathedral less bizarre and it's still one that there's something to it that the imperfections can help something go forward. It's actually a trick I've studied to the degree that I now incorporated in almost all open source that I do. I make sure that when I release the first version of any new thing I work on, it's a little broken.
(1:02:36) It's a little busted in ways that invite people to come in and help me. Because there's no easier way to get the collaboration of other programmers than to put something out that they know how to fix and improve. - Yeah, that's awesome. - But Ruby is somehow, or was at least a little bit different in that regard.
(1:02:54) Not in all regards. Matz got the ethos of the language, the design of language just right. But the first versions of Ruby were terribly slow. It's taken, I mean hundreds of man years to get Ruby to be both this beautiful yet also highly efficient and really fast. - And we should say that the thing that made you fall in love with this particular programming language is metaprogramming? - Yes, so that takes all of these elements we've just talked about and turned them up to 11.
(1:03:27) I'll explain metaprogramming real quick. - Yeah, please. - Metaprogramming is essentially a version of the 5.days. You get to add keywords to the language. Active record is the part of Rails that communicates with the database. This is a system where every table in the database is represented by a class.
(1:03:58) So if we take the user example, again, you do class user descends from active record base, and then the first line you can write is this. I want my users to have many posts or have many comments. Let's do that. We're making some system where users can make comments. The very next line is has_many, space, colon comments. Now you've set up a dependency between users and comments that will give your whole host of access and factory methods for users to be able to own comments, to create comments, to update comments in that line alone has many looks like a keyword.
(1:04:33) It looks like it's part of the Ruby language. That's metaprogramming. When Rails is able to add these elements to how you define a class and then that runs code that adds a bunch of methods to the use of class, that's metaprogramming. And when metaprogramming is used in this way, we call it domain specific languages.
(1:05:00) You take a generic language like Ruby and you tailor it to a certain domain like describing relationships in a database at a object level. And this is one of those early examples where you can do, user has many comments. Belongs _two_ :account. Now you've set up a one-to-one relationship before we had a one-to-many relationship. Rails is rife with all these kinds of domain specific languages where at sometimes it doesn't even look like Ruby.
(1:05:30) You can't identify Ruby keywords. You can just identify what looks like keywords in its own programming language. Now again, I know that Lisp and others also do this stuff. They just do it with the maximum amount of line noise that can ever be crammed into a programming language.
(1:05:51) And Ruby does it at a level where you cannot tell my metaprogramming from Matz's keywords and with zero line noise. - Yeah, I should say that my first love was Lisp. So there's a slow tear that you can't see. - I've actually never written any real Lisp myself. - Well, how can you judge it so harshly then? - 'Cause I have two eyes and I can look at code, and my aesthetic sensibilities forbid me to even go much further. Which is a limitation, I know.
(1:06:15) I should actually dive into Lisp because I've found that I've learned a lot just diving into, maybe I'm insulting Lisp again here, but the past of programming languages with Smalltalk, for example. I think Smalltalk is a incredible experiment that also worked but isn't suitable for today's programming environments.
(1:06:38) - I love that we're talking about Ruby so much and what beautiful code is and what a beautiful programming language is. So one of the things that is I think implied maybe you made explicit in your descriptions there is that Ruby is dynamic typing versus strict typing. And you have been not just saying that it's a nice thing, but that you will defend dynamic typing to the death.
(1:07:01) Like that freedom is a powerful freedom to preserve. - It's the essence of what makes Ruby Ruby. This is why I don't fully understand when people call for Ruby to add static typing. 'Cause to me, it's the bedrock of what this is. Why would you wanna turn one of the most beautiful languages into something far uglier? This is one of my primary objections to static typing.
(1:07:26) It's not just that it limits you in certain ways. It makes metaprogramming harder. I write a bunch of metaprogramming. I've seen what it takes to do metaprogramming TypeScript. That was actually one of the things that just really sent me on a tear of getting meta or getting TypeScript out of some of the projects that I'm involved with. We pulled TypeScript out of turbo.
(1:07:46) One of the front-end frameworks that we have, because I tried to write to metaprogramming in TypeScript and I was just infuriated. I don't want that experience, but I also don't want it from an aesthetic point of view. I hate repetition. We've just talked about how much I love that Ruby boils all of these expressions down to its essence. You can't remove one dot.
(1:08:08) You can't remove one character without losing something. This moment you go for static typing that you declare, at least I know there are ways to do implied typing and so forth. But let's just take the stereotypical case of a example, for example. Capital U, user, I'm declaring the type of the variable; lowercase user, I'm now naming my variable; equals uppercase user or new uppercase user. I've repeated user three times.
(1:08:38) I don't have time for this. I don't have sensibilities for this. I don't want my Ruby polluted with this. Now I understand all the arguments for why people like static typing when the primary arguments is that it makes tooling easier. It makes it easier to do auto complete in editors, for example.
(1:09:00) It makes it easier to find certain kinds of bugs because maybe you're calling methods that don't exist on an object and the editor can actually catch that bug before you even run it. I don't care. First of all, I don't write code with tools. I write them with text editors. I chisel them out of the screen with my bare hands. I don't auto complete. And this is why I love Ruby so much, and this is why I continue to be in love with the text editor rather than the IDE.
(1:09:31) I don't want an IDE. I want my fingers to have to individually type out every element of it because it will force me to stay in the world where Ruby is beautiful. Because as soon as it gets easy to type a lot of boilerplate, well guess what? You can have a lot of boilerplate. Every single language basically that has great tooling support has a much higher tolerance for boilerplate because the thinking is, well, you're not typing it anyway, you're just auto completing it.
(1:10:01) I don't want that at all. I want something where the fabric I'm working in, it's just a text file. There's nothing else to it. So these things play together. There's the aesthetic part, there's the tooling part, there's the metaprogramming part. There's the fact that Ruby's ethos of duck typing, I dunno if you've heard that term before.
(1:10:27) It's essentially not about, can I call this method, if a object is of a certain class. It is, can I call this method if the method responds? It's very out of Smalltalk in that regard. You don't actually check of whether that class has the method, which allows you to dynamically add methods at runtime and do all sorts of really interesting things that underpin all the beautiful metaprogramming that we do in Ruby. I don't wanna lose any of that.
(1:10:53) And I don't care for the benefits. One of the benefits I see touted over and over again is that it's much easier to write correct software. You can have fewer bugs. You're gonna have less null pointer exceptions. You're gonna have less all of this stuff. Yeah, I don't have any of that. It's just not something that occurs in my standard mode of operation.
(1:11:14) I'm not saying I don't have bugs, of course I do, but I catch those bugs with unit testing, with integration testing. Those are the kinds of precautions that will catch logical bugs. Things that compile but are wrong along with the uncompilable stuff. So I've never been drawn into this world, and part of it is because I work on a certain class of systems.
(1:11:37) I fully accept that. If you're writing systems that have five, 10, 50 million lines of code with hundreds, thousands, or tens of thousands of programmers, I fully accept that you need different methods. What I object to is the idea that what's right for a code base of 10 million lines of code with a hundred thousand programmers working on it is also the same thing I should be using in my bedroom to create Basecamp, because I'm just a single individual. That's complete nonsense.
(1:12:06) In the real world, we would know that that makes no sense at all. That you don't, I don't know, use your Pagani to go pick up groceries at Costco. It's a bad vehicle for that. It just doesn't have the space. You don't wanna muddy the beautiful seats. You don't wanna do any of those things.
(1:12:26) We know that certain things that are very good in certain domains don't apply to all. In programming languages, it seems like we forget that. Now, to be fair, I also had a little bit perhaps of a reputation of forgetting that. When I first learned Ruby, I was so head over heels in love with this programming language that I almost found it unconceivable that anyone would choose any other programming language at all to write web applications.
(1:12:49) And I kind of engaged the evangelism of Ruby on Rails in that spirit as a crusade, as I just need to teach you the gospel. I just need to show you this conditional code that we just talked about and you will convert at the point of a sharp argument. Now, I learned that that's not the way. And part of the reason it's not the way is the programmers think differently.
(1:13:07) Our brains are configured differently. My brain is configured perfectly for Ruby. Perfectly for a dynamically duck typed language that I can chisel code out of a text editor with. And other people need the security of an IDE. They want the security of classes that won't compile unless you call the methods on it.
(1:13:34) I have come to accept that, but most programmers don't. They're still stuck in, essentially, I like static typing. Therefore, static typing is the only way to create reliable correct systems, which is just such a mind blowing, to be blunt, idiotic thing to say in the face of evidence, mountains of evidence to the contrary.
(1:13:59) This is one of the reasons I'm so in love with Shopify as the flagship application for Ruby on Rails. Shopify exists at a scale that most programmers will never touch. On Black Friday, I think Shopify did 1 million requests per second. That's not 1 million requests of images. That's of dynamic requests that are funneling through the pipeline of commerce. I mean, Shopify runs something like 30% of all e-commerce stores on the damn internet.
(1:14:29) A huge portion of all commerce in total runs through Shopify and that runs on Ruby on Rails. So Ruby on Rails is able to scale up to that level without using static typing in all of what it does. Now I know they've done certain experiments in certain ways because they are hitting some of the limits that you will hit with dynamic typing.
(1:14:56) And some of those limits you hit with dynamic typing are actually, by the way, just limits you hit when you write 5 million lines of code. I think the Shopify monolith is about 5 million lines of code. At that scale, everything breaks because you're at the frontier of what humans are capable of doing with programming languages.
(1:15:14) The difference in part is that Ruby is such a succinct language that those 5 million, if they had been written in, let's just say Go or Java would've been 50 or 25. Now that might have alleviated some of the problems that you have when you work on huge systems with many programmers. But it certainly would also have compounded them trying to understand 25 million lines of code.
(1:15:33) - So the thing does scale. That's a persistent myth that it doesn't scale Shopify and others. But Shopify thinks a great example. By the way, I love Shopify and I love Tobi, amazing. - You gotta have Tobi on. - Yeah, for sure. - Just talking to him this morning. - For sure, he's a brilliant. I got to hang out with him in the desert somewhere, I forget in Utah.
(1:15:52) He's just a brilliant human. And Shopify, shopify.com/lex has been supporting this podcast for the longest time. I don't think actually Tobi knows that they sponsor this podcast. I mean this is a big company, right? - It's a huge company. I think just under 10,000 employees, market cap of 120 billion, GMV of a quarter of a trillion every quarter.
(1:16:17) - And he's involved with the details though. - He is, very much so. Funny story about Tobi. Tobi was on the Rails core team back in the mid 2000s. Tobi himself wrote Active Merchant, which is one of the frameworks for creating shops. He wrote the Liquid templating language that Shopify still uses to this day.
(1:16:41) He has a huge list of contributions to the Rails ecosystem, and he's the CEO of the company. Yeah, I think it's just... It's very inspiring to me because it's such at the opposite end of what I like to do. I like to chisel code with my own hands most of the day. He runs a company of almost 10,000 people that is literally like world commerce depends on it. A level of criticality I can't even begin to understand.
(1:17:06) And yet we can see eye to eye on so many of these fundamental questions in computer science and program development. That is a dynamic range to be able to encompass Rails being a great tool for the one developer who's just starting out with an idea who don't even fully know everything.
(1:17:28) Who is right at the level where PHP would've been a good fit in those late '90s because yeah, I could probably upload something to an FTP server, and so on. Rails does have more complexity than that, but it also has so much longer runway. The runway goes all the way to goddamn Shopify. That is about the most convincing argument I can make for sort of dynamic range that we can do a lot of it.
(1:17:46) And even having said that, Shopify is the outlier of course. I don't think about Shopify as the primary target when I write Rails. I think of the single developer. Actually, I do think about Shopify. But I don't think about Shopify now. I think of Shopify when Tobi was writing Snow Devil, which was the first e-commerce store to sell snowboards that he created, that was the pre-Shopify Shopify.
(1:18:11) He created all by himself. And that was possible because Ruby on Rails isn't just about beautiful code. It's just as much about productivity. It's just as much about the impact that an individual programmer is able to have. That they can build system where they can keep the whole thing in their head and be able to move it forward such that you can go from one developer sitting and working on something and that something is Shopify and it turns into what it is today.
(1:18:38) When we talk about programming languages and we compare 'em, we often compare 'em at a very late stage. Like what is the better programming language for let's say Twitter in 2009 when it's already a huge success. Twitter was started on Ruby on Rails. They then hit some scaling problems. It was a big debacle at the time.
(1:18:56) They end up then I think writing it in some other language, which by the way I think is the best advertisement ever for Ruby on Rails, because nothing fucking happened for 10 years after they switched over, right? Essentially zero innovation. Some of that was because they were doing a long conversion, and all of the early success in part came because they had the agility quickly change and adopt and so forth.
(1:19:21) That's what startups needs. That's what Shopify needed. That's what Twitter needed. That's what everyone needs. And that's the number one priority for Ruby on Rails. To make sure that we don't lose that. Because what happens so often when development tools and program language driven by huge companies is that they mirror their org chart.
(1:19:40) React and everything else needed to use that is in some ways a reflection of how Meta builds Facebook. 'Cause of course it is. Because of course it's an destruction of that. I'm not saying React isn't a great tool and that can't be used by smaller teams. Of course it can. But it's born in a very different context than something like Ruby on Rails.
(1:20:00) - Let me say this a small aside because I think we might return to Shopify and celebrate it often. Just a sort of personal note. This particular podcast has way more sponsors and sponsors that want to be sponsors than I could possibly ever have. And it's really, really important for me to not give a shit, and to be able to celebrate people. Like I celebrate people.
(1:20:25) I celebrate companies. And I don't care that they're sponsoring. I really don't care. I just wanna make that very explicit 'cause we're gonna continue saying positive things about Shopify. I don't care. Stop sponsoring. It doesn't really matter to me. But yeah, I just wanna make that explicit.
(1:20:47) So, but to linger on the scaling thing with the Twitter and the Shopify, can you just explain to me what Shopify is doing with the YJIT? What did they have to try to do to scale this thing? Because that's kind of an incredible story, right? - Yeah, so one of the great contributions that Shopify has made to the entire Ruby ecosystem, not just Rails but in particular Rails is YJIT.
(1:21:08) So YJIT is their compiler for Ruby. That just makes everything a lot more efficient and at Shopify scale eking out even a five, 10% improvement in Ruby's overhead and execution time is a huge deal. Now, Shopify didn't need YJIT. Shopify was already running on the initial version of Ruby that was, I think 10 times slower than what we have today.
(1:21:35) If you look back upon the Ruby 186, that Tobi probably started on just as I started on, and that was enough to propel Shopify to the scale that it has today. A lot of the scaling conversation in is lost in a failure to distinguish two things. Scale is kind of one package we talk about when there are really multiple packages inside of it.
(1:22:00) One is runtime performance, latency. How fast can you execute a single request? Can it happen fast enough that the user will not notice? If your Rails request takes a second and a half to execute, the user's gonna notice. Your app is gonna feel slow and sluggish.
(1:22:20) You have to get that response time down below, let's say at least 300 milliseconds. I like to target a hundred milliseconds as my latency. That's kind of performance. How much performance of that kind of latency can you squeeze out of a single CPU core, that tells you something about what the price of a single request will be.
(1:22:39) But then whether you can deal with 1 million requests a second like Shopify is doing right now. If you have one box that can do a thousand requests a second, you just need X boxes to get up to a million. And what you'll actually find is that when it comes to programming languages, they're all the same in this way. They all scale largely beautifully horizontally. You just add more boxes.
(1:23:01) The hard parts of scaling a Shopify is typically the program language. It's the database. And that's actually one of the challenges that Shopify has now is how do you deal with MySQL at the scale that they're operating at? When do you need to move to other databases to get worldwide performance? All of these things. The questions about scaling Ruby are economic questions.
(1:23:28) If we are spending so and so much on application servers, if we can get just 5% more performance out of Ruby, well we could save 5% of those servers and that could filter down into the budget. Now that analysis concludes into basically one thing. Ruby is a luxury language. It's a luxury, the highest luxury in my opinion.
(1:23:52) It is the Coco Chanel of programming languages. Something that not everyone can afford. And I mean this in the best possible way. There are some applications on the internet where each request has so little value, you can't afford to use a luxurious language like Ruby to program in it.
(1:24:14) You simply have to slum it with a C or a Go or some other low level language or Rust, talk about line noise there for... - It's like the thrift store of languages. - Exactly, where you need kind of just... you need a very low level to do it. You can't afford to use a luxury language to use to build it with. That's not true of Shopify. It wasn't true of Basecamp even back in 2004.
(1:24:34) It's not been true of 99% of all web applications ever created because the main cost component of 99% of web applications, it's not CPU course. It's wet course. It's human course. It's human capacity to understand and involve systems. It's their personal productivity.
(1:24:59) I did a calculation once when someone had for the 400th time said that, "Oh, if you switch from Ruby to some faster language, you could save a bunch of money." And I calculated it out that at the time, and I think the last time I did this calculation was almost a decade ago. We were spending about 15% of our operating budget on Ruby application service.
(1:25:23) So for me to improve my cost profile of the business by seven percentage points, I'd have to pick something twice as fast. That's quite hard. Versus if Ruby and Ruby on Rails was even 10% more productive than something else, I would move the needle far more because making individual programmers more productive actually matters a lot more. This is why people are so excited about AI.
(1:25:43) This is why they're freaking out over the fact that a single programmer in Silicon Valley who makes $300,000 a year can now do the work of three or five, at least in theory. I haven't actually seen that fully in practice, but let's just assume the theory is correct if not now, then in six months. That's a huge deal. That matters so much more than whether you can squeeze a few more cycles out of the CPU, when it comes to these kinds of business applications.
(1:26:12) If you're making unreal engine rendering stuff like Tim Sweeney you had on. Yeah, he needs to really sweat all those details. The Nanite engine can't run on Ruby. It's never going to. It would not meant for that, fine. These kinds of business applications absolutely can and everything that people are excited about AI for right now, that extra capacity to just do more.
(1:26:33) That was why we were excited about Ruby back in the early 2000s. That was be because I saw that if we could even squeeze out a 10% improvement of the human programmer, we'd be able to do so much more for so much less. - Probably argue about this, but I really like working together with AI, collaborating with AI. And I would argue that the kind of code you want AI to generate is human readable, human interpretable.
(1:27:01) If it's generating Perl golf code, it's not a collaboration. So it has to be speaking the human. It's not just you're writing the prompts in English. You also want to read the responses in the human interpretable language like Ruby, right? So that's actually is beneficial for AI too. 'Cause you've kind of said that for you the sculptor, the sort of the elitist Coco Chanel sculptor, you want to on your fancy keyboard to type every single letter yourself of your own fingers.
(1:27:33) But it's also that the benefit of Ruby also applies in once some of that is written by AI and you're actually doing with your own fingers the editing. Because you can interact with it because it's human interpretable.
(1:27:51) - The paradigm I really love with this was something Elon actually said on one of your shows when you guys were talking about Neuralink, that Neuralink allows the bandwidth between you and the machine to increase. That language either spoken or written is very low bandwidth. If you are to calculate just how many bits we can exchange as we are sitting here, it's very slow. Ruby has a much higher bandwidth of communication revealed conveys so much more concept per character than most other programming languages do.
(1:28:25) So when you are collaborating with AI, you want really high bandwidth. You want it to be able to produce programs with you, whether you're letting it write the code or not, that both of you can actually understand really quickly and that you could compress a grand concept, a grand system into far fewer parts that both of you can understand. Now, I actually love collaborating with AI too.
(1:28:54) I love chiseling my code and the way I use AI is in a separate window. I don't let it drive my code. I've tried that. I've tried the cursors and the wind surfaces and I don't enjoy that way of writing. And one of the reasons I don't enjoy that way of writing is I can literally feel competence draining out of my fingers. Like that level of immediacy with the material disappears.
(1:29:18) And the where I felt this the most was I did this remix of Ubuntu called Omakub when I switched to Linux, and it's all written in Bash. I'd never written any serious amount of code in Bash before. So I was using AI to collaborate, to write a bunch of Bash with me, 'cause I needed all this. I knew what I wanted.
(1:29:37) I could express it in Ruby. But I thought it was an interesting challenge to filter through Bash because what I was doing was setting up a Linux machine. That's basically what Bash was designed for. It's a great constraint. But what I found myself doing was asking AI for the same way of expressing a conditional, for example, in Bash over and over again. That by not typing it, I wasn't learning it.
(1:30:02) I was using it, I was getting the expression I wanted, but I wasn't learning it. And I got a little scared. I got a little scared like, is this the end of learning? Am I no longer learning if I'm not typing? And the way I, for me, recast that was, I don't wanna give up on the AI. It's such a better experience as a programmer to look up APIs, to get a second opinion on something, to do a draft. But I have to do the typing myself because you learn with your fingers.
(1:30:32) If you're learning how to play the guitar, you can watch as many YouTube videos as you want. You're not gonna learn the guitar. You have to put your fingers on the strings to actually learn the motions. And I think there is a parallel here to programming where programming has to be learned in part by the actual typing.
(1:30:51) - I'm just really, this is fascinating. Listen, part of my brain agrees with you. 100% part doesn't. I think AI should be in the loop of learning. Now current systems don't do that, but I think it's very possible for cursor to say, to basically force you to type certain things. So like if you set the mode of learning, I don't want to be this sort of give up on AI.
(1:31:20) I think vibe coding is a skill. So for an experienced programmer it's too easy to dismiss vibe coding as a thing. - [DHH] I agree, I wouldn't dismiss it. - But I think you need to start building that skill and start to figure out how do you prevent the competency from slipping away from your fingers and brain.
(1:31:43) Like how do you develop that skill in parallel to the other skill? I don't know. I think it's a fascinating puzzle though. I know too many really strong programmers that just kind of avoid AI, 'cause it's currently a little too dumb. - Yes, it's a little too slow. It's actually my main problem. It's a little too dumb in some ways, but it's a little too slow in other ways.
(1:32:07) When I use Claude's code, the terminal version of a Claude, which is actually my preferred way of using it, I get too impatient. It feels like I'm going back to a time where code had to compile and I had to go do something else. A boil some tea while the code is compiling. Well, I've been working in Ruby for 20 years. I don't have compiled weight in me anymore. So there's that aspect of it.
(1:32:30) But I think the more crucial aspect for me is I really care about the competence. And I've seen what happens to even great programmers the moment they put away the keyboard. Because even before AI, this would happen as soon as people would get promoted. Most great programmers who work in large businesses stop writing code on a daily basis because they simply have too many meetings to attend to. They have too many other things to do. And invariably they lose touch with programming.
(1:32:58) That doesn't mean they forget everything. But if you don't have your fingers in the sauce, source, you are going to lose touch with it. There's just no other way. I don't want that because I enjoy it too much. This is not just about outcomes.
(1:33:21) This is what's crucial to understand programming for programmers who like to code is not just about the programs they get out of it. That may be the economic value. It's not the only human value. The human value is just much in the expression. When someone who sits down on a guitar and plays "Stairway to Heaven," there's a perfect recording of that that will last in eternity. You can just put it on Spotify. You don't actually need to do it. The joy is to command the guitar yourself.
(1:33:47) The joy of a programmer, of me as a programmer, is to type the code myself. If I elevate myself, if I promote myself out of programming, I turn myself into a project manager. A project manager of a murder of AI crows as I wrote the other day. I could have been become a project manager my whole career. I could have become a project manager 20 years ago if I didn't care to write code myself. And I just wanted outcomes.
(1:34:11) That's how I got started in programming. I just wanted outcomes. Then I fell in love with programming and now I'd rather retire than giving it up. Now that doesn't mean you can't have your cake and needed to. I've done some vibe coding where I didn't care that I wasn't playing myself. I just wanted to see something. There was an idea in my head. I wanted to see something. That's fine.
(1:34:35) I also use AI all day long. In fact, I'm already at the point where if you took it away from me, I'd be like, oh my god, how do we even look things up on the internet anymore? Is Stack Overflow still around? Or I'm still a thing? Like how do I even find answers to some of these questions I have all day long.
(1:34:52) I don't wanna give up AI. In fact, I'd say the way I like to use AI, I'm getting smarter every because of AI. Because I'm using AI to have it explain things to me. Even stupid questions. I would be a little embarrassed to even enter into Google. AI is perfectly willing to give me the ELI5 explanation of some Unix command. I should have known already, but I don't, I'm sorry, can you just explain it to me and now I know the thing.
(1:35:20) So at the end of the day of me working with AI all day long, I'm a little bit smarter. Like 5%, sorry, not 5%, half a percent maybe. That compounds over time. But what I've also seen, when I worked on the Omakub project and I tried to let AI drive for me, I felt I was maybe half a percent dumber at the end of the day. - Okay, you said a lot of interesting things.
(1:35:43) First of all, let's just start at the very fact that asking dumb questions. If you go to Stack Overflow and ask a dumb question or read somebody else's dumb question and the answer to it, there's a lot of judgment there. AI sometimes to an excessive degree has no judgment. It usually says, oh, that's a great question.
(1:36:01) - [DHH] To a fault. - Yeah, oh, that's wonderful. Yeah, I mean, it's so conducive to learning. It's such a wonderful tool of learning and I too would miss it. And it's a great basically search engine into all kinds of nuances of a particular programming language, especially if you don't know it that well or like APIs you can load in documentation. It's just so great for learning. I, for me personally...
(1:36:30) I mean, on the happiness scale, it makes me more excited to program. I don't know what that is exactly. Part of that is the, I'm really sorry. Stack Overflow is an incredible website, but there is a negativity there. There's a judgment there. It's just exciting to be out like with a hype man next to me, just like saying, yeah, that's a great idea. And I'll say, no, that's wrong.
(1:36:56) I'll correct the AI, and the AI will say, "You're absolutely right. How did I not think about that?" You know, rewrite the code. I'm like, holy shit, I'm having, that's like a buddy. That's like really being positive and is very smart and is challenging me to think. And even if I never use the code it generates, I'm already a better programmer.
(1:37:20) But actually the deeper thing is for some reason I'm having more fun. That's a really, really important thing. - I like to think of it as a pair programmer for exactly that reason. Pair programming came a vogue in like the 2000s. Where you'd have two programmers in front of one machine and you'd push the keyboard between you. One programmer would be driving, they'd be typing in.
(1:37:39) The other programmer would essentially sit and watch the code, suggest improvements, look something up. That was a really interesting dynamic. Now unfortunately, I'm an introvert, so I can do that for about five minutes before I want to jump off a bridge. So it doesn't work for me as a full-time occupation.
(1:37:55) But AI allows me to have all the best of that experience all the time. Now I think what's really interesting, what you said about it makes it more fun. I hadn't actually thought about that, but what it's made more fun to me is to be a beginner again. It made it more fun to learn Bash successfully for the first time.
(1:38:14) Now I had to do the detour where I let it write all the code for me and I realized I wasn't learning nearly as much as I hoped I would and that I started doing once I typed it out myself. But it gave me the confidence that, you know what? If I need to do some iOS programming myself, I haven't done that in probably six years was the last time I dabbled in it. I never really built anything for real.
(1:38:39) I feel highly confident now that I could sit down with AI and I could have something in the app store by the end of the week. I would not have that confidence unless I had a pair of programming body like AI. I don't actually use it very much for Ruby code. I'm occasionally impressed whenever I try it, they're like, oh, it got this one thing right. That is truly remarkable and it's actually pretty good.
(1:38:56) And then I'll ask you two more questions and I go like, oh yeah, okay. If you were my junior programmer, I'd start tapping my fingers and going like, you gotta shape up. Now the great thing of course is we can just wait five minutes. The Anthropic CEO seems to think that 90% of all code by the end of the year is gonna be written by AI.
(1:39:14) I am more than a little bit skeptical about that, but I'm open-minded about the prospect that programming potentially will turn into a horse when done manually. Something we do recreationally is no longer a mode of transportation to get around LA. You're not gonna saddle up and go to the grocery store and pick up stuff from whole foods in your saddlebacks. That's just not a thing anymore.
(1:39:41) That could be the future for programming, for manual programming entirely possible. I also don't care. Like even though we have great renditions of all the best songs, as I said, there are millions of people who love to play the guitar. It may no longer have as much economic value as it once did. I think that I'm quite convinced is true that we perhaps have seen the peak.
(1:40:01) Now I understand the paradox, when the price of something goes down, actually the overall usage goes up and total spend on that activity goes up. That could also happen maybe. But what we're seeing right now is that a lot of the big shops, a lot of the big companies are not hiring like they were five years ago.
(1:40:19) They're not anticipating they're gonna need tons more programmers. Controversially, Tobi actually put out a memo inside of Shopify asking everyone who's considering hiring someone to ask the question, could this be done by AI? Now, he's further ahead on this question than I am.
(1:40:40) I look at some of the coding trenches and I go like, I'd love to use AI more, and I see how it's making us more productive, but it's not yet at the level where I just go like, oh, we have this project. Let me just give it to the AI agent and it's gonna go off and do it. - But let's just be honest.
(1:40:55) You're like a Clint-Eastwood type character cowboy and on a horse seeing cars going around and you're like, well. - That's part of it. And I think that's, it is important to have that humility that what you are good at may no longer be what society values. This has happened a million times in history that you could have been exceptionally good at saddle making, for example. That's something that a lot of people used to care about because everyone rode a horse.
(1:41:19) And then suddenly riding a horse became this niche hobby that there's some people care about it, but not nearly as many. That's okay. Now the other thing of this is I've had the good fortune to... I've been a programmer for nearly 30 years. That was a great run. I try to look at life in this way that I've already been blessed with decades of economically viable, highly valuable ways of translating what I like best in the working world to write Ruby code. That that was so valuable that I could make millions and millions of dollars doing it.
(1:41:51) And if that's over tomorrow, I shouldn't look at that with regret. I should look at it with gratitude. - But you're also a highly experienced, brilliant and opinionated human being, so it's really interesting to get your opinion on the future of the horse because it, you know, there's a lot of young people listening to this who love programming or who are excited by the possibility of building stuff with software, with Ruby on Rails, that kind of language. This and now the possibility. - But is it a career? - Is it a career?
(1:42:29) And if indeed a single person can build more and more and more with the help of AI, like how do they learn that skill? Is this a good skill to learn? I mean, that to me is the real mystery here, because I think it's still absolutely true that you have to learn how to program from scratch currently. But how do you balance those two skills? Because I too, as I'm thinking now, there is a scary slipping away of skill that happens.
(1:43:01) In a matter of like really minutes on a particular piece of code. It's scary. Not the way driving, you know, when you have a car drive for you doesn't quite slip away that fast. So that really scares me. So when somebody comes up to me and asks me like, how do I learn to program, I don't know what the advice is because I think it's not enough to just use cursor or copilot to generate code.
(1:43:29) - It's absolutely not enough. Not if you wanna learn, none of you want to become better at it. If you just become a tap monkey, maybe you're productive in a second. But then you have to realize, well, can anyone just tap? If that's all we're doing is just sitting around all day long tapping? Yes, yes, yes, yes, yes. That's not a marketable skill.
(1:43:47) Now, I always preface this both to myself and when I speak to others, it is rule number note one, nobody fucking knows anything. No one can predict even six months ahead. Right now, we're probably at peak AI future hype because we see all the promise, because so much of it is real and so many people have experienced it themselves.
(1:44:08) This mind boggling thing that the silicon is thinking in some way that feels eerily reminiscent of humans. I'd actually say the big thing for me wasn't even ChatGPT. It wasn't even Claude. It was DeepSeek. Running DeepSeek locally and seeing the think box where it converses with itself about how to formulate the response.
(1:44:33) I almost wanted to think, is this a gimmick? Is it doing this as a performance for my benefit? But that's not actually how it thinks. If this is how it actually thinks, okay, I'm a little scared. This is incredibly human how it thinks in this way. But where does that go? So in '95, 1 of my favorite movies, one of my favorite B movies came out, "The Lawnmower Man." - Great movie. - Incredible movie about virtual reality.
(1:45:00) Being an avatar and living in VR, like the story was a mess. But the aesthetics the world had built up was incredible. And I thought, we're five years away. I'm gonna be living in VR now. I'm just gonna be floating around. I'm gonna be an avatar. This is where most humans can spend most of the day that didn't happen. We're 30 years later, VR is still not here.
(1:45:25) It's here for gaming. It's here for some specialized applications. My oldest loves playing Gorilla Tag. I dunno if you've tried that. That's basically the hottest VR game. Wonderful, it's great. It's really hard to predict the future 'cause we just don't know. And then when you factor in AI and you have even the smartest people go like, I don't think we fully understand how this works.
(1:45:50) - But then on the flip side, you have Moore's Law that seems to have worked for many, many, many years in decreasing the size of transistor, for example. So like you know, Flash didn't take over the internet, but Moore's Law worked. So we don't know which one AI is. - Which one it is. And this is what I find so fascinating too.
(1:46:13) I forget who did this presentation, but someone in the web community, this great presentation on the history of the airplane. So you go from the Wright brothers flying in, what? 1903 or something like that. And 40 years later you have a jet flight. Just an unbelievable amount of progress in four decades. Then in '56, I think it was, the halt the sign for the Boeing 747 essentially precursor was designed and basically nothing has happened since. Just minor tweaks and improvements on the flying experience since the '50s.
(1:46:49) Somehow, if you were to predict where flying was gonna go, and you were sitting in 42 and you'd seen, you'd remember the Wright brothers flying in '03, and you were seeing that jet engines coming, you're like, we're gonna fly to to the stars in another two decades.
(1:47:09) We're gonna invent super mega hypersonic flights that's gonna traverse the earth in two hours. And then that didn't happen. It tapped out. This is what's so hard about predicting the future. We can be so excited in the moment because we're drawing a line through early dots on a chart, and it looks like those early dots are just going up into the right, and sometimes it's just flatten out.
(1:47:28) This is also one of those things where we have so much critical infrastructure, for example, that still runs on COBOL. That about five humans around the world really understand truly, deeply that there's a lot, it's possible for society to lose a competence. It's still needs because it's chasing the future. COBOL is still with us. This is one of the things I think about with with programming.
(1:47:51) Ruby on Rails is at such a level now that in 50 years from now, it's exceedingly likely that there's still a ton of Ruby on Rail systems running around. And very hard to predict what that exact world is gonna be like. But yesterday's weather tells us that if there's still COBOL code from the '70s operating social security today, and we haven't figured out a clean way to convert that, let alone understand it, we should certainly be humble about predicting the future.
(1:48:16) I don't think any of the programmers who wrote that COBOL code back in the '70s had any damn idea that in 2025, checks were still being cut off the business logic that they had encoded back then. But that just brings me to the conclusion on the question for what should a young programmer do? You're not gonna be able to predict the future. No one's gonna be able to predict the future.
(1:48:36) If you like programming, you should learn programming. Now, is that gonna be a career forever? I don't know, but what's gonna be a career forever? Who knows? Like a second ago, we thought that it was the blue collar labor that was gonna be extracted first. It was the robots that were gonna take over.
(1:48:54) Then GenAI comes out, and then all the artists suddenly look like, "Holy shit, is this gonna do all animation now? Is gonna do all music now?" They get real scared. And now I see the latest Tesla robot going like, oh, maybe we're back now to blue collar being in trouble. Because if it can dance like that, it can probably fix a toilet.
(1:49:14) So no one knows anything, and you have to then position yourself for the future in such a way that it doesn't matter that you pick a profession or path where if it turns out that you have to retool and re-skill, you're not gonna regret the path you took. That's a general life principle.
(1:49:36) For me, how I look at all endeavors I involve myself in is I want to be content with all outcomes. When we start working on a new product at 37signals, I set up my mental model for its success. And I go, do you know what? If no one wants this, I will have had another opportunity to write beautiful Ruby code, to explore Greenfield domain, to learn something new, to build a system I want, even if no one else wants it. What a blessing.
(1:50:02) What a privilege. If a bunch of people want it, that's great. We can pay some salaries, we can keep the business running, and if it's a blow away success, wonderful. I get to impact a bunch of people. - I think one of the big open questions to me is how far you can get with vibe coding, whether an approach for a young developer to invest most of the time it's into vibe coding or into writing code from scratch.
(1:50:29) So vibe coding, meaning, so I'm leaning into the meme a little bit, but the vibe coding, meaning you generate code, you have this idea of a thing you want to create, you generate the code and then you fix it with both natural language to the prompts and manually. You learn enough to manually fix it. So that's the learning process.
(1:50:55) How you fix code that's generated, or you write code from scratch and have the LMS kind of tab, tab, tab, tab, add extra code. Like which part do you lean on? I think to be safe, you should find the beauty and the artistry and the skill in both right from scratch. So like there should be some percent of your time just writing from scratch and some percent vibe coding. - There should be more of the time writing from scratch.
(1:51:20) If you are interested in learning how to program, unfortunately you're not gonna get fit by watching fitness videos. You're not gonna learn how to play the guitar by watching YouTube guitar videos. You have to actually play yourself. You have to do the sit ups. Programming, understanding, learning almost anything requires you to do.
(1:51:38) Humans are not built to absorb information in a way that transforms into skills by just watching others from afar. Now ironically, it seems AI is actually quite good at that, but humans are not. If you wanna learn how to become a competent programmer, you have to program. It's really not that difficult to understand. Now I understand the temptation.
(1:52:02) And the temptation is there because vibe coding can produce things. Perhaps in this moment, especially in a new domain you're not familiar with, with tools you don't know perfectly well, that's better than what you could do. Or that you would take much longer to get at, but you're not gonna learn anything.
(1:52:20) You're gonna learn in this superficial way that feels like learning, but is completely empty calories. And secondly, if you can just vibe code it, you're not a programmer. Then anyone could do it, which may be wonderful. That's essentially what happened with the Access database. That's what happened with Excel. It took the capacity of accountants to become software developers because the tools became so accessible to them that they could build a model for how the business was gonna do next week. That required a programmer prior to Excel. Now it didn't because they could do it themselves
(1:52:50) by coding enables non-programmers to explore their ideas in a way that I find absolutely wonderful. But it doesn't make your a programmer. - I agree with you, but I wanna allow for room for both of us to be wrong. For example, there could be, vibe coding could actually be a skill.
(1:53:14) That if you train it, and by vibe coding, let's include the step of correction, the iterative correction. It's possible if you get really good at that, that you're outperforming the people that run from scratch, that you can come up with truly innovative things, especially at this moment in history. While the LLMs are a little bit too dumb to create super novel things and a complete product, but they're starting to creep close to that.
(1:53:41) So if you are investing time now into becoming a really good vibe coder, maybe this is the right thing to do as if it's indeed a skill, we kind of meme about vibe coding, just like sitting back and it's in the name. But if you treat it seriously, a competitive vibe coder and get good at riding the wave of AI and get good at the skill of editing code versus writing code from scratch, it's possible that you can actually get farther in the long term.
(1:54:15) Maybe editing is a fundamentally different task than writing from scratch if you take that seriously as a skill that you develop. To me that's an open question. I just think, I personally, now you're on another level, but just me, just personally, I'm not as good at editing the code that I didn't write. - No one is. - That's one different. - No one is of this generation, but maybe that's a skill.
(1:54:43) Maybe if you get on the same page as the AI, 'cause there's a consistency to the AI. It's like it really is a pair programmer with a consistent style and structure and so on. Plus with your own prompting, you can control the kind of wrote code you write. I mean, it could generally be a skill like. - That's the dream of the prompt engineer. I think it's complete pipe dream.
(1:55:04) I don't think editors exist that aren't good at writing. I've written a number of books. I've had a number of professional editors. Not all of them wrote their own great books, but all of them were great writers in some regard. You cannot give someone pointers if you don't know how to do it. It's very difficult for an editor to be able to spot what's wrong with a problem if you that it couldn't make the solution themselves.
(1:55:34) Editing, in my opinion, is the reward, the capacity to be a good editor is the reward you get from being a good doer. You have to be a doer first. Now that's not the same as saying that vibe coding, prompt engineering won't be able to produce fully formed amazing systems even shortly. I think that's entirely possible, but then there's no skill left, which maybe is the greatest payoff at all.
(1:55:58) Wasn't that the whole promise of AI anyway, that it was just all natural language that even my clumsy way of formulating a question could result in a beautiful, succinct answer. That actually to me is a much more appealing vision that there's gonna be these special prompt engineering wizards who know how to tickle the AI just write to produce what they want.
(1:56:17) The beauty of AI is to think that someone who doesn't know the first thing about how AI actually works is able to formulate their idea and their aspirations for what they want, and the AI could somehow take that messy clump of ideas and produce something that someone wants. That's actually what programming has always been.
(1:56:40) There's very often been people who didn't know how to program, who wanted programs, who then hired programmers, who gave them messy descriptions of what they wanted and then when the programmers delivered that back said, "Oh no, actually that's not what I meant. I want something else." AI may be able to provide that cycle.
(1:56:59) If that happens to the fullest extent of it, yeah, there's not gonna be as many programmers around, right? But hopefully presumably someone still, at least for the foreseeable future, have to understand whether what the AI is producing actually works or not. - As a interesting case study, maybe a thought experiment.
(1:57:20) If I wanted to vibe code Basecamp or HEY, and some of the products you've built, like what would be the bottleneck? Where would I fail along the way? - What I've seen when I've been trying to do this, trying to use vibe coding to build something real is you actually failed really early. The vibe coding is able to build a veneer at the current present moment of something that looks like it works, right? But it's flawed in all sorts of ways.
(1:57:45) There are the obvious ways, the meme ways that it's leaking all your API keys, it's storing your password in plain text. I think that's ultimately solvable. Like it's gonna figure that out or at least it's gonna get better at that, but its capacity to get lost in its own labyrinth is very great right now.
(1:58:07) It code something and then you wanna change something and it becomes a game of whack-a-mole. Real quick, Pieter Levels who've been doing this wonderful flight simulator was talking to that where at a certain scale, the thing just keeps biting its own tail. You wanna fix something and it breaks five other things, which I think is actually uniquely human because that's how most bad programmers are.
(1:58:26) At the certain level of complexity with the domain, they can't fix one thing without breaking three other things. So in that way I'm actually in some way it's almost a positive signal for that the AI is gonna figure this out because it's done an extremely human trajectory right now. The kind of mistakes it's making are the kind of mistakes the junior programmers make all the time.
(1:58:49) - Yeah, can we zoom out and look at the vision, the manifesto, the doctrine of Rails? What are some of the things that make a programming language a framework great? Especially for web development. So we talked about happiness. The underlying objective of Ruby. What else? - So you're looking at the nine points I wrote out in I think 2012. And first, before we dive into 'em, I wanna say the reason I wrote it down is that if you want a community to endure, you have to record its values and you have to record its practices. If you don't, eventually you're gonna get enough
(1:59:24) new people come in who have their own ideas of where this thing should go. And if we don't have a a guiding light helping us to make decisions, we're gonna start flailing. We're gonna start actually falling apart. I think this is one of the key reasons that institutions of all kinds start falling apart. We forget why Chesterton's Fence is there.
(1:59:44) We just go like, why is that fence there? Let's yank it out. Oh, it was to keep the wolves out. Now we're all dead, oops. So I wanted to write these things down. And if we just take them quick one by one, you talked about optimizing for programmer happiness. I put that at number one in homage of Matz, and that's a lot about accepting that there is occasionally a trade off between writing beautiful code and other things we want out of systems.
(2:00:07) There could be a runtime trade off, there can be a performance trade off, but we're gonna do it nonetheless. We're also going to allow ambiguity in a way that many programmers, by default, are uncomfortable with. I give the example actually here of in the interactive Ruby shell where you can play with the language or even interact with your domain model. You can quit it in two ways, at least that I found.
(2:00:33) You can write exit. Boom, you're out of the program. You can write quit. Boom, you're out of the program. They do the same thing. We just wrote both exit or the people who built that wrote both exit and quit because they knew humans were likely to pick one or the other. Python is the perfect contrast to this.
(2:00:52) In the Python interactive protocol, if you write exit, it won't exit. It'll give you a lesson. It'll basically tell you to read the fucking manual. It says use exit parentheses or Ctrl-D i.e. end of file to exit. I'm like, one is very human and another is very engineer. And I mean that both of them in the best possible way. Python is pedantic.
(2:01:17) Python is the value from the start stated is that there should be preferably one and only one way to do a certain thing. Ruby is the complete opposite. No, we want the full expression that fits different human brains, such that it seems like the language is guessing just what they want.
(2:01:41) - And part of that is also described the principle of least surprise, which is a difficult thing to engineer into a language because you have to kind of, it's a subjective thing. - Which is why you can't do it in one way, which is why I used the example of both exit and quit. The principle of least surprise for some people would be like, "Oh, exit. That's how I get out of the prompt." For other people it'd be quit.
(2:01:58) Why don't we just do both? - Okay, so what's the convention over configuration? That's a big one. - That's a big one. That's a huge one. And it was born out of a frustration I had in the early days with especially Java frameworks where when you were setting up a web application framework for Java back in the day, it was not uncommon to literally write hundreds if not thousands of lines of XML configuration files. Oh, I need this.
(2:02:28) I want the database to use the foreign keys as post_id. No, no, no, I wanted as post capital ID. Oh, no, no, no, you have to do it capital PID. There are all these ways where you can configure how foreign relation keys should work in a database and none of them matter. We just need to pick one, and then that's fine. And if we pick one and we can depend on it, it becomes a convention. And if it's a convention, we don't have to configure it.
(2:02:53) And if we don't have to configure it, you can get started with you actually care about much quicker. So convention over configuration is essentially to take that idea that the system should come preassembled. I'm not just handing you a box of fucking Legos and asking you to build the Millennium Falcon. I'm giving you a finished toy.
(2:03:11) You can edit, you can change it, it's still build out a Legos. You can still take some pieces off and put in some other pieces, but I'm giving you the final product. And this cuts against the grain of what most programmers love. They love a box of Legos. They love to put everything together from scratch. They love to make all these detailed little decisions that just don't matter at all.
(2:03:29) And I want to elevate that up so that hey, I'm not trying to take the decisions away from you. I just want you to focus on decisions that actually matter that you truly care about. No one cares about whether it's post_id or post ID or PID. - Yeah, great defaults. - Yes. - It's just a wonderful thing. You have all these aspirations, they're gonna do some kind of custom, most beautiful Legos castle that nobody's ever built from these pieces.
(2:03:57) But in reality to be productive in most situations, you just need to build the basic thing. And then on top of that is where your creativity comes. - Absolutely, and I think this is one of those, part of the doctrine that a lot of programmers who get to use Ruby on Rails begrudgingly will acknowledge it's a nice thing, even if they don't really like it.
(2:04:21) Like it's hard to beat the sort of attraction to building with Legos from scratch out of programmers. That's just what we like. This is why we're programmers in the first place because we'd like to put these little pieces together, but we can direct that instinct towards a more productive end of the stack. - Okay, what are some of the other ones? - The menu is omakase.
(2:04:39) It actually comes out of the same principle that great defaults really matter. If you look at everything that's wrong with the JavaScript ecosystem right now, for example, it is that no one is in charge of the menu. There are a billion different dishes and you can configure just your tailored specific configuration of it, but no one done the work to make sure it all fits together.
(2:04:59) So you have all these unique problems in the JavaScript ecosystem, for example, there's probably 25 major ways of just doing the controller layer. And then as many of how to talk to the database. So you get this permutation of n times n times n of no one is using the same thing. And if they are using the same thing, they're only using the same thing for about five minutes. So we have no retained wisdom.
(2:05:23) We build up no durable skills. Rails goes the complete opposite way of saying, do you know what? Rails is not just a white framework. It's a complete attempt at solving the web problem. It's a complete attempt at solving everything you need to build a great web application.
(2:05:44) And every piece of that puzzle should ideally be in the box pre-configured, preassembled. If you want to change some of those pieces later, that's wonderful. But on day one you'll get a full menu designed by a chef who really cared about every piece of ingredient and you're gonna enjoy it. And that's again, one of those things where many programmers think like I know better.
(2:06:08) And they do in some hyper local sense of it. Every programmer knows better. This is what Ruby is built on. That every programmer knows better in their specific situation. Maybe they can do something dangerous. Maybe they think they know better and then they blow their foot off and then they truly will know better because they've blown their foot off once and won't do it again. But the the menu of omakase is that.
(2:06:31) - So you in general see the value in the in the monolith? - Yes, the integrated system. - Integrated. - That someone thought of the whole problem. This is one of the reasons why I've been on a crusade against microservices since the term was coined. Microservices was born out of essentially a good idea.
(2:06:50) What do you do at Netflix scale when you have thousands of engineers working on millions of lines of code? No one can keep that entire system in their head at one time. You have to break it down. Microservices can be a reasonable way to do that. When you're at Netflix scale, when you apply that pattern to a team of 20 programmers working on a code base of half a million lines of code, you're an idiot.
(2:07:13) You just don't need to turn method invocations into network calls. It is the first rule of distributed programming. Do not distribute your programming. It is makes everything harder. All the failure conditions you have to consider as a programmer just becomes infinitely harder when there's a network cable involved.
(2:07:33) So I hate the idea of premature decomposition and microservices is exactly that. The monolith says. Let's try to focus on building a whole system that a single human can actually understand and push that paradigm as far as possible by compressing all the concepts such that more of it will fit into memory of a single operating human. And then we can have a system where I can actually understand all the Basecamp. I can actually understand all of HEY.
(2:07:56) Both of those systems are just over a hundred thousand lines of code. I've seen people do this that maybe twice, maybe three times that scale and then it starts breaking down. Once you get north of certainly half a million lines of code, no individual human can do it and that's when you get into maybe some degree of microservices can make sense.
(2:08:14) - Basecamp and HEY are both a hundred thousand? - A hundred thousand lines of code thereabouts. - Wow, it's small. - It is. Considering the fact that Basecamp I think has something like 420 screens, different ways and configurations. - Ah, do you include the front end in that? - No, that's the Ruby code. Well, it's front end in the sense that some of that Ruby codes.
(2:08:31) It's beneficial to the front, but it's not JavaScript for example. Now the other thing we might talk about later is we write very little JavaScript actually for all of our applications. HEY, which is a Gmail competitor. Gmail ships I think 28 megabytes of uncompressed JavaScript. If you compress it, I think it's about six megabytes, 28 megabytes. Think about how many lines of code that is.
(2:08:49) When HEY launched, we shipped 40 kilobytes. It's trying to solve the same problem. You can solve the email client problem with either 28 megabytes of uncompressed JavaScript or with 40 kilobytes if you do things differently. But that comes through the same problem essentially. This is why I have fiercely fought splitting front end and back end apart.
(2:09:13) That, in my opinion, this was one of the great crimes against web development. That we are still atoning for. That we separated and divided what was and should be a unified problem solving mechanism. When you are working both on front end and back end, you understand the whole system.
(2:09:34) And you're not going to get into these camps that decompose and eventually you end up with like GraphQL. - Okay, let's fly through the rest of the doctrine. No one paradigm. - No one paradigm goes to the fact that Ruby is a fiercely object-oriented programming language at its core, but it's also a functional program language.
(2:09:55) This five times I told you about, you can essentially do these anonymous function calls and you can chain 'em together very much in the spirit of how true functional programming languages worked. Ruby has even moved closer towards the functional programming and of the scale by making strings immutable. There are ideas from all different disciplines and all different paradigms of software development that can fit together.
(2:10:19) Smalltalk, for example. With only object oriented, and that was just it. Ruby tries to be mainly object oriented, but borrow a little bit of functional programming, a little bit of imperative programming, be able to do all of that. Rails tries to do the same thing. We're not just gonna pick one paradigm and run it through everything.
(2:10:36) Object orientation is at the center of it, but it's okay to invite all these other disciplines and it's okay to be inspired. It's okay to remix it. I actually think one of the main benefits of Rails is that it's a remix. I didn't invent all these ideas. I didn't come up with active record.
(2:10:54) I didn't come up with the NBC way of dividing an application. I took all the great ideas that I had learned and picked up from every different camp and I put it together. Not because there was gonna be just one single overarching theory of everything, but I was gonna have a cohesive unit that incorporated the best from everywhere.
(2:11:15) - Is that idea a bit at tension with the beauty of the monolith system? - I think the monolith can be thought of as quite roomy, quite as a big tent that the monolith needs actually to borrow a little bit of functional programming for the kinds of problems that that excels, that discipline excels at solving, and that paradigm excels at solving. If you also want object orientation at its core.
(2:11:39) I actually think when I've looked at functional programming languages, there's a lot to love. And then I see some of the crazy contortions they have to go through when part of the problem they're solving calls for mutating something. And you go like, holy shit, this is a great paradigm from 90% of the problem. And then you're twisting yourself completely out of shape when you try to solve the last 10. - Ooh, exalt beautiful code is in the next one.
(2:12:04) - We've talked about that at length and here's a great example that really summarizes the domain specific language quality of Ruby on Rails that you can make code actually pleasant to write and read. Which is really funny to me because as we talked about when I started learning programming, it wasn't even a consideration. I didn't even know that that could be part of the premise, that could be part of the solution. That writing code could feel as good as writing a poem.
(2:12:31) - class Project, ApplicationRecord belongs_to :account has many participants. Class_name, Person, validates_presence_of :name. - See, you could read it out. You didn't even change. - Anything like a haiku or something. - Right? Isn't that beautiful? - Yeah, it's nice. It's really nice. There's an intuitive nature to it. Okay, so I have specific questions there.
(2:12:55) I mean ActiveRecord, just to take that tangent. That has to be your favorite feature. - It's the crown jewel of Rails. It really is. It is the defining characteristic of how to work with Ruby on Rails. And it's born in an interesting level of controversy, because it actually uses a pattern that had been described by Martin Fowler in the "Patterns of Enterprise Application Architecture.
(2:13:19) " One of the greatest books for anyone working on business systems. And if you had not read it, you must pick it up immediately. "Patterns of Enterprise Application Architecture," I think it was published in 2001. It is one of the very few programming books that I have read many times over. It's incredible.
(2:13:41) In it, Martin describes a bunch of different patterns of how to build business systems essentially. And ActiveRecord is a little bit of a footnote in there. The pattern is literally called active record. You can look it up. - Nice. - It's called active record. I wasn't even creative enough to come up with a name of my own. But it allows the creation, the marriage of database and object orientation in a way that a lot of programmers find a little off-putting.
(2:14:04) They don't actually want to pollute the beautiful object-oriented nature of that kind of programming with SQL. There was a rant by Uncle Bob the other day about how SQL is the worst thing ever. Baba, okay fine, whatever, I don't care. This is practical. We are making CRUD applications.
(2:14:27) You're taking things out of an HTML form and you're sticking 'em into a damn database. It's not more complicated than that. The more abstractions you put in between those two ends of the spectrum, the more you're just fooling yourself. This is what we're doing. We're talking to SQL databases.
(2:14:42) By the way, quick aside, SQL was one of those things that have endured the onslaught of NoSQL databases structured list data for a better part of a decade and still reign supreme. SQL was a good thing to invest your time in learning. Every program I'm working with the web should know SQL to a fair degree. Even if they're working with an ORM, an optic relational map or as active record. You still need to understand SQL.
(2:15:06) What Active record does is not so much try to abstract the SQL away behind a different kind of paradigm. It's just making it less cumbersome to write. Making it more amenable to build domain models on top of other domain models in a ways that you don't have to write every damn SQL statement by hand.
(2:15:26) - We'll just say the active record is an ORM, which is a layer that makes it intuitive and human interpretable to communicate with a database. - Even simpler than that. It turns tables into classes and rows into objects. I actually think SQL is very easy to understand most of it. You can write some SQL golf too. That's very hard to understand. But SQL at its base, and much of the criticism against SQL was it was written for human consumption.
(2:15:51) It's actually quite verbose, especially if you're doing things like inserts over and over again. It's quite verbose insert into table parentheses, enumerate every column you want to insert, values, parentheses, every value that fits with that column. It gets tedious to write SQL by hand, but it's actually very humanly readable.
(2:16:12) Active record just takes that tedious away. It makes it possible to combine things in a way that a humanly describable language just doesn't. It composes things into methods and you can combine these methods and you can build structures around them. So I don't dislike SQL, I dislike a lot of things in programming. I try to get rid of them. SQL wasn't really one of them.
(2:16:35) It was just a sense of I don't wanna write the same thing over and over again. It was a, can we be a little more succinct? Can we match it just slightly better to the optic orientation without trying to hide away the fact that we're persisting these objects into a database. That's where I think a lot of ORMs went wrong. They tried to live in the pure world of objects.
(2:16:55) Never to consider that those objects had to be consistent into a SQL database and then they came up with convoluted way of translating back and forth. Active record says, do you know what? Just accept it. This record, this object is not gonna get saved into some NoSQL database. It's not gonna be saved. It's gonna be saved into SQL database. So it's just structure the whole thing around that.
(2:17:15) It's gonna have attributes. Those attributes are gonna respond to columns in the database. It's not more complicated than that stuff making it so. - Yeah, but I should say, so I personally love SQL because I'm an algorithms person and so I love optimization.
(2:17:34) I love to know how the databases actually work so I can match the SQL queries and the design of the table such that there is, you know, optimal, squeeze the optimal performance out of the table. Okay, based on the actual way that that table is used. So I mean, I think that pushes to the point that like there is value in learning in understanding SQL. I wonder, because I started looking at active record and it looks really awesome.
(2:17:59) Does that make you lazy? Not you, but a person that rolls in and starts using Rails you can probably get away with never really learning SQL, right? - As long as you wanna stay at the entry level of competence. And this is actually my overarching mission with Rails is to lower the barrier of entry so far down that someone can start seeing stuff on their browser without basically understanding anything.
(2:18:26) Yeah, they can run Rail's new blog, run a couple of generators, they have a whole system, they don't understand anything. But it's an invitation to learn more. Where I get fired up, and this ties back to the AI discussion, is when that's turned into this meme, that programmers no longer have to be competent. I mean the AI is gonna figure it out. The generators is gonna figure it out.
(2:18:49) I don't need to know SQL, active record is gonna abstract it away from me. No, no, no dude, hold up. The path here is competence. I'm trying to teach you things. I understand I can't teach you everything in five minutes. No one who's ever become good at anything worthwhile could be taught everything in five minutes.
(2:19:07) If you wanna be a fully well-rounded web application developer, that takes years. But you can actually become somewhat productive in a few days. You can have fun in a few days for sure. You're gonna have fun in a a few minutes and a few hours. And over time, I can teach you a little more. Active record says like, yeah, yeah.
(2:19:25) All right, start to here and then like next week we'll do a class on SQL. - And actually you have this beautiful expression that I love that that a great programming language. Like Ruby has a soft ramp, the ramp goes to infinity. - That's exactly right. - So yeah. It's super accessible, super easy to get started. - And it never stops.
(2:19:45) There's always more to learn. This is one of the reasons I'm still having fun programming. That I'm still learning new things. I can still incorporate new things. The web is deep enough as a domain. You're never gonna learn all of it. - Provide sharp knives.
(2:20:02) - This is a good one, because another way of saying this, the opposite way of saying this, the Java way of saying is do not provide foot guns, right? I don't wanna give you sharp knives. You're a child. You can't handle a sharp knife. Here's a dull butter knife. Cut your damn steak, right? That's a very frustrating experience. You want a sharp knife even though you might be able to cut yourself. I trust humans in the same way that Matz trust humans.
(2:20:23) Maybe you cut off a finger. All right, you're not gonna do that again. Thankfully it was a virtual think finger. It's gonna grow back out. Your competence is gonna grow. It's more fun to work with sharp tools. - And that actually contributes to the the ramp that goes to infinity. - Yes, to the learning - Value integrated systems. - We kind of hit on that one.
(2:20:43) This is Rails is trying to solve the whole problem of the web, not just one little component. It's not leaving you a bunch of pieces. You have to put together yourself. - Progress over stability. - You know what? If there's one that's dated, it's probably that one.
(2:20:59) At this stage, Rails has been incredibly stable over many, many generations. The last major release Rails 8 was basically a no upgrade for anyone running Rail 7. Rail 7 was almost a no upgrade for anyone running Rail 6. I used to think it required more churn to get progress to stay on the leading edge of new stuff. And I wrote this before I experienced the indignity of the 2010s in the JavaScript community.
(2:21:29) Where it seemed like stability was not just unvalued, it was actually despised that churn in and of itself was a value we should be pursuing. If you were still working with the same framework three months later you were an idiot. And I saw that and I actually recoiled.
(2:21:47) And if I was gonna write the doctrine today, I'd write that differently. I wouldn't say progress over stability. - Well maybe it'd be a function of the age of the programming language also. - Maybe, or a deeper understanding of the problem. I think part of what's so fascinating about technology is that we have this perception that everything constantly moves so fast.
(2:22:06) No, it doesn't. Everything moves at a glacial pace. There is occasionally a paradigm shift like what's happening with AI right now. Like what happened with the introduction of the iPhone in 2007, like what happened with the internet in '95. That's basically the total sum of my career. Three things changed.
(2:22:26) Everything else in between was incremental small improvements. You can recognize a Rails application written in 2003. I know because the Basecamp I wrote back then is still operating, making millions of dollars in ARR, servings in customers on the initial version that was launched back then. And it looks like the Rails code if I squint a little that I would write today. So most things don't change even in computing.
(2:22:52) And that's actually a good thing. We saw with the JavaScript ecosystem. What happens when everyone gets just mad about constant churn? Things don't change that often. - By the way, on that small tangent. You just sort of visibly, verbally changed your mind with the you of 15 years ago. - Yes. - That's interesting.
(2:23:12) Have you noticed yourself changing your mind quite a bit over the years? - I would say, oh yes. And then also, oh no, in the sense that there are absolutely fundamental things both about human nature, about institutions, about programming, about business that I've changed my mind on. And then I've also had experiences that are almost even more interesting where I thought I had changed my mind and I tried it a new way, realized why I had the original opinion in the first place and then gone back to it. So it happens both ways. An example of the later part, for example,
(2:23:47) was managers at 37signals. For the longest time, I would Rail against engineering managers as an unnecessary burden on a small or even medium sized company. And at one point I actually started doubting myself a little bit. I started thinking like, do you know what? Maybe all programmers do need a one-on-one therapy session every week with their engineering manager to be a whole individual.
(2:24:15) So we tried that for a couple years where we hired some very good engineering managers who did engineering management the way you're supposed to do it, the way it's done all over the place. And after that I thought like, no, no, I was right. This was correct. We should not have had managers. Not every programmer needs a therapy session with an engineering manager every week. We don't need these and least scheduled huddles.
(2:24:35) We don't need all these meetings. We just need to leave people the hell alone to work on problems that they enjoy for long stretches of uninterrupted time. That is where happiness is found. That's where productivity is found. And if you can get away with it, you absolutely should.
(2:24:54) Engineering management is a necessary evil when that breaks down. - What's the case for managers then? - The case for managers is that if you do have a lot of people. There's a bunch of work that kind of just crops up. The one-on-one is one example that programmers need someone to check in with. There's another idealized version that someone needs to guide the career of juniors, for example, to give them redirecting feedback and all this other stuff.
(2:25:18) And it's not that in the abstract, I don't agree with some of those things, but in practice I've found that they often create more problems that they solve. And a good example here is can you get feedback from someone who's not better at your job than you are? You get some feedback. You can get feedback on how you show up at work.
(2:25:39) Are you being courteous to others? Are you being a good communicator? Okay, yes. But you can't get feedback on your work and that's more important. It's more important that you work under and with someone who's better at your job than you are if you wish to progress in your career. And every single programmer I've ever worked with was far more interested in progressing in their career on that metric, getting better at their craft than they were in picking up pointers that a middle manager could teach them.
(2:26:04) That's not saying that there isn't value in it. It's not saying there isn't value in being a better person or a better communicator. Of course there is all those things. But if I have to choose one or the other, I value competence higher. Like that's again, I caveat this a million times because I know what people sometimes hear.
(2:26:21) They hear the genius asshole is just fine and that's great and you should excuse all sorts of malicious behavior if someone's just really good at what they do. I'm not saying that at all. What I am saying is that the history of competence is a history of learning from people who are better than you, and that relationship should take precedence over all else and that relationship gets put aside a bit when engineering managers introduced.
(2:26:47) Now the funny thing is this conversation ties back to the earlier things we were talking about. Most engineering managers are actually former programmers. They at least no program to some extent, but what I've seen time and again is that they lose their touch, their feel with it very, very quickly and turn into pointy haired bosses very, very quickly who are really good at checking for updates.
(2:27:13) Just seeing where we are on project A here if you need anything or we ready to deliver. Okay, yes. And also, no. Shut up, leave me the hell alone. Let me program and then I'll come up for error. I'll talk with other programmers who I can spar with that we can learn something with. I can turn the problems over with and we can move forward. If you look back on the history of computer industry, all the great innovation that's happened, it's all been done by tiny teams with no engineering managers. Just full of highly skilled individuals.
(2:27:44) You've had John Carmack on here. I used to look up to its software so much. Not just because I loved quick, not just because I loved what they were doing, but because he shared a bit about how the company worked. There were no managers or maybe they had one business guy doing some business stuff, but that was just to get paid.
(2:28:01) Everything else was basically just designers and programmers. And there were about eight of them and they created goddamn "Quake II." So why do you need all these people again? Why do you need all these managers again? I think, again, at a certain scale it does break down. It's hard to just have a hundred thousand programmers running around wild without any product mommies or daddies telling them what to do.
(2:28:23) I understand that. And then even as I say that, I also don't understand it. Yeah, because if you look at something like Gmail, for example, there was like a side project done by Buchheit at Google at the time. So much of the enduring long-term value of even all these huge companies were created by people who didn't have a manager.
(2:28:43) And that's not an accident, that's a direct cause and effect. So I've turned in some way even more militant over the years against this notion of management, at least for myself and knowing who I am and how I wanna work. Because the other part of this is I don't want to be a manager, and maybe this is just me projecting the fact that I'm an introvert who don't like to talk to people on one-on-one calls every week.
(2:29:03) But it also encapsulates how I was able to progress my career. I did not really go to the next level with Ruby or otherwise until I had a door I could close and no one could bother me for six hours straight. - So in companies probably one of the reasons is it's very easy to hire managers, and managers also delegate responsibility from you.
(2:29:28) So if you just have a bunch of programmers running around, you're kind of response like it's work, it's intellectual work to have to deal with the the first principles of every problem that's going on. So managers like you can like relax, oh I'll be taken care of. But they then hire their own managers and it just multiplies and multiplies and multiplies.
(2:29:52) I would love it if some of the great companies would have in the United States, if there was like an extra side branch that we could always run, maybe physicists can come up how to split the simulation to where just all the managers are removed. Also just in that branch, just the PR and the comms people also.
(2:30:14) And even the lawyers, just the engineers and let's just see and then we merge it back. - I've essentially run that branch at 37signals for 20 years. And I've experimented with forking back on the other side. I've experimented with having a full-time lawyer on staff. I've experimented with having engineering managers.
(2:30:31) And I can tell you life is much better at 50, 60 people when none of those individuals or none of those roles. It's never about the individuals. It's about the roles. None of those roles are in your organization fulltime. Occasionally you need a manager. Occasionally you need a lawyer. I can play the role of manager occasionally, fine. And then I can set it back down to zero. It's almost like a cloud surface.
(2:30:56) I wanna a manager service I can call on for seven hours this week and then I wanna take it down to zero for the next three months. - Yeah. I read. I don't know if this is still the case, that Basecamp is an LLC and doesn't have a CFO, like a full-time accountant. Was that upgrade? - So the funniest. These days, we do have a head of finance. We did not for the first 19 years of life, I think.
(2:31:17) We got away with basically just an accountant do our books in the same way you would do a small ice cream shop, except we would over time have done hundreds of millions of dollars in revenue. The scale seemed quirky. And at some point, you can also fall in love with your own quirkiness to a degree that isn't actually healthy.
(2:31:35) And I've certainly done that over time and we should have had someone mount or count the beans a little more diligently, a little earlier. This was part of a blessing of just being wildly profitable and selling software that can have infinite margins basically that you kind of can get away with a bunch of stuff that you perhaps shouldn't.
(2:31:52) What partially taught me this lesson was when we realized we had not been collecting sales tax in different US states where we had Nexus. And it took us about two years and $5 million in settlements and cleanups to get out of that mess. And after that I went like, okay, fine, we can hire a finance person.
(2:32:14) And we now have a wonderful finance person, Ron, who actually ended up replacing something else. We used to have a full-time data analytics person who would do all sorts of insight mining for why are people signing up for this thing. We ran that for 10 years and realized, you know what? If I can have either a data analytics person or an accountant, I'm picking the accountant. - I love this so much on so many levels.
(2:32:35) Can we just linger on that advice that you've given that small teams are better? I think that's really less. Less is more. What did you say before? Worse is better. Okay, I'm sorry. - Worse better on adoption with technology a lot of times. And I think actually comes out at the same thing. It comes out of the fact that many of the great breakthroughs are created by not even just tiny teams but individuals.
(2:33:00) Individuals writing something. And an individual writing something on some parameter, what they do is worse. Of course it's worse when one person has to make something that a huge company have hundreds if not thousands of developers that they can have work on that problem.
(2:33:21) But in so many other parameters that worseness is the value, that less is the value. In getting real, which we wrote back in 2006. We talk about this notion of less software. When we first got started with Basecamp back in 2004, people would ask us all the time, aren't you petrified of Microsoft? They have so many more resources. They have so many more programmers.
(2:33:42) What if they take a liking to your little niche here and they show up and they just throw a thousand programmers at the problem? And my answer perhaps partly because I was like 24 was first of all, no, no care in the world. But the real answer was they're not gonna produce the same thing. You cannot produce the kind of software that Basecamp is with a team of a thousand people. You will build the kind of software that a thousand people builds.
(2:34:06) And that's not the same thing at all. So much of the main breakthrough in both end user systems but also in open source systems and fundamental systems, they're done by individuals or very small teams. Even all these classical histories of Apple has always been like, well it was a big organization but then you had the team that was actually working on the breakthrough. It was four people. It was eight people.
(2:34:30) It was never 200. - And large teams seems to slow things down. - Yes. - It's so fascinating and part of it's the manager thing. - Because humans don't scale. Communication between humans certainly don't scale. You basically get the network cost effect every time you add a new node, it goes up exponentially.
(2:34:56) This is perhaps the key thing of why I get to be so fond of having no managers at Basecamp, because our default team size is two. One programmer, one designer, one feature. When you're operating at that level of scale, you don't need sophistication. You don't need advanced methodologies. You don't need multiple layers of management because you can just do. The magic of small teams is that they just do.
(2:35:21) They don't have to argue because we don't have to set direction, we don't have to worry about the roadmap. We can just sit down and make something and then see if it's good. When you can get away with just making things, you don't have to plan.
(2:35:38) And if you can get out of planning, you can follow the truth that emerges from the code, from the product, from the thing you're working on in the moment. You know far more about what the great next step is, when you're one step behind rather than if you try 18 months in advance to map out all the steps. How do we get from here to very far away? You know what? That's difficult to imagine in advance because humans are very poor at that.
(2:36:02) Maybe AI one day will be much better than us, but humans can take one foot or put one foot in front of each other. That's not that hard and that allows you to get away with all that sophistication. So the process has become much simpler. You need far fewer people, it compounds. You need much less process.
(2:36:20) You need to waste less time in meetings. You can just spend these long glorious days and weeks of uninterrupted time solving real problems you care about and that are valuable and you're gonna find that's what the market actually wants. No one is buying something because there's a huge company behind it. Most of the time. They're buying something because it's good.
(2:36:42) And the way you get something good is you don't sit around and have a meeting about it. You try stuff. You build stuff. - It really is kind of incredible what one person, honestly one person can do in 100 hours of deep work of focused work, even less. - So I'll tell you this, I tracked exactly the number of hours I spent on the first version of Basecamp.
(2:37:09) And I was doing this because at the time I was working on a contract basis for Jason, he was paying me, I was gonna say $15 an hour. That's what I got paid when we first got started. I think he had bumped my pay to a glorious 25. But I was billing him and I know that the invoice for the first vision of Basecamp was 400 hours. That's what it took for one sole individual in 2004 to create an entire system that has then gone on to gross hundreds of millions of dollars and continues to do extremely well.
(2:37:36) One person just me setting up everything. Part of that story is Ruby. Part of that story is Rails. But a lot of it is also just me plus Jason, plus Ryan, plus Matt. That was the entire company at the time. And we could create something of sheer sustaining value with such a tiny team, because we were a tiny team. Not to spite off. Small is not a stepping stone.
(2:37:59) This is the other thing that people get into their head. This is one of the big topics of "Rework." That it gave entrepreneurs the permission to embrace being a small team. Not as a way point, not as like I'm trying to become a thousand people. No, I actually like being a small team. Small teams are more fun.
(2:38:18) If you ask almost anyone, I'm sure Tobi would say this too, even at his scale, the sheer enjoyment of building something is in the enjoyment of building it with a tiny team. Now you can have impact at a different scale when you have a huge company. I fully recognize that and I see the appeal of it.
(2:38:37) But in the actual building of things, it's always small teams, always. - How do you protect the small team. Basecamp has successfully stayed small. Has been the dragon, and you had to fight off. That like basically you make a lot of money. There's a temptation to grow. So how do you not grow? - Don't take venture capital. - Okay, that's step one. - That is point number one. - First of all, - Point number two everybody takes venture capital.
(2:39:03) So you already went. - I mean that's been the answer for the longest time. Because the problem isn't just venture capital, it's other people's money. Once you take other people's money, completely understandably, they want a return and they would prefer to have the largest return possible. 'Cause not them sitting in the code.
(2:39:21) It's not them getting the daily satisfaction out of building something. Chiseling beautiful code poems out of the editor, right? They don't get that satisfaction. They get the satisfaction maybe of seeing something nice put into the world, that's fair. But they certainly also get a satisfaction of a higher return.
(2:39:38) And there is this sense certainly in venture capital, stated in venture capital that the whole point of you taking the money is to get to a billion dollars or more. Now the path to that usually does go through running established playbooks and then when it comes to software, the enterprise sales playbook is that playbook.
(2:39:57) If you're doing B2B software SaaS, you will try to find product market fit. And the second you have it, you will abandon your small and medium sized accounts to chase the big whales with a huge sales force and by then you're a thousand people and life sucks. - That said, I mean people are just curious about this. I've gotten a chance to get to know Jeff Bezos.
(2:40:17) He invested in Basecamp, not controlling? - He bought secondaries. So this was the funny thing is that when investing have these two dual meanings. Normally when people think about investing, they think you're putting in growth capital because you want the business to hire more people to do more R&Ds so they can grow bigger. Bezos didn't do that actually.
(2:40:41) He bought a ownership stake directly from Jason and I, and 100% of the proceeds of that purchase went into my and Jason's bank account, personal bank account. Not a single cent went into the account of the company 'cause we didn't need the money to grow. What we needed or what we certainly enjoyed was to some extent maybe the vote of confidence, but more so the security of taking a little bit off the table is that we dare turn down the big bucks from venture capitals. It was essentially a vaccine against wanting to take a larger check
(2:41:19) from people who then wanted to take the company to something enormous that we didn't want to go with it. So Jeff gave Jason and I just enough money that we were comfortable turning all these people down in a way where if it had turned belly up like six months later, we wouldn't have been kicking ourselves and going, we had something here that was worth millions and now we have nothing and I have to worry about rent and groceries again. - It is a vote of confidence.
(2:41:46) I wonder from, I'd love to hear Jeff's side of the story of like why, 'cause he doesn't need like the money. So it's really, I think it probably is just believing in people and wanting to have cool stuff be created in the world and make money off of it, but not like.
(2:42:09) - 100% the motivation for Jeff wasn't a return, because he actually has a team. He's private office that runs these investments who did the calculus on the investment pitch we gave him, which was so ridiculous that Jason and I were laughing our asses off when we were writing down our metrics. I was like, no one's gonna pay this. No one is gonna give us this multiple of this amount of revenue. And that's fine.
(2:42:33) I mean we took the call essentially out of kind of an awe that Jeff Bezos even wanted to look at us and like, do you know what? We don't want venture capital. We don't need other people's money, but like let's just give him a bullshit number that no sane person would actually say yes to. And then I mean, we can each go our own way. And his investment team said like, "Jeff, no way.
(2:42:50) This makes no economic sense at all. They're asking for way too much money with way too little revenue." And Jeff just went like, "I don't care. I wanna invest in this guy." Because to him at the time it was chump change, right? Like Jason and I each got a few million dollars.
(2:43:06) I mean whatever the currency swing between the yen and the dollar that day probably moved 10 x that for his net worth than our investment did. Jeff seemed genuinely interested in being around interesting people, interesting companies, helping someone go to distance. And I actually look back on that relationship with some degree of regret, because I took that vote of confidence for granted in ways that I'm a little bit ashamed of over the years I've been more critical about some of the things that Amazon had done that I feel now is sort of justified. So that's just sort of part of that processing of it.
(2:43:44) But on the economic sense, he gave us that confidence. He gave us the economic confidence, but then he also gave us the confidence of a CEO running perhaps at the time the most important internet business in the US. Showing up to our calls, which we would have with him like once a year and basically just going like, yeah, you guys are doing awesome stuff. You should just keep doing awesome stuff. I read your book, it's awesome.
(2:44:08) You launched this thing, it's awesome. You should just do more of that. I don't actually know how to run your business. You guys know it. - So the book goes out. I'm just so, from a fan perspective, I'm curious about how Jeff Bezos was able to see, 'cause to me, you and Jason, like especially humans in the space of tech.
(2:44:28) And the fact that Jeff was able to see that, right? How hard is it to see that? - He certainly saw it very early. And I think this is something that Jeff does better than almost anyone else. He spots that opportunity so far in advance of anyone else even opened their eyes to it, or certain he is willing to bet on it far early and far harder than anyone else is.
(2:44:45) And he's just right time and again. I mean, we were not the only investment that he made. And certainly Amazon had a extremely long-term vision. So far longer than I have ever had the gumption to keep. Like I think of myself as a long-term thinker. I am playing a child's game compared to the game that Jeff is playing.
(2:45:10) Like when I looked at Amazon's economics around the dot-com boom and bust, they looked ridiculous. Like they were losing so much money. They were so hated by the market they were... no one believed that it was gonna turn into what it is, but Jeff did. In a way that that level of conviction I really aspire to. And I think that's one of the main things I've taken away from that relationship is that you can just believe in yourself to that degree against those odds. That's ridiculous.
(2:45:39) He did that at so many times our level that it's pathetic if I'm doubting myself. - Yeah, I think Amazon is one of those companies. I mean, it's come under a bunch of criticism over the years. This is something about humans that don't appreciate so much that we take for granted the positive that a thing brings real quick and then we just start criticizing the thing. It's the Wi-Fi and the airplanes. - [DHH] That's exactly it.
(2:46:06) - But I think Amazon, there could be a case made that Amazon is one of the greatest companies in the last a hundred years - For sure, I think it's an easy case to make. What I also think is that the price you pay to be one of the greatest companies in the last a hundred years is a lot of detractors, a lot of pushback, a lot of criticism that this is actually order restored in the universe.
(2:46:34) One of my favorite teachers in all the time I've been on the internet is Kathy Sierra. I don't know if you know her work, but she was actually Vern a few short years before the cruel internet ran her off. But she wrote a blog called Creating Passionate Users and she carved into my brain this notion of balance in the universe.
(2:46:58) If you are creating something of value that a lot of people love, you must create an equal and opposite force of haters. You cannot have people who love what you do without also having people who hate what you do. The only escape from that is mediocrity. If you are so boring and so uninteresting that no one gives a damn whether you exist or not, yeah, you don't get the haters, but you also don't get the impact of people who really enjoy your work.
(2:47:22) And I think Amazon is that just at the massive scale, right? They brought so much value and change to technology to commerce that they must simply have a black hole size of haters, otherwise the universe is simply gonna tip over. - Let me ask you about small teams. So you mentioned Jason a bunch of times, Jason Fried, you have been partners for a long, long time.
(2:47:46) Perhaps it's fair to say he's more on the sort of the design business side and you're like the tech, the engineering wizard. How have you guys over all these years creating so many amazing products, not murder each other? It's a great story of like partnership. What can you say about collaboration? What can you say that about Jason that you love that you've learned from? Why does this work? - So first I'll say we have tried to murder each other several times over the years, but far less, I think in the last decade.
(2:48:15) In the early days, our product discussions were so fierce that when we were having them in the office and there were other employees around, some of them were legitimately worried that the company was about to fall apart. Because the volume coming out of the room would be so high and sound so acrimonious, that they were legitimately worried the whole thing was gonna fall apart. But you know what's funny is that it never felt like that in the moment.
(2:48:46) It always felt like just a peak vigorous search for something better. And that we were able to stomach that level of adversity on the merits of an idea because it was about the idea. It wasn't about the person and it never really got personal. Not even never really, it didn't get personal. It wasn't like, Jason, you're an asshole.
(2:49:15) It was like, Jason, you're an idiot. And you're an idiot because you're looking at this problem the wrong way. And let me tell you the right way to do it. - As a small tangent, let me say this. Some people have said, oh, probably return to this, that you're sometimes can have flights of temper on the internet and so on. I never take it that way because it is the same kind of ilk.
(2:49:38) Maybe I haven't seen the right kind of traces of temper, but usually it's about the idea and it's just excited, passionate human. - That's exactly what I like to think of it as. It doesn't always come across as that. And I can see why spectators in particular sometimes would see something that looks like I'm going after the man rather than the ball. And I do think I've tried to get better at that.
(2:50:04) But in my relationship with Jason, I think it's worked so well because we have our own distinct areas of competence where we fully trust each other. Jason trusts me to make the correct technical decisions. I trust him to make the correct design and product direction decisions, and then we can overlap and share on the business, on marketing, on writing, on other aspects of it.
(2:50:30) So that's one thing is that if you're starting a business with someone where you do exactly the same as they do and you're constantly contesting who's the more competent person, I think that's far more difficult and far more volatile. So if you're starting a business and you're both programmers and you both work on the same kind of programming, ah, good luck. I think that's hard.
(2:50:53) I tried to pick an easier path working with a designer where I knew that at least half of the time I could just delegate to his experience and competence and say like, do you know what? I may have an opinion, I have an opinion all the time on design. But I don't have to win the argument because I trust you. Now occasionally we would have overlaps on business or direction where we'd both feel like we had a strong stake in the game and we both had a claim to competence in that area.
(2:51:18) But then for whatever reason, we also both had a long-term vision were I would go, do you know what? I think we're wrong here. But as I learned from Jeff Bezos, by the way, I'm gonna disagree and commit. That was one of those early lessons he gave us that was absolutely crucial and perhaps even instrumental in ensuring that Jason and I have been working together for a quarter of a century.
(2:51:40) Disagree and commit is one of the old time Jeff Bezos greats. - I'm just surprised that Yoko Ono hasn't come along. You know what I mean? Like there's so many Yokos in this world. - It might have happened. If not in part because we don't sit on each other's lap all the time. Most of our careers, we haven't even lived in the same city.
(2:52:04) Like I lived in Chicago for a couple of years while we were getting going after I'd moved to the US in 2005. But then I moved to Malibu and then I lived in Spain and then I lived in Copenhagen. And Jason and I from the foundation of our relationship learned how to work together in a remarkably efficient way where we didn't have to actually talk that much.
(2:52:29) On any given week, I'd be surprised if Jason and I spent more than two hours of direct exchange and communication. - Yeah, sometimes it's the basic human frictions that you just accumulate. - Yes, I think you rub up against another person, that person well better be your spouse if it's too much for too long. - Yeah, but even there. - Even there. - COVID has really test the relationship.
(2:52:47) It's fascinating to watch. - It has, and I do think that having some separation, which is kind of counterintuitive because I think a lot of people think the more collaboration you can have, the better. The more ideas that can bounce back and forth, the better. And both Jason and I, for whatever reason, came to the conclusion early on in careers. Absolutely not, that's complete baloney.
(2:53:06) This is why we were huge proponents of remote work. This is why I enjoy working in my home office where I can close the door and not see another human for like six hours at the time. I don't wanna bounce ideas off you all the time. I wanna bounce ideas off you occasionally and then I wanna go off and implement those ideas.
(2:53:27) There's way too much bouncing going on and not enough scoring, not enough dunking. And I think this is one of the great traps of executive rule. Once a founder elevates themselves all the way up to an executive where what they're doing is just telling other people what to do, that's the realm they live in 24/7. They just live in the idea realm. Oh, I can just tell more people more things what to do and we can just see it happen.
(2:53:51) If you actually have to be part of implementing that, you slow your horse. You think like, do you know what? I had a good idea last week. I'm gonna save the rest of my good ideas until next month. - And there is a temptation for the managers and for the people in the executive layer to do something, which that's something usually means a meeting, right? And so that's why you say...
(2:54:11) - Their job is telling other people what to do. - Yeah, and the meeting, so this is one of the big things you're against is meeting... - Meetings are toxic. And this really I think ties into this with Jason and I. If I had to count out the total number of meetings we've had in 24 years of collaborations where we in person sat in front of each other and discussed a topic, I probably it'd be less than whatever, three months at a fan company. We just haven't done that that much. We haven't worn it out.
(2:54:40) One of this funny metaphors that Trump came up with at one point was a human has like a limited number of steps in their life, right? Like that's the longevity argument here. You could do so much activity and then you run out. There's some kernel in that idea that can be applied to relationship. There's some amount of exchange we can have.
(2:54:59) There's some amount of time we can spend together where you can wear it out. Jason and I were diligent about not wearing each other out. And I think that is absolutely key to the longevity of the relationship combined with that level of trust. And then just combining with the level that we really like the work itself. We don't just like the brainstorming.
(2:55:20) The say where we just come up with good ideas. No, we like to do the ideas and we like to be part of that process directly ourselves. I like to program, he likes to do design. We could go off and do our little things for long stretches of time. In case you, come together and go like, hey, let's launch a great product.
(2:55:40) - This might sound like I'm asking you to do therapy, but I find myself to sometimes want or long for a meeting because I'm lonely. Like 'cause it remote work is just sitting by yourself. I don't know, it can get really lonely for long stretches of time. - Let me give you a tip, get a wife. - Yes, oh, goddammit. - Get a couple kids. - [Lex] All right. - Like family really is the great antidote to loneliness.
(2:56:09) And I mean that as sincerely as I can possibly say it. I certainly had exactly that feeling you described early in my career when I was working remotely and I was just a, like me living in an apartment. A total stereotype where for the longest time when I first moved to Chicago, all I had on the floor was a mattress and then I bought this big TV and I didn't even mount it. And then I had a stack of DVDs.
(2:56:33) And I was basically, I was working a lot of time and then I would just go home and do that. And it wasn't great. It really wasn't. Like I do think that humans need humans. And if you can't get them at work and I actually sort of kind of don't want them at work, at least I don't want 'em for 40 hours a week. That's not what I prefer.
(2:56:51) You need something else. You need other relationships in your life, and there's no greater depth of relationship if you can find someone that you actually just wanna spend a lot of time with. That's key to it. And I think it's key for both Jason and I that we've had families for quite a long time and it grounds you two in a way where the sprint of a startup can get traded in for the marathon of an enduring company. And you get settled in a way. We talked briefly about sometimes I get fired up.
(2:57:22) I mean a lot of times, maybe even most of the times I get fired up about topics, but I don't get fired up in the same way now as I used to when I was 24. I'm still extremely passionate about ideas and trying to find the right things, but having a family meeting, my wife, building a life around that has just mellowed everything out in a completely cliche way.
(2:57:48) But I think it's actually key. I think if we could get more even younger people not to wait until they were in the late goddamn 30s or early 40s to hitch up with someone, we'd be better off and we'd have more stable business relationships as well because folks would get that nurturing human relations somewhere else.
(2:58:15) Now when I say all of that, I also accept that there are plenty of great businesses that's been built over the years that have not been built remote, that have been built by a gang of hooligans sitting in an office for immense hours of time. I mean, both John Carmack and Tim Sweeney talked about that in the '90s with their careers that that was just basically work, sleep, hang out with the guys at the office, right? A totally fair. That never appealed to me.
(2:58:40) Both Jason and I saw eye to eye on the idea that 40 hours a week dedicated to work was enough that if we were gonna go to distance for not just the five to seven years it takes to build a VC case up to an exit. But for potentially 10 years, 20 years or further, we needed to become whole humans. Because the only that whole humanness was gonna go to distance, which included building up friendships outside of work, having hobbies, finding a mate and having a family.
(2:59:19) And that entire higher existence, those legs of the stool that work is not the only thing in life is completely related to the fact that we've been around for 25 years. There's way too much, especially in America of false trade-offs. Oh, you wanna build a successful business? Well, you can either have money enjoyment or family or health pick one.
(2:59:40) What? Why do we have to give up all of this? Now again, I'm not saying, and there are moments of payers in life where you can sprint. But I am saying if that sprint turns into a decade, you're gonna pay for it. And you can pay for it in ways. I've seen time and again seem like a very bad trade that even if it works. And by the way, most of the time it does not. Most of the time startups go bust.
(3:00:05) Most of the time people spend five, seven years or something that does not pan out and they don't get the payout and then they just sit with regret of like, what the fuck happened to my 20s? Early on, Jason and I basically made the pact that working together was not gonna lead to that kind of regret. That we were gonna allow ourselves and each other to build a whole life outside of work.
(3:00:29) And the fact that that worked is something I feel is almost like forbidden knowledge, certainly in technology circles in the US. It's something that we've tried to champion for 20 years and we still get slack. For just two days ago, I had another Twitter beef with someone saying like, "Oh, well okay, maybe it worked, but you didn't turn into Atlassian. So you're a failure.
(3:00:56) Basecamp isn't Jira, so why are you even bothering?" And it's such a fascinating winner-takes-all mentality that unless you dominate everyone else in all the ways you've lost. When so much of life is far more open to multiple winners where we can end up with a business that have made hundreds of millions of dollars over the years, and we've kept much of that to do whatever we want and that's enough. That's good, that's great. That's actually something worth aspiring to.
(3:01:30) Certainly it should be a path for someone to consider choosing rather than the VC unicorn of bust mentality that dominates everything. - Yeah, I'd love to ask you about this exchange so you can explain to me the whole saga, but so just to link on that a little bit is I think there's a notion that success for a tech founder is like work for a few years all out and then exit, sort of sell your company for, I don't know, hundreds of millions of dollars. That's success. When it seems in reality, when you look at who the people like you,
(3:02:09) like really smart, creative humans, who they actually are and what happiness entails. It actually entails working your whole life a little bit. It's like because you actually love the programming, you love the building, you love the designer and you don't want to exit. And that's something you've talked about really eloquently about.
(3:02:33) So like you actually want to create a life where you're always doing the building and doing it in a way that's not completely taken over your life. - Mojito Island is a mirage. It always was. There is no retirement for ambitious people. There is no just sitting back on the beach and sipping a mojito for what? For two weeks before you go damn crazy and wanna get back into the action? That's exactly what happens to most people who have the capacity to build those kinds of exits.
(3:03:00) I've never seen, I shouldn't say never. I've almost never seen anyone be able to pull that off. Yet so many think that that's why they're doing it. That's why they're sacrificing everything. Because once I get to the finish line, I'm golden. I've won, I can retire, I can sit back, I can just relax. And you find out that that kind of relaxation is actually hell.
(3:03:27) It's hell for creative people to squander their God-given creative juices and capacities. And I was really lucky to read the book "Flow" by Mihaly Csikszentmihalyi early on - Nice, the pronunciations, spot on. - You know what? I had to practice that with AI over the last few days because I knew I was gonna cite him and I butchered his name several times.
(3:03:47) So AI taught me how to pronounce that, at least somewhat correctly. But his main work over his career was essentially the concept of flow that came out of a search for understanding happiness. Why are some people happy? When are they happy? And what he learned was quite illuminating.
(3:04:11) He learned that people aren't happy when they sit on Mojito Island. They're not happy when they're free of all obligations and responsibilities. No, they're happy in these moments where they're reaching and stretching their capacities just beyond what they can currently do. In those moments of flow, they can forget time and space.
(3:04:33) They can sit in front of the key board program a hard problem, think 20 minutes have passed and suddenly it's been three hours. They look back upon those moments with the greatest amount of joy. And that is what peak happiness is. If you take away the pursuit of those kinds of problems, if you eliminate all the problems from your plate, you're gonna get depressed.
(3:04:52) You're are not gonna have a good time. Now there are people who can do that, but they're not the same kind of people who built these kinds of companies. So you have to accept the kind of individual you are. If you are on this path, don't bullshit yourself. Don't bullshit yourself into thinking, I'm just gonna sacrifice everything, my health, my family, my hobbies, my friends, but in 10 years I'm gonna make it all up because in 10 years I can do it. It never works out like that.
(3:05:16) It doesn't work out on both ends of it. It does not work out if you're successful and you sell your company, because you'll get bored out of your mind after two weeks on retirement. It doesn't work out if the company is a failure and you regret the last 10 years spent for nothing. It doesn't work out if it all works and you stay in the business because it never gets any easier.
(3:05:35) So you're gonna fail on all metrics if you just go, there's only work and nothing else. And I didn't want that. I wanted the happiness of flow. I understood that insight was true, but I wanted to do it in a way where I could sustain the journey for 40 or 50 years.
(3:05:56) - And there's another other interesting caveat that I've heard you say is that if you do exit and you sell your company and you wanna stay in, you wanna do another company that's going to usually not be as fulfilling. Because really your first baby, like. - You can't do it again or most people can't do it again. A, because their second idea is not gonna be as good as the first one. It is so rare to capture lightning in the bottle like we have, for example, with Basecamp.
(3:06:21) I know this from experience because I've been trying to build a lot of other businesses since. And some of them have been moderate successes, even good successes, none of them have been Basecamp. It's really difficult to do that twice. But founders are arrogant pricks, including myself. And we like to think that, do you know what? We succeeded in large part because we're just awesome. We're just so much better than everyone else.
(3:06:42) And in some ways that's true some of the time. But you can also be really good at something that matters for a hot moment that door is open. The door closes now you're still good at the thing, but it doesn't matter. No one cares. There's that part of it. And then there's the part of it that going back to experience things for the first time only happens the first time. You can't do it again.
(3:07:07) I don't know if I have it in me to go through the of the early days again. And I say bullshit in the sense of the most endearing sense. It's all great to do it. I know too much. This is one of the reasons why, whenever I'm asked the questions, if you could tell your younger self something that would really, what would you say to your younger self? I would fuck not say a thing.
(3:07:27) I would not rob my younger self of all the life experiences that I've been blessed with due to the ignorance of how the world works. Building up the wisdom about how the world works is a joy. And you gotta build it one break at a time if you just handed all the results. It's like, oh, should we watch your movie? Here's how it ends. I don't wanna fucking watch the movie now. You spoiled it. I don't want you to spoil my business experience.
(3:07:51) I don't wanna spoil any of my ignorance. The greatest blessing half the time when you're starting something new is A, you don't know how hard it's gonna be; B, you don't know what you don't know. Like the adventure is to pay off, the responsibility is to pay off. This is something Jordan Peterson has really taught me to articulate.
(3:08:10) This notion that responsibility is actually key to me. "Man's Search for Meaning," Viktor Frankl talks about this as well, that we can endure any hardship if there's a reason why. Now he talked about it in truly life altering concentration can't waste. But you can also apply at a smaller scale with less criticality of even just your daily life that all that hardship in building the original business that is responsibility you take upon yourself the appeal.
(3:08:43) The reason you take that on you is in part because you don't know fully what it entails. If you had known upfront, if I had known upfront, how hard it would be? How much frustration there'd be along the way? If you just told me that in a narrative before I got started, I would've been like, eh, maybe I should just go get a job. - You said so many smart things there.
(3:09:02) Just to pick one. It's funny that sometimes the advice givers, the wisdom givers have gone through all the bullshit. And so there is a degree to which you wanna make the mistake. So I think I would still give the advice of you want to have a stretch of your life where you work too hard, including a thing that fails.
(3:09:28) I don't think you can learn the lessons why that's a bad idea in any other way except by doing it. There is a degree like, but of course you don't. - I think you should stretch. Should you have to stretch for a decade? I'm not so sure. - Yeah, the decade thing is 20s is a special time. - It's a lot to trade. You don't get your 20s back.
(3:09:46) Yeah, you don't get your 30s back. You don't get your 40s back. You really, I would've regretted personally if I hadn't done the other things I did in my 20s, if I hadn't had the fun I had, if I hadn't had the friends I had, if I hadn't built up the hobbies that I did, if I hadn't started driving race cars at an early enough age to actually get really good at it, if I had just gone all in on business because I would've got the same out in the end.
(3:10:10) This is something Derek Sivers really taught me is he has this great essay about how when he went for a bike ride, he could go really hard all out and he could do the ride I think in whatever 19 minutes. Or he could enjoy the ride, go 5% slower, do the ride in 21 minutes and realize there's only two minutes apart.
(3:10:33) Either I go all in all the time, there's nothing else, I'm completely exhausted at the alt. Or I travel the same distance and I arrive maybe two minutes later, but I got to enjoy the scenery, listen to the birds, smell the flowers. That journey is also valuable.
(3:10:54) Now I say that while accepting and celebrating that if you wanna be the best at one thing in the world, no, you have to sacrifice everything. You have to be obsessed with just that thing. There is no instant of someone who's the best in the world at something who's not completely obsessed at. I didn't need to be the best at anything.
(3:11:15) This was a rare blessing of humility I had early on is like, do you know what? I am not that smart. I'm not that good. I'm not that talented. I can do interesting things by combining different aspects and elements that I know, but I'm not gonna be the best at anything. And that released me from this singular obsession with just going, I'm gonna be the best programmer in the world. Eh, I know I'm not. I failed at it twice before I even got how conditional it's worked.
(3:11:39) I'm not smart enough to be the best at anything. I'm not dedicated enough to do that. That's a bit of a blessing. And I think as a society, we have to straddle both celebrating peak excellence, which we do all the time and celebrating the peak intensity of mission it takes to become that. And then also going like, do you know what? We don't all need to be Michael Jordan.
(3:12:03) There's only gonna be one of those. - Well, we should say that there's certain pursuits where a singular obsession is required. Basketball is one of them. By the way, probably racing. If you wanna be the best at F1 in the world... - If you wanna be Senna, you gotta be a maniac.
(3:12:25) - But I would argue that there's most disciplines like programming allows if you want to be, quote, unquote, "the best," whatever that means, I think that's judged at the end of your life. And usually if you look at that path, it's gonna be an nonlinear one. It you're not gonna look like the life of an Olympic athlete who's singular focus.
(3:12:44) There's gonna be some acid there in the 20s or there's going to be a several detours, which the true greats. There's gonna be detours. And sometimes they're not gonna be Steve Jobs asset type of situation. There'll be just different companies you've worked for different careers or different sort of efforts you allocated your life to. But it's gonna be non-linear. It's not gonna be a singular focus.
(3:13:10) - The way I think about this sometimes is I want a good bargain on learning. I can become in the top 5% of whatever I defined as good at something much, much easier. Perhaps it's 20 times easier, 100 times easier to get into the top 5% than it is to get into the top 0.1%. That's almost impossibly hard to get into that.
(3:13:36) But if I'm content just being in the top 5%, I can be in the top 5% on like five things at once. I can get really good at writing. I can get decent at driving a race car. I can become pretty good at programming. I can run a company. I can have a family. I can do a lot of things at the same time. That gives me sort of that variety that almost was idealized Karl Marx has this idea, "Oh, I'm gonna fish in the morning and hammer in the evening and paint on the weekends, right?" That there's a sense for me at least where his diagnosis of alienation was true. That just that tunnel vision. There's just this one thing I'm just gonna focus on that
(3:14:12) gives me a sense of alienation I can't stomach. When I'm really deep on programming and sometimes I go deep for weeks, maybe even in a few cases months, I have to come up for air and I have to go do something else. Like, all right, that was programming for this year.
(3:14:32) I've done my part and I'm gonna go off riding or annoy people on the internet or drive some race cars to do something else. And then I can do the programming thing with full intensity again next year. - Speaking of annoying people on the internet, you gotta explain to me this drama. Okay, so what is this guy that said, imagine losing to Jira but boasting that you have a couple million dollars per year.
(3:14:50) So this had to do with this almost now a meme decision to leave the cloud. DHH left the cloud. I think that's literally a meme, but it's also a fascinating decision. Can you talk through the full saga of DHH leaves the cloud? - Yes. - Of leaving AWS saving money. And I guess the case this person is making now.
(3:15:17) - Is that we wasted our time optimizing a business that could've been a hundred times bigger if we'd just gone for the moon. - And for the moon includes? - Venture capital. - But also... - And other things in not caring about cost. - But also because AGIs are on the corner, you should have been investing into AI, right? Is this just part of? - Sort of tank ginger? I think it's a bit of a muddy argument, but if we just take it at it's peak ideal, which I actually think is a reasonable point, is that you can get myopically focused on counting pennies when you should be focused on getting, right? That I've optimized our spend on infrastructure
(3:15:55) by getting out of the cloud and that took some time and I could have taken that time and spend it on making more features that would've attract more customers or spend even more time with AI or done other things. Opportunity cost is real. I'm not denying that.
(3:16:15) I'm pushing back on the idea that for a company of our size saving $2 million a year on our infrastructure bill, which is about somewhere between half to two thirds goes directly to the bottom line, which means it's return to Jason or I as owners and our employees part of our profit sharing plan, is totally worth doing.
(3:16:40) This idea that costs don't matter is a very Silicon Valley way of thinking that again, understand at the scale of something maybe, but I also actually think it's aesthetically unpleasing. I find an inefficient business as I find an inefficient program full of line noise to just be a splinter in my brain. I hate looking at an expense report and just seeing disproportionate waste.
(3:17:07) And when I was looking at our spend at 37signals a while back, a few years back, I saw bills that did not pass my smell test. I remembered how much we used to spend on infrastructure before the cloud and I saw numbers I could not recognize in proportion to what we needed.
(3:17:27) The fact that computers had gotten so much faster over time, shouldn't things be getting cheaper? Why are we spending more and more money servicing more customers? Yes, but with much faster computers, Moore's Law should be lowering the costs and the opposite is happening. Why is that happening? And that started a journey of unwinding why the cloud isn't as great as the deal as people like to think that.
(3:17:49) - Yeah, can we look at the specifics just for people who don't know the story and then generalize to what it means about the role of the cloud and the tech business. So the specifics is you were using AWS S3 for... - We were using AWS for everything. HEY.com launched as an entirely cloud app. It was completely on AWS for compute, for databases, for all of it.
(3:18:15) We were using all the systems as they're best prescribed that we should. Our total cloud bill for Basecamp, our total spend with AWS was I think 3.2 million or 3.4 million at its peak. That's kind of a lot of money. 3.4 million, I mean we have a ton of users and customers, but still that just struck me as unreasonable.
(3:18:39) And the reason why it was so unreasonable was because I had the pitch for the cloud ringing in my ears. Hey, this is gonna be faster. This is gonna be easier. This is gonna be cheaper. Why are you trying to produce your own power, right? Like, do you have your own power plant? Why would you do that? Leave the computers to the hyperscalers. They're much better at it anyway. I actually thought that was a compelling pitch.
(3:19:03) I bought in on that pitch for several years and thought, do you know what? I'm done ever owning a server again. We are just gonna render our capacity and Amazon is gonna be able to offer us services much cheaper than we could buy 'em themselves because they're gonna have these economies of scale. And I was thinking Jeff's word ringing. My competitors margin is my opportunity. That was something he used to drive amazon.com with.
(3:19:27) That if he could just make 2% when the other guy was trying to make 4%, he would end up with all the money. And on volume, he would still win. So I thought that was the operating ethos for AWS. It turns out that's not true at all. AWS by the way operates its almost 40% margin.
(3:19:46) So just in that, there's a clue that competitors are not able to do the competitive thing we like about capitalism, which is to lower costs and so forth. So the cloud pitch in my optics, it's fundamentally false. It did not get easier first of all. I dunno if you've used AWS recently, it is hella complicated.
(3:20:10) If you think Linux is hard, you've never tried to set up IAM rules or access parameters or whatever for AWS. - AWS is always difficult. It was always complicated. - Well, I think it's gotten even more difficult. But yes, now some of that is it's difficult because it's very capable and you have a bunch of capacity on tap. And there are reasons I don't think they're good enough to justify how complicated the whole jing-a-ma-jing has become. But what's certainly true is that it's no longer easier.
(3:20:33) It's not easier to use AWS than it is to run your own machines, which we learned when we pulled out the cloud and didn't hire a single extra person. Even though we operate all our own hardware, the team stayed exactly the same. So you have this three-way pitch, right? It's gonna be easier, it's gonna be cheaper.
(3:20:51) Certainly wasn't cheaper. We've just proved that by cutting our spend on infrastructure by half to two thirds. And it's gonna be faster. The last bit was true, but way too many people overestimated the value of that speed. If you need a thousand computers online in the next 15 minutes, nothing beats the cloud.
(3:21:16) How would you even procure that? If we just need another 20 servers, it's gonna take a week or two to get boxes shipped on pallets delivered to a data center and unwrapped and racked, and all that stuff, right? But how often do we need to do that? And how often do we need to do that if buying those servers is way, way cheaper so we get vastly more compute for the same amount of money.
(3:21:33) Could we just buy more servers and not even care about the fact that we're not hyper optimized on the compute utility? That we don't have to use things like automatic scaling to figure things out because we have to reduce costs? Yes, we can. So we went through this journey over a realization in early 2023 when I had finally had enough with our bills.
(3:21:58) I wanted to get rid of them. I wanted to spend less money. I wanted to keep more of the money ourselves. And in just over six months, we moved seven major applications out of the cloud in terms of compute caching databases to works onto our own servers. A glorious, beautiful new fleet bought from the king of servers, Michael Dell, who really, by the way, is another icon on my, I saw he just celebrated 41 years in business.
(3:22:29) 41 years this man has been selling awesome servers that we've been using for our entire existence. But anyway, these pallets arrive in a couple of weeks and we rack 'em up and get everything going. And we were out. At least with the compute part. We then had a long multi-year commitment to S3 because the only way to get decent pricing in the cloud, by the way, is not to buy on a day-to-day basis, not to rent on a database basis, but to bind yourself up to multi-year contracts with computes often a year.
(3:22:58) That was in our case and with storage is was four years. We signed a four-year contract to store our petabytes of customer files in the cloud, to be able to get something just halfway decent affordable. So all of these projects came together to the sense that we're now saving literally millions of dollars, projected about 10 million over five years. It's always hard.
(3:23:22) How do you do the accounting exactly, and TOC this, that and the other thing. But it's millions of dollars. But it's not just that. It's also the fact that getting out of the cloud meant returning to more of an original idea of the internet. That the internet was not the sign such that three computers should run everything.
(3:23:42) It was a distributed network such that the individual nodes could disappear and the whole thing would still carry on. DARPA had designed this such that the Russians could take out Washington and they could still fight back from New York, that the entire communication infrastructure wouldn't disappear because there was no hub and spoke. It was a network. I always found that an immensely beautiful vision.
(3:24:02) That you could have this glorious internet and no single node was in control of everything. And we've returned to much more of a single node controlling everything idea with these hyperscalers. When US-East-1, the main and original region for AWS goes offline, which has happened more than a few times over the years. Seemingly a third of the internet is offline.
(3:24:25) Like that in itself is just an insult to DARPA's design. It just detract from the fact that what AWS built was marvelous. I think the cloud has moved so many things so far forward, especially around virtualization, automation setup. It's all those giant forward for system administration that's allowing us now to be able to run things on-prem in a way that smells and feels much like the cloud just at half the cost or less, and with the autonomy and the satisfaction of owning hardware.
(3:24:59) I don't know what the last time you looked at like an actual server and took it apart and looked inside of it. These things are gorgeous. I mean, I posted a couple of pictures of our racks out in the data center and people always go crazy for 'em because we've gotten so abstracted from what the underlying metal looks like in this cloud age that most people have no idea. They've no idea how powerful a modern CPU is.
(3:25:23) They have no idea how much RAM you can fit into a one U rack. Progress in computing has been really exciting, especially I'd say in the last four to five years after TSMC with Apple's help really pushed the envelope. I mean, we kind of sat still there for a while while Intel was spinning their wheels going nowhere.
(3:25:47) And then TSMC with Apple propelling them really moved things forward. And now servers are exciting again. Like you're getting jumps year over year and the 15, 20% rather than the single digit we were stuck with for a while. And that all means that owning your own hardware is a more feasible proposition than it's ever been.
(3:26:05) That you need fewer machines to run ever more and that more people should do it. Because as much as I love Jeff and Amazon, like he doesn't need another whatever, 40% margin on all the tech stuff that I buy to run our business. And this is just something I've been focused on. Both because of the ideology around honoring DARPA's original design, the practicality of running our own hardware, seeing how fast we can push things with the latest machines and then saving the money.
(3:26:38) And that has all been so enjoyable to do, but also so counterintuitive for a lot of people because it seemed, I think for a lot of people in the industry that like we'd all decided that we were done buying computers, that that was something we would just delegate to AWS and Azure and Google Cloud, that we didn't have to own these things anymore. So I think there's a little bit of whiplash for some people that, "Oh, I thought we agreed.
(3:27:02) We were done with that." And then along come us and say, "Ah, do you know what? Maybe you should have a computer." - Is there some pain points to running your own servers? - Oh, plenty. There's pain points to operating computers of all kind. Have you tried just like using a personal computer these days? Half the time when my kids or my wife have a problem, I go like, have you tried turning it just off and on again. Computers are inherently painful to humans.
(3:27:25) Owning your own computer though kind of makes some of that pain worth it. There's a responsibility that comes with actually owning the hardware that to me, at least make the burden of operating that hardware seem slightly more enjoyable. Now there are things you have to learn, certainly at our scale too.
(3:27:43) I mean, we're not just buying a single computer and plugging into an ethernet. We have to have racks and racks with them and you gotta set it up with network cabling and there is some specialized expertise in that, but it's not like that expertise is like building nuclear rockets. It's not like it's not widely distributed.
(3:28:02) Literally the entire internet was built on people knowing how to plug in a computer to the internet, right? Oh, ethernet cable goes here, power cable goes here, let's boot up Linux. That's how everyone put anything online until 10, 12 years ago when the cloud sort of took over. So the expertise is there and can be rediscovered. You too can learn how to operate a Linux computer.
(3:28:21) - Yeah, and it's, you know, when you get a bunch of them, there's a bunch of flashing LEDs and it's just so exciting. - Oh, they're beautiful, calming, amazing. Computers are really fun. This is actually something I've gotten into even deeper after we moved out of the cloud. Now my next kind of tingle is that if you could move out of the cloud, can you also move out of the data center? Personal servers have gotten really scarily quick and efficient and personal internet connections rival what we connected data centers with
(3:28:55) just a decade or two ago. So there's a whole community around this concept of home lapping, which is essentially installing server hardware in your own apartment, connecting it to the internet, and exposing that directly to the internet. That harks back to those glorious days of the '90s when people building for the internet would host the actual website on their actual computer in the closet.
(3:29:20) And I'm pretty fired up about that. I'm doing a bunch of experiments. I've ordered a bunch of home servers from my own apartment. I marvel at the fact that I can get a five gigabit, five connection now, I think, do you know what five gigabit that could have taken Basecamp to multiple millions of MRR in the way that back then, I ran the whole business on a single box with 2004 technology or probably a hundred megabit cable.
(3:29:48) Like the capacity we have access to both in terms of compute and connectivity is something that people haven't readjusted to. And this happens sometimes in technology where progress sneaks up on you. This happened with SSDs. I love that, by the way. We designed so much of our technology and storage approach and database design around spinning metal disks that had certain seek rate properties.
(3:30:15) And then we went to NVMe and SSDs, and it took quite a while for people to realize that the systems had to be built fundamentally different now. That the difference between memory and disk was now far smaller when you weren't spinning these metal plates around with a little head that had to read off them.
(3:30:35) You were essentially just dealing with another type of memory. I think we're a little bit in that same phase when it comes to the capacity of new businesses to be launched literally out of your damn bedroom. - So you can get pretty far with a large user based with home labing? - Absolutely. - That's exciting. That's like the old school.
(3:30:53) That's really exciting, right? - It's bringing back the startup in the garage in the literal physical sense of the word. Now some of that is do we need to, you can get relatively cheap cloud capacity if you don't need very much. - Hell yes, we need to. I mean the feeling of doing that by yourself of seeing LED lights in your own home. I mean there's nothing like that.
(3:31:18) - There's just an aesthetic to it that I am completely in love with and I wanna try to push on. Now it's not gonna be the same thing as getting out of the cloud. I'm not sure our exit out of the cloud was not the exit out of the data center. We basically just bought hardware, shipped it to a professionally managed data center that we didn't even actually touch.
(3:31:36) This is the other misconception people have about moving out of the cloud. That we have a bunch of people who are constantly driving to a data center somewhere to rack new boxes and change dead RAM. That's not how things happen in the modern world at all. We have a company called Summit, previously Deft, that is what we call white gloves.
(3:31:55) They work in the data center when we need something like, "Hey Deft, can you go down and swap the dead SSD in box number six?" They do it. And what we see is akin to what someone working with the cloud would see. You see IP addresses coming online. You see drives coming online. It's not that different, but it is a whole heck of a lot cheaper when you are operating at our scale. And of course it is.
(3:32:18) Of course it's cheaper to own things if you need those things for years rather than it is to rent it. In no other domain would we confuse those two things that it's cheaper to own for the long duration than it is to rent. - There is some gray area, like I've gotten a chance to interact with the xAI team a bunch. I'm probably going back out there in Memphis to do a big podcast associated with the Grok release.
(3:32:40) And those folks, in order to achieve the speed of building up the cluster and to solve some of the novel aspects that have to do with the GPU, with the training, they have to be a little bit more hands on. It's a less white glove. - Oh, and I love that, right? They're dealing with a frontier problem and they're dealing with it not by renting a bunch of GPUs at a huge markup from their main competitor.
(3:33:02) They're going like, "No, screw that. We're gonna put a hundred thousand GPUs in our own tents," right? And build it in absolute record time. So I think if anything, this is testament to the idea that owning hardware can give you an advantage both at the small scale, at the medium scale and at the pioneer levels of computing.
(3:33:22) - By the way, you know, speaking of teams, that those are xAI, Tesla are large companies, but all those folks, I don't know what it is about, you said Jeff is really good at finding good people, at seeing strength in people. Like Elon is also extremely, I don't know what that is. Actually, I've never actually seen, maybe you could speak to that. He's good at finding greatness.
(3:33:48) - I don't think he's finding as much as he's attracting. He's attracting the talent because of the audacious of his goals and his mission. The clarity by which he states it. He doesn't have to go scour the earth to find the best people.
(3:34:08) The best people come to him because he is, talking about Elon here, one of the singular most invigorating figures. In both the same order of the universe here, haters and lovers, right? Like he's having such an impact at such a scale that of course he's gotta have literally millions of people think he's the worst person in the world and he's also gonna have millions of people thinking he's the greatest gift to humanity. Depending on the day. I'm somewhere in between.
(3:34:32) But I'm more on the greatest gift to humanity end of the scale than I'm on the other end of the scale. And I think that really inspires people in a way that we've almost forgotten that that level of audacity is so rare that when we see it, we don't fully know how to analyze it. We think of Elon as finding great talent and I'm sure he is also good at that.
(3:34:52) But I also think that this beacon of the mission, we're going to fucking Mars. We're gonna transform transportation into using electricity. We're gonna cover the earth in internet is so grand that there are days where I wake up and go like, what the fuck am I doing with these to-do lists? Like Jesus, should I go sign up for something like that? Yeah, that sounds invigorating in a sense.
(3:35:22) I can only imagine like a biking back in 10, 50 going like, should we go to Normandy? You may die along the way, but oh boy, does that sound like a journey and an adventure. - There's a few components. There's one definitely this bigger than life mission and really believing it. You know, every other sentence is about Mars, like really believing it. It doesn't really matter what, like anybody else, the criticism, anything. There's a very singular focused big mission.
(3:35:49) But I think it also has to do a bunch of the other components. Like being able to hire well once the people, once the beacon attracts. And I've just seen people that don't necessarily on paper have a resume with the track record. I've seen really who now turned out to be like legendary people. He basically like tosses 'em the ball of leadership.
(3:36:14) Sees something in them and says like, "You go." And gives them the ownership and they run with it. And that happens at every scale. That there's a real meritocracy. And like there's something, there's just like, you could see the flourishing of human intellect in these meetings, in these group getting together where they're like, the energy is palpable. It's like exciting for me to just be around that.
(3:36:41) There's not many companies I've seen that in because when a company becomes successful and larger, it somehow suffocates that energy. I guess you see in startups at the early stages. But like it's cool to see it at a large company that's actually able to achieve scale, you know? - I think part of the secret there is that Elon actually knows things.
(3:37:08) And when you know things, you can evaluate the quality of work products. And when you can evaluate the quality of work products, you can very quickly tell who's full of shit and who will actually take you to Mars. And you can fire the people who's full of shit and you can bet on the people who get us to Mars.
(3:37:27) That capacity to directly evaluate the competency of individuals, it's actually a little bit rare. It's not widely distributed amongst managers, hiring managers. It's not something you can easily delegate to people who are not very skilled at the work itself. And Elon obviously knows a lot about a lot and he can smell who knows stuff for real.
(3:37:52) And is this at our tiny scale, something I've tried to do in the same order where when we hire programmers, for example, it's gonna be interesting now with AI as the new challenge. But up until this point, the main pivot point for getting hired was not your resume, was not the schooling you've had, it was not your grades, was not your pedigree, it was how well you did on two things.
(3:38:18) A, your cover letter. Because I can only work with people remotely if they're good writers. So if you can't pen a proper capital letter and can't bother to put in the effort to write it specifically for us, you're out. Two, you have to be able to program really well to the degree that I can look at your code and go like, yeah, I wanna work with that person.
(3:38:41) Not only I wanna work with that person, I wanna work on that person's code when I have to see it again in five years to fix some damn bug. So we're gonna give you a programming test that simulates the way we work for real and we're gonna see how you do. And I've been surprised time and again where I thought for sure this candidate is a shoe-in, they sound just right, the CV is just right and then you see the code getting turned in. I'm like, no way. No way are we hiring this person.
(3:39:05) And the other way has been true as well. I've go like, I don't know about this guy or this woman. Eh, I dunno. And then they turn in their coach stuff and I'm like, holy shit, can that person be on my team tomorrow, preferably? The capacity to evaluate work product is a superpower when it comes to hiring.
(3:39:27) - There's a step that I've seen Elon do really well, which is be able to show up and say, this can be done simpler. - [DHH] Yes. - But he knows what he's talking about. And then the engineer, because Elon knows enough, the engineer who's first reaction, you can kind of tell like, it's almost like rolling your eyes if your parent tells you something. This is not, no, we've I've been working on this for a month, you don't...
(3:39:52) But then when you have that conversation a little more, you realize no, it can be done simpler. Find the way. So there's a good, when two engineers are talking, one might not have perfect information. But if the senior engineer has like good instinct that's like been battle earned, then you can say simplify. And it actually will result in simplification.
(3:40:17) - And I think this is the hallmark of the true greats. That they not only have the insight into what's required to do the work, but they also have the transcendent vision to go beyond what the engineer would do, the programmer would do. I think if we are looking at these rarities, obviously the myth of Steve Jobs was also this.
(3:40:42) Even though perhaps he was less technical than Elon is in many ways, he had the same capacity to show up to a product team and really challenge them to look harder for the simplification or for making things greater in a way that would garner disbelief from the people who are supposed to do it. Like this guy is full of shit.
(3:41:02) Like this is crazy. We can never, and then two months later it is. So there is something of this where you need the vision, you need it anchored by the reality of knowing enough about what's possible, knowing enough about physics, knowing enough about software, that you're not just building bullshit. There are plenty of people who can tell a group of engineers. No, just do it faster. Like that's not a skill.
(3:41:26) It's gotta be anchored in something real, but it's also gonna be anchored in, it's a tired word, but a passion for the outcome to a degree where you get personally insulted if a bad job is done. This is what I've been writing about lately with Apple.
(3:41:47) They've lost that asshole who would show up and tell engineers that what they did was not good enough in ways that would actually perhaps make them feel a little small in the moment, but would spark that zest to really fix it. Now they have a logistics person who's very good at sourcing components and lining up production Gantt charts, but you're not getting that magic.
(3:42:12) Now what's interesting with that whole scenario was I actually thought how well Tim Cook ran things and has run things at Apple for so long that maybe we were wrong. Maybe we were wrong about the criticality of Steve Jobs to the whole mission. Maybe you could get away with not having it. I think the bill was going to come later and now it has.
(3:42:34) Apple is failing in all these ways that someone who would blow up Steve's ghost and really insult him would say like, see? This is what's happening now. So the other thing here too, of course, is it's impossible to divorce.
(3:42:53) Like your perception of what's a critical component of the system and the messy reality of a million different moving parts in the reality of life. And you should be skeptical about your own analysis and your own thesis at all time. - Since you mentioned Apple, I have to ask somebody in the internet submitted the question. Does DHH still hate Apple? I believe the question is.
(3:43:17) So there was a time when Basecamp went to war with Apple over the 30%. Can you tell the saga of that battle? - Yes, but first I'll tell you, I fell in love with Apple, which was all the way back in also early 2000s when Microsoft was dominating the industry in a way we now see Apple and Google dominate mobile phones. Microsoft was just everything when it came to personal computers, and I really did not like the Microsoft of the '90s.
(3:43:49) The Microsoft of the '90s was the cutoff the air supply to Netscape kind of characters was the Bill Gates sitting defiant in an interview with the DOJ asking about what the definition of what is. And just overall unpleasant, I think. You can have respect for what was achieved, but I certainly didn't like it.
(3:44:11) And as we've talked about, I came begrudgingly to the PC after Commodore fell apart and I couldn't continue to use the Amiga. So I already had a bit of a bone to pick with PCs just over the fact that I love my Amiga so much. But then in the early 2000s, Apple emerged as a credible alternative because they bet the new generation of Macs on Unix underpinnings.
(3:44:37) And that allowed me to escape from Microsoft, and suddenly I became one of the biggest boosters of Apple. I was in my graduating class at the Copenhagen Business School. I started with the first white iBook, first person using Mac. And by the time we were done in graduating, I had basically converted half the class to using Apple computers, 'cause I would evangelize them so hard and demonstrate them and do all the things that a super fan would do. And I continued that work over many years.
(3:45:07) Jason or I actually in I think 2004, 2005, did an ad for Apple that they posted on the developer side where we were all about like Apple is so integral to everything that we do and we look up to them and we are inspired by them. And that love relationship actually continued for a very long time. I basically just became a Mac person for 20 years. I didn't even care about looking at PCs.
(3:45:33) It seemed irrelevant to me, whatever Microsoft was doing, which felt like such a relief. Because in the '90s, I felt like I couldn't escape Microsoft and suddenly I had found my escape. And now I was with Apple and it was glorious and they shared so many of my sensibilities and my aesthetics and they kept pushing the envelope, and there was so much to be proud of, so much to look up to.
(3:45:57) And then that sort of started to change with the iPhone, which is weird because the iPhone is what made modern Apple. It's what I lined up in 2007 together with Jason for five hours to stand in the line to buy a first generation product where Apple staff would clap at you when you walked out the store. I don't know if you remember that.
(3:46:17) It was a whole ceremony and it was part of that myth and mystique and awe of Apple. So I wasn't in the market for other computers. I wasn't in the market for other computer ideas. I thought perhaps I'd be with the Mac until the end of days. But as Apple discovered the gold mine, it is to operate a toll booth where you don't have to innovate, where you don't actually even have to make anything, where you can just take 30% of other people's business.
(3:46:50) There was a rot that crept into the foundation of Apple and that started all the way back from the initial launch of the app store. But I don't think we saw at the time, I didn't see at the time just how critical the mobile phone would become to computing in general. I thought when the iPhone came out that like, oh, it's like a mobile phone. I've had a mobile phone since the early '90s.
(3:47:09) Well, it wasn't a mobile phone. It was a mobile computer. And even more than that, it was the most important computer or it would become the most important computer for most people around the world. Which meant that if you liked to make software and wanted to sell it to people, you had to go through that computer.
(3:47:27) And if going through that computer meant going through Apple's toll booth and not just having to ask them permission, which in and of itself was just an dignity. When you're used to the internet where you don't have to ask anyone permission about anything, you buy a domain and you launch a business and if customers show up, boom, you're success.
(3:47:45) And if they don't, well you're a failure. Now suddenly before you could even launch, you'd have to ask Apple for permission. That always sat wrong with me. But it wasn't until we launched HEY in 2001 that I saw the full extent of the rot that has snug into Apple's apple. - For people who don't know and we'll talk about it, HEY is this amazing email sort of attempt to solve the email problem.
(3:48:15) - Yes, I like to pitch it as what Gmail would've been with 20 years of lessons applied in a way where they could actually ship. Gmail was incredible when it launched in 2004. And it still is a great product but it's also trapped in its initial success. You can't redesign Gmail today, it just has way too many users.
(3:48:39) So if you want fresh thinking on email, I wanted fresh thinking on email. I needed to build my own email system. And not just my own email client. That's what a lot of people have done over the years. They build a client for Gmail, but you're severely constrained if you don't control the email server as well. If you really wanna move the ball forward with email, you have to control both the server and the client.
(3:48:57) And that was the audacious mission we set out to do with HEY. And that was what's funny, I thought our main obstacle here would be Gmail. It's the 800 pound gorilla in the email space. Something like 70% of all email in the US is sent through Gmail. I think their world rates are probably in that neighborhood as well.
(3:49:20) They're just absolutely huge and trying to attack an enormous established competitor like that who's so actually still loved by plenty of people. And is free seems like a suicide mission. And it was only a mission we signed up for because we had grown ambitious enough after making Basecamp for 20 years that we thought we could tackle that problem. So I thought, hey, this is dumb.
(3:49:45) I would not advise anyone to go head to head with Gmail. That seems like a suicide mission. We're gonna try anyway, because you know what? If we fail it's gonna be fine. We're just gonna build a better email experience for me and Jason and the people at the company and our cat, and that'll be okay 'cause we're gonna afford to do so.
(3:50:03) But when we got ready to launch, after spending two years building this product, millions of dollars in investment to it. We obviously needed mobile apps. You're not gonna be a serious contender with email if you're not on a mobile phone and you need to be there with a native client.
(3:50:21) So we had built a great native client for both iOS and for Android. And as we were getting ready to launch, we submitted both of them to the app stores, got both of them approved on I think Friday afternoon for the iOS app. And we then went live on Monday. And we were so excited. Hey world, we've been working on this new thing, I'd love for you to check it out. And of course as with anything, when you launch a new product, there are some bugs.
(3:50:45) So we quickly found a few in the iOS client and submitted a new build to Apple. Hey, here's our bug faces, can you please update? And that's when on help broke loose. Not only were they not gonna prove our update, they said, oh wait a minute, we gave you permission to be in the app store, but I'm sorry that was a mistake.
(3:51:09) We see that you're not using our in-app payment system, which means that we don't get 30% of your business. You will have to rectify that or you can't be in the app store. And first I thought like, well it got approved already. We're running on the same model. We've run Basecamp on in the app store for a decade. If you're not signing up through the app and we're signing up our own customers on our own website and they're just going to the app store to download their companion app, we're gonna be fine.
(3:51:34) That was the truth, right? That was why I never got so fired up about the app store. Even as Apple started tightening the screws was like my business was okay. Now suddenly my business wasn't okay. Apple was willing to destroy HEY if we did not agree to give them 30% of all the signups that came through the iOS app.
(3:51:54) And it wasn't just about the 30%. It was also about splitting and not longer having a direct relationship with our customers. When you sell an app in the app store, you're not selling an app to a customer. You're selling an app to inventory at Apple, and then Apple sells an app to that customer. That customer has a purchasing relationship with Apple.
(3:52:20) So if you wanna give discounts or refunds or whatever, it's complete hell. If you want to easily support multi-platform that's complete hell if someone signs up for HEY on their iPhone and they wanna switch to Android. But that billing relationship, it's tied to Apple, it's complete hell. For a million reasons, I did not want to hand my business over to Apple.
(3:52:38) I did not want to hand 30% of our revenue over to Apple. So we decided to do something that seemingly Apple had never heard before. We said no, we're not gonna add the in-app payment. I don't care if you're threatening us. this is not fair, this is not reasonable, please approve. And of course they didn't and it escalated.
(3:53:05) And after a couple of days we realized, you know what? This isn't a mistake. This isn't going away. We're gonna be dead if they go through with this. If we're not gonna yield and give them the 30%, they're gonna kick us off unless we make such a racket such noise that they will regret it. And that's exactly what then happened. We were blessed by the fact that we launched HEY one week before the WWDC, their Worldwide Developer Conference.
(3:53:39) Where Apple loves to get up on stage and harp on how much they do for developers, how much they love them and why you should bill for their new devices and so on and so forth. And then we also just happened to have a platform on the internet, which is very convenient when you need to go to war with a $3 trillion company. So I started kicking and screaming - Oh boy.
(3:53:56) - And essentially turning it up to 11 in terms of the fight and going public with our or denial to be in the app store. And that turned into a prolonged two week battle with Apple that essentially ended in the best possible outcome we could have gotten as David fighting Goliath, which was a bit of a truce. We wouldn't hand 30% over to Apple.
(3:54:20) They do wouldn't kick us out of the app store, but we had to build some bullshit dummy account such that the app did something when you downloaded it. That was a rule that Phil Schiller seemingly made up on the fly when pressed for the fifth time by the media about why we couldn't be in the app store when a million other companion apps could.
(3:54:42) But we just happened to be able to create so much pain and noise for Apple that it was easier for them to just let us be than to keep on fighting. - What do you think about Tim Sweeney's victory with Epic over Apple? - I think it is incredible and the entire developer ecosystem. Not just on iOS but on Android as well, owe Epic, Tim Sweeney and Mark Rein, an enormous debt of gratitude for taking on the only battle that has ever inflicted a series wound on Apple in this entire sorted campaign of monopoly enforcement.
(3:55:21) And that is Epic's fight versus them. Tim recently revealed that it has cost well over a hundred million dollars in legal fees to carry on this battle against Apple. We, for a hot moment, considered suing Apple when they were threatening to kick us out. We shopped the case around with a few law firms and perhaps of course they would tell us you have a good case.
(3:55:47) I mean they're trying to sell a product here, but they would also tell us. - It's gonna cost a minimum of $10 million and it's gonna take five to seven years through all the appeals. Now we now learned the actual price tag was 10 times higher, right? Epic spend over 100 million. It would've destroyed us to take on Apple in the legal realm. Only a company like Epic could do it and only a company run by founders Like Tim, like Mark could risk the business in the way that they did, the audacity they had to provoke the fight in the first place, which I thought was just incredible.
(3:56:23) And to stick with it for the long term, no board would've signed off on this lawsuit to a professional CEO, no freaking way. So the fact that they've been able to beat Apple in also the most hilarious way possible, I think it's just incredible. 'Cause remember their first victory in the case was actually not much of a victory. There were about 11 counts in the trial.
(3:56:49) Apple basically won 10 of them and the judge awarded Epic this one little win that Apple couldn't tell 'em not to link out to the internet to be able to do the payment processing. So they won this one little thing. And Apple, instead of just taking the 10 out of 11 wins and going fine, you can have your little links but all these other rules stay in place decided to essentially commit criminal contempt of court as they've now been referred to for prosecution and angered the judge to such a degree that the rule of law in the US now
(3:57:30) is that you can launch an app in the app store. And you don't have to use in-app payment, but you can have a direct billing relationship with a customer if you just link out to the open internet when you take the credit card and then hop back into the app. And we owe all of that to Tim and Mark. We owe all of that to Epic.
(3:57:49) We're gonna launch new apps any minute now, I hope actually next week to take advantage of this that revamp the hey apps so that people who download the Hey app off the Apple app store can sign up in the app and can then use the web to put in their credit card so we don't have to pay 30%.
(3:58:11) Instead we have a direct billing relationship and such that they can take that subscription to Android, to PCs, whatever without any hassle. And we have Tim and Mark to thank for it. - Yeah, Tim, I mean, like you said founders but also specific kind of founders. 'Cause I think maybe you can educate me on this, but Tim is somebody who maintains. to this day sort of the unreasonableness of principles. Yes, that's what I love. I think sometimes maybe even with founders you can get worn down. It's a large company.
(3:58:38) There's a lot of smart, quote, unquote, people around you lawyers and just whispering your ear over time and you're like, well just be reasonable being a, you know, this is a different thing. And to be the sort of maintain, I mean Steve Jobs did this. Still are the asshole. Who' says, "No, I'll sink this whole fucking company over this.
(3:59:03) " - That's the exact language basically I used in our original campaign. I will burn this business down before I hand over 30% of it to Apple. And that sort of indignation, that actual rage is something I try to be a little careful about tapping into because it is a little bit of a volatile compound.
(3:59:24) Because I mean I have a bunch of employees, we have a bunch of customers. It would be pretty sad if the journey of 37 signals after 25 years would come to an end because Apple would burn us down or I would burn the business down over this fight with Apple. But I think you also need that level of conviction to be able to even drive the day-to-day decisions.
(3:59:42) One of the other Apple examples, and I know we're racking on Apple a little bit here, and I don't actually hate them. I really don't. I am tremendously disappointed at the squandered relationship that did not need to be sold away for so little. Now I understand that the app store toll booth is actually a pretty big business. It's multiple billions, but Apple is a trillion dollar company.
(4:00:06) And I think in the lens of history, this is gonna come off as a tremendous mistake and I think it's already coming off as a tremendous mistake. The flop that was the Vision Pro was partly because Apple had pissed off every other developer. No one was eager to come build the kind of experiences for their new hardware that would perhaps have made it a success.
(4:00:31) So when you're on top and you have all the cards, you can dilute yourself into thinking that you can dictate all terms at all times and there are no long term consequences. Apple is learning finally, the fact that there are long-term consequences and that developers actually are important to Apple's business. And the relationship is not entirely one sided. We don't owe our existence to Apple and Apple alone.
(4:00:51) We've built our own customer bases. Apple has been beneficial to the industry. I'm glad the iPhone exists, da da da da. It's not that it doesn't go both ways, but Apple wants it only one way. And I think that is a mistake, and it's a mistake that was avoidable. And A, that's disappointing. Certainly disappointing for me.
(4:01:22) I've literally spent 20 years evangelizing this shit, right? I've spent so much money buying Apple hardware, excusing a bunch of things they've done over the years. And then for what? For the fact that you wanted 30% of something that I created in the most unreasonable way possible? Couldn't we have found a better way to do this? I think they're going to get forced to do a better way. But did you also have to go through the indignity of having a criminal contempt charge against you getting referred to prosecution? It just seems so beneath Apple, but it also seems so in line with what happens to huge companies who are run by, quote, unquote, "professional managers"
(4:01:58) rather than founders and unreasonable people. - Well, we should probably also say that the thing you love about Apple, the great spirit of Apple I think still persists and there's a case to be made that this 30% thing is a slice, a particular slice of a company, not a defining aspect of the company. And that it Apple is still on top in the hardware that it makes and a lot of things that it makes.
(4:02:23) And you know this is, that could be just a hiccup in a long story of a great company that does a lot of awesome stuff for humanity. So like Apple is a truly special company. We mentioned Amazon, there is no company like Apple. - I agree, this is why the disappointment is all greater.
(4:02:50) Because we had such high aspirations and expectations to Apple that they were the shining city on the hill and they were guiding the industry in a million positive ways. I think as we talked about earlier, hardware is exciting again in large part because Apple bought PA Semi and pursued a against all odds mission to get ARM up to the level it is today. And we have these incredible M chips now because of it.
(4:03:17) And the design sensibilities that Apple bring to the table are unparalleled. No one has taste certainly at the hardware level like Apple does, even at the software level. I'd say there's a lot of taste left in Apple, but there's also some real sour taste now.
(4:03:37) So they have to wash that off first I think before they find their wear back. But Apple's been in a mirage before, I mean Wozniak and Steve Jobs started this thing in the garage, has great success with the Apple II. He hands the company over to a sugar drink salesman who tanks the company into the '90s. He doesn't learn the lesson, spends the next 20 years building up this amazing company, then hands the company over again to a logistics person who presumably had more redeeming qualities than the first guy who put in charge, but still ends up leading the company astray. Now this is the norm.
(4:04:16) The norm is that great companies don't last forever. In the long arc of history, almost no company lasts forever. There are very few companies around that was here 100 years ago, even fewer 200 years ago. And virtually nothing that are 1000 years old outside of a handful of Japanese sorts makers or something like that, right? So you can get diluted into thinking that something is forever when you're in the moment.
(4:04:42) And they seem so large. Apple could absolutely stumble and I think they have more reason to stumble now than ever. They're behind on AI, terribly behind. Their software quality is faltering in a bunch of ways. The competition is catching up on the hardware game in part because TSMC is not an Apple subsidiary, but a foundry that services a MD and NVIDIA and others who are now able to use the same kind of advanced processes.
(4:05:11) This is something I learned after not looking at PC hardware for the longest time, that holy smokes, AMD actually makes CPUs that are just as fast, if not faster than apples. They're not quite as efficient yet because ARM has some fundamental efficiencies over x86, but they're still pretty good. So Apple should have reason to worry. Apple's shareholders should have reason to be concerned.
(4:05:34) Not just about all these stumbles, but also by the fact that Apple is run by old people. Apple's board has an average age of, I think, 75. Their entire executive team is above 60. Now that sounds horribly ageist. And in some ways, it a little bit is. In the same way, I'm ageist against myself.
(4:06:02) Like I'm 45 now and I sort of kind of have to force myself to really get into AI because it is such a paradigm shift and a lot of people when they reach a certain age are just happy to stay with what they know. They don't wanna go back to being a beginner. They don't wanna go back to having to relearn everything. And I think like, ah, this is a little hard for me at 45.
(4:06:23) How the hell do you do that at 75? - I have to come back to, you mentioned it earlier, you're a parent. Can you speak to the impact that becoming a father has had on your life? - I think what's funny about fatherhood is that for me, I wasn't even sure it's something I wanted. It took meeting the right woman and letting her convince me That this was the right idea before we even got started.
(4:06:50) I didn't have starting my own family on the list of priorities in my late 20s or even early 30s. It was really the impetus of meeting my wife Jamie and her telling me this is what I want. I wanna have a family. I wanna get married. I wanna have kids. I wanna have three. And me going for a second like whoa, whoa, whoa.
(4:07:19) And then eh, all right, let's do it. And I think that's the kind of happy accident where some parts of my life have been very driven, where I knew exactly what I wanted and how to push forward to it and what the payoff was going to be. But when it comes to having a family, that always felt like a very fuzzy abstract idea that sure, someday, maybe.
(4:07:49) And then it became very concrete because I met a woman who knew what she wanted. And looking back on it now, it almost seems crazy. Like there's this fork in the road of reality. Where if that hadn't happened and I had been sitting here now, not being a father, not having a family, the level of regret knowing what I know now about the joys of having that family would've been existential, would've been...
(4:08:28) I don't know if they would've been devastating. I think men have a little bit of a longer window to pursue these things than women do. There are just certain biological facts. But ending up with the family I have now, ending up with my three boys have been just a transformative experience in the sense that here's something that turned out to be the most important thing. And it was an open secret.
(4:08:56) Not even an open secret. It was an open truth through all of history. You listen to anyone who's ever had children, they will all say, "My children are the most important to me." Yet somehow that wisdom couldn't sink in until you were in the situation yourself. I find those truths fascinating when you can't actually relay them with words.
(4:09:20) I can tell you, Hey Lex, what are you doing? Get a wife, make some kids, get a move on it. And these are just words. They're not communicating the gravity of what it actually feels to go through the experience. And you can't really learn it without going through it. Now of course you could be influenced and whatever, we can all help contribute and little sparks and little seeds can grow in your your mind about it.
(4:09:41) But it still has to happen. And now that I am in this situation and just the sheer joy on a daily basis where you think your level of life satisfaction is on a scale of one to 10, and then the satisfaction of seeing your children understand something, accomplish something, learn something, do something, just be.
(4:10:07) Just goes like, oh my god, the scale doesn't go from one to 10. It goes from one to 100. And I've been playing down here in the one to 10 range all this time. And there's a one to 100 that has been humbling in a way that is impactful in and of itself. This whole idea that I thought I had a fair understanding of like the boundaries of life in my early 30s, like what is this about? I mean, I've been on this earth long enough now here to know something. And you realize, I don't know, I did not know.
(4:10:41) I did not know that the scale was much broader. And I've often talked about joys of having kids and just seeing your own DNA, which is remarkable to me because literally that's been the pursuit of humans since the dawn of time.
(4:11:08) I am here today because whatever, 30,000 years ago, some Neanderthal had the same realization that I should procreate and I should continue my bloodline. And that that all amounts to me sitting here now. But it didn't become a practical reality to me before meeting the right woman. And I think that that's sometimes not part of the conversation enough that there's something broken at the moment about how people pair up in the western world.
(4:11:35) And it's at the source of why we're not having enough children because there's not enough couples, there's not enough marriage. There's not a lot of these... There's not enough of all these traditional values that even 50, 60, 70 years ago was just taken for granted. We're in this grand experiment of what happens if we just remove a bunch of institutions? What happens if we no longer value marriage as a something to aspire to? What happened if parenthood is now seen in some camps? It's almost something like weird or against your own self-expression. It's a grand experiment
(4:12:13) that I'm kind of curious how it turns out. I prefer to watch it as a movie like the "Children of Men," I'm like, that was a good show. I kind of wish that wasn't reality. But we're seeing that reality play out while I'm sitting here in a very traditional two parent loving household with three children, and going, this is now at the top. I've done a lot of things in my life.
(4:12:40) I've built software, I've built companies, I've raced cars, I've done all sorts of things, and I would trade all of it in a heartbeat for my kids. That's just a really fascinating human experience that the depth of that bond is something you can't appreciate before you have it. But I also think there is a role to play, to talk it up.
(4:13:06) Because we're being bombarded constantly with reasons why not to. Oh, it's too expensive. Well, you could get divorced and then you might lose half. There's all these voices constantly articulating the case against marriage, the case against having children that those of us who've chosen to do the traditional thing to get married and to have children have an obligation to kind of talk it up a little bit, which would've seen ridiculous again 50 years ago that you'd have to talk up something so fundamental of that.
(4:13:42) But I have become kind of obligated in that sense to do just that. To talk it up, to say, you know what? You can look at everything that I've done and if you like some of those parts realize that to me in this situation, the kids, the family, the wife is more important than all of it. And it sounds like a cliche because you've heard it a thousand times before.
(4:14:07) And by becoming a cliche, maybe you start believing it's not true that it's just something people say, but it is reality. I know almost no parents that I have personal relationships with that don't consider their children to be the most important thing in their life. - So there's a lot of interesting things you said.
(4:14:28) So one, it does seem to be, I know a lot of parents, perhaps more interestingly, I know a lot of super successful people who are parents who really love their kids and who say that the kids even help them to be more successful. Now the interesting thing, speaking to what you're saying is it does seem for us humans, it's easier to articulate the negatives because they're sort of concrete, pragmatic, you know, it costs more, it takes some time, you know, they can be, you know, crying all over the place. They're, you know, tiny narcissists running around or whatever.
(4:15:08) - Which is all true. - Yeah, pooping everywhere, that kind of stuff. But to articulate the thing you were speaking to of there's this little creature that you love more than anything you've ever loved in your life. It's hard to convert that into words. You have to really experience it.
(4:15:27) But I believe it and I want to experience that, but I believe 'cause just from a scientific method, have seen a lot of people who are not honestly not very capable of love, fall completely in love with their kids. Like, you know, very sort of... let's just call it what it is, engineers that are very like boop bop. They just fall in love.
(4:15:52) And it's like, all right, people who just like you said, they don't really want, they don't really care or don't really think about having kids that kind of stuff once they do it changes everything. So, you know, but it's hard to convert it into words. - One of the reasons I think is also difficult is, I mean, I like kids. Not that I actively disliked them, but when I was around other people's kids, I didn't have a emotional reaction. Some women have, right? They see a baby and they go, "Oh.
(4:16:19) " I never had any emotion of that. I mean, I could appreciate, I'm glad for you that you have children. It did not provoke anything in me. The emotions that are provoked in me when I look at my own children, this doesn't exist in the same universe.
(4:16:36) So you don't have something, you don't have a complete parallel, or at least a lot of men, or at least me, I didn't have sort of a framework to put it into what would it be like to have my own child? And then you experience it. It's like the poof. And it happens so quickly too. This is what I found fascinating. It happens before that little human is even able to return any words to you that the love you develop to an infant, it happens quite quickly.
(4:17:01) Not necessarily immediately, I don't know, different people have different experiences, but it took me a little bit. But then once it hit, it just hit like kick of a horse. And I love that it's also just such a universal experience. That you can be the most successful person in the world. You can be the poorest person in the world. You can be somewhere in the middle.
(4:17:24) And we share this experience that being a parent, for most of them, turns out to be the most important thing in their life. - But you know, it is really nice to do that kind of experience with the right partner. But I think because I'm such an empath, the cost of having the wrong partner is high for me.
(4:17:44) But then I also like realized, man, I have a friend of mine who's divorced happily and he still loves the shit out of his kids. And it's still beautiful. It's a mess, but there's all of that love is still there and it's, you know, you just have to make it work. It's just that, I don't know, that kind of like divorce would destroy me.
(4:18:03) - You should listen to The School of Life. He has this great bit on YouTube. You'll marry the wrong person. If you accept upfront that you will marry the wrong person, that every potential person you can marry is gonna be the wrong person on some dimension. They're gonna annoy you. They're gonna be not what you hoped in certain dimensions.
(4:18:25) The romantic ideal that everything is just perfect all the time is not very conducive to the reality of hitching up and get making babies. Because I think as you just accounted, even when it turns to shit, I find that most of the people I personally know where things have fallen apart and have turned to shit, never in a million years would they go like, "I regret it.
(4:18:51) " "I would rather, my children did not exist because a relationship turned sour." I mean, I think you should try very hard. And I think this is also one of those things where we didn't fully understand those fences. And when we pulled them up and celebrated how easy it is to get divorced, for example, that that wasn't gonna have some negative consequences.
(4:19:12) I'm not saying you shouldn't have divorces. I'm not saying return to times past. I'm saying though that civilization over thousands of years developed certain technologies for ensuring the continuation of its own institutions and its own life that perhaps we didn't fully appreciate.
(4:19:34) I mean, again, this is something Jordan Peterson and others are far more articulate to speak about, and that I've learned a lot to just analyze my own situation. Why is it that this incredible burden, it is to be responsible for someone else's life that you brought into this world is also the most rewarding part of existence? That's just curious.
(4:20:03) Before I heard Peterson articulate the value of taking on the greatest burden you know how to carry, I always thought about burdens as a negative things. Why would I want the burden of a child? I might screw it up. I might be a bad parent. They might have bad... all this stuff, right? All the reasons why you shouldn't. And so few voices articulating why you should. - Yeah, but there I should also add on top of that thing you mentioned currently, perhaps in the west, the matchmaking process is broken. Is broken and technology made it worse. Yeah, it's fascinating.
(4:20:32) This whole thing that that hasn't been solved. So hiring great teams, that's probably been solved the best out of matchmaking. Finding great people to hire. Second, finding great friends. That's like, that's also hasn't been solved. - And it's breaking down. - It's breaking down. And third is matchmaking for like relationships. That's like the worst.
(4:20:56) And in fact, technology made it even worse. It's fascinating. - It is. It's a great example again of how all the greatest intentions still led us straight to hell. I really enjoyed Louis Perry's analysis of the sexual revolution not being an unqualified good, which was something I hadn't thought about at all before she articulated it.
(4:21:22) That of course women should be able to have freedom and self determination and abortions and all of these things. And Louise Perry is not arguing against that either, of course. But there are second order facts that we don't appreciate at the time and we may not have ready-made solutions for. And that's just interesting. You make life better in a million different ways and somehow we end up more miserable.
(4:21:46) Why is that? Why is it that humans find meaning in hardship? And I think some of that is that it's a difficult question to answers through science. And again, Peterson articulates well this idea that you have to find some of it through art, some of it through authors, some of it through different... I was just about to say modes of knowing before I stopped myself, because that sounds like woo bullshit.
(4:22:20) But there are different ways to acquire those deep lessons that sort of paper is not gonna tell you. - I mean, this is really, the point also applies to religion, for example. If you remove from society the software of religion, you better have a good replacement. - And we've had a bunch of bad replacements, especially over the last few decades.
(4:22:51) Religion is one of those things I've struggled with a lot because I'm not religious. I sort of wish I was. I can now fully appreciate the enormous value having an operating system like that brings not just at the individual level, but rather at a societal level. And it's not clear at all what the answer is.
(4:23:14) I think we've tried a lot of dead ends when it came to replacements and people have been filling that void in a million different ways that seem worse than all the religions, despite their faults in a myriad of ways have been able to deliver. - Yeah, religions like the cobalt code. I
(4:23:38) t's just... - Yes, it's the institutions where we don't fully understand the rules and why they're there and what's gonna happen if we remove them. Yeah, some of them seems obvious to me are just bullshit of the time. Oh, you should need whatever shellfish, because in that region of the world. There was something, something, something, okay, fine. But there's a bunch of other things that are pivotal to keeping society functioning for the long term, and we don't fully understand which is which. What's the bullshit and what's the load-bearing pillars of society.
(4:24:04) - Can you speak to the hit on productivity that kids have? Did they increase your productivity, decrease it, or is that even the wrong question to ask? - I think it's one of the reasons why ambitious people are often afraid of having children because they think I have so much more to do and I barely have enough time now.
(4:24:29) How would I possibly be able to accomplish the things I wanna accomplish if I add another human into the mix? Now, A, we've always worked 40 hours a week, not 80 or 100 or 120. I think that's very beneficial. B, kids don't exist in this vacuum of just them alone being entered into your life. Hopefully there's a partner. And in my life, I'm married to a wonderful woman who decided to stop working her corporate job when we got together and have been able to carry a huge part of that responsibility. I was just about to say burden.
(4:25:04) And I think that's exactly how it often gets presented, especially from a feminist perspective, that carrying for your own children is some sort of unpaid labor that has to be compensated for in some specific way beyond the compensation of what bringing life into this world, racing wonderful humans. There's something screwy about that analysis that I actually think the modern trad movement is a reply against. Whether they have all the answers, I'm certainly not sure of either.
(4:25:41) But there's something that's just not right in the analysis that children are a burden and that if woman chooses to stay at home with the kids, that that some sort of failure mode of feminist ambition. I think that's actually a complete dead end. Now depends on different people, different circumstances.
(4:26:06) I can just speak to my life being married to a wonderful woman who have decided to be home with the kids, at least at their early age and taking on a lot of those responsibilities. Now it doesn't mean there isn't plenty of ways that I have to be part of that and have to chip in, but it's allowed me to continue to work the 40 hours a week that I've always worked.
(4:26:30) But it's made the 40 hours more strict. I have a schedule where I wake up, whatever, 6:30 and we have to get out of the door a little before 8:00. I usually have to play at least one or two rounds of "Fortnite" with my youngest and sometimes middle child. Then take the kids to school, get in, start work at, I don't know, 8:39. Didn't work until 5:00, 5:30, sometimes six o'clock.
(4:26:59) But then it's dinner and I have to be there for that. And then I have to read to the kids and by the time that's done, I don't want to go back to work. So my work time really is 9:00 to 5:00, 9:00 to 6:00, depending of whatever is going on. Sometimes there's emergencies and you have to tend to them, but it's made it more structured.
(4:27:19) And I found some benefit in that and I've found some productivity in that, that I can't goof around quite as much that the day will end at around 5:36. That's just if I didn't accomplish what I wanted to do today, if I get to that time, it's done. I'm over, I have to try again tomorrow. Whereas before having a family and before having kids, I could just like not do it and just make it up in the evening.
(4:27:49) So in that way it's made me more structured, but it hasn't really changed my volume of work all that much. I still work about the same amount of hours. And that's, by the way, enough. This is one of the key points we make in. It doesn't have to be crazy at work. The latest book we wrote is that there's enough time. 40 hours a week is actually a ton if you don't piss it away. Most people do piss it away. They piss it away in meetings.
(4:28:14) They piss it away on just stuff that doesn't matter when even three hours, four hours of concentrated uninterrupted time every day would move to goals they truly care about way down the field. - I think kids do make you more productive in that way for people who need it, especially people like me. They create their urgency. Lik if you have to be done by 5:00, it's a maybe counterintuitive notion, but for people like me who like to work, you can really fill the day with fluff of work.
(4:28:50) And if you have to be done by 5:00, you're going to have to do the deep work and get it done. Like really focus singular work. And then you're just gonna cut off all the pressure. - It keeps you honest. It keeps you honest because you can squander one day, you can squander two days. But if I squander a whole week, I feel terrible.
(4:29:13) Now that's just some drive I have in me where I feel content and full meaning if I actually do stuff that matters, if I can look back upon the week and go like that was a nice week, really. We moved forward. Maybe we didn't get done, but we moved forward and everything got better. And I think kids really helped just time box things in that way.
(4:29:32) And a lot of people need that because I find just so much of the celebration of overwork to be so tiresome. Oh, I work 60 hours or 80 hours or 100 hours a week and just like, first of all, no, you don't. No, you don't. Like those 80 hours are full of all sorts of fluff that you label work, but that I would laugh at and that most people laugh at.
(4:29:58) That you would laugh at if you actually did the analysis of where's that time going. Most of the important stuff that has to be done is done in these uninterrupted chunks of two hours here or four hours there or five hours there. The hard part is making sure you get them in the whole piece. So don't give me that. There's time enough.
(4:30:23) And also what's so important that it ranks above continuing your lineage. I think there's just some ancient honor in the fact that, again, this DNA that's sitting on this chair traveled 30,000 years to get here and you're gonna squander all that away just so you can send a few more emails.
(4:30:47) - There is something that's also hard to convert into words of just the kind of fun you can have just playing with your kids. I don't know what that on the surface it's like, I can have that kind of fun just playing video games by myself. But no, it's like there's something magical about it, right? - I have a thousand hours logged in "Fortnite" since 19, I think, all of it with my kids. I'd never be playing "Fortnite." Well, I don't know if I never would be.
(4:31:13) I wouldn't be playing a thousand hours of "Fortnite" if it wasn't for my kids. The enjoyment for me is to do something with them that I also happen to enjoy. I really love "Fortnite." It's a phenomenal game. I don't have to force myself to play that with them. I often ask like, hey, do you wanna play "Fortnite?" But still, it's an activity that I get to share with them.
(4:31:32) It's a passion that I get to share with them. I've started doing go-karting with my oldest. I've been driving race cars for a long time and now they're getting into go-karting and just being at the go-kart track, seeing them go around, seeing them get faster, seeing them learn that skill, you just go look at like, what else would I be doing with my life? At my age, 45, I'm standing here truly enjoying life.
(4:31:58) I brought into this world. What else was so important at this stage that I would otherwise be spending my time on? - All right, like you mentioned, you like to race cars and you do it at a world class competitive level, which is incredible. So how'd you get into it? What attracts you to racing? What do you love about it? - The funny thing about getting into racing is I did not get my driver's license until I was 25.
(4:32:23) I grew up in Copenhagen, Denmark, where the tax on cars is basically over 200%. So you pay for three cars and you get one. And I didn't even have the money for one car, let alone three. So I could not afford a car growing up. We did not have a car growing up, but Copenhagen is a nice city to be able to get around on a bike or with a bus, or as I did for a long period of time, on roller blades.
(4:32:57) But when I was 25, I realized I wanted to spend more time in the US. I wasn't sure yet that I was gonna move there. That turned out later to be true. But I knew that if I wanted to spend time in the US, I needed to have a driver's license. I was not gonna get around very well if I didn't know how to drive a car. So I got a driver's license at 25.
(4:33:16) Then ended up moving to the US later that year and I'd always been into video games, racing video games, "Metropolitan Street Racer" on the Dreamcast was one of those games that really sucked me into. It was the precursor to Project Gotham, which was the precursor to essentially, Forza Horizon, I think. - Oh, okay, interesting. - I think that's how the lineage goes.
(4:33:39) Just a great game. I actually just fired it up on an emulator a few weeks ago. And it still sort of kind of holds up because it has enough real car dynamics that it smells a little bit like driving a real car. It's not just like an arcade racer like Sega Rally or something like that. But I'd always been into that.
(4:34:01) Then I got my driver's license at 25 and moved to the US, and then two years later, a friend that I'd met in Chicago took me to the Chicago Autobahn Country Club, which is this great track, about 45 minutes from Chicago. And I sat in a race car and I drove a race car for the first time. And I had the same kind of pseudo religious experience I did as when I started working on Ruby, where I did maybe 20 laps in this basically a Mazda race car from, I think, it was like the '90s or something, like a pretty cheap race car, but a real race car.
(4:34:36) Single seat, manual gearbox, but exposed slick wheels, all the stuff. And after having had that experience, first of all it was just the most amazing thing ever. Like the physical sensation of driving a race car is really unique. And I think if you've driven a car fast, you have a maybe a 2% taste of it.
(4:34:59) The exposure to the elements that you get in a single seat race car, especially one like that where your head is actually out in the elements, you can see the individual wheels and you sensation of speed is just so much higher is at a completely different level. - So can you actually speak to that? So even at that Mazda, so you can feel, what can you feel like the track reverberating you see? You feel the grip? - Oh yeah, not only can you see the bumps because you're literally looking straight at the wheel, you can feel all the bumps because you're running a slick tire.
(4:35:32) It's a really stiff setup. It's nothing like taking a fast street car out on a racetrack and try to driving a little bit around. - So can you feel like the slipping, the traction? - Yeah, you feel the slipping. hat's a huge part of the satisfaction of driving a race car is driving in at the edge of adhesion as we call it. Where the car's actually sliding a little bit.
(4:35:51) A couple of percent of slip angle is the fastest way to drive a race car. You don't wanna slide too much that looks great, lots of smoke, but it's not fast. How you want to drive it is just at the limit of adhesion where you're rotating the car as much as your tires can manage.
(4:36:09) And then slightly more than that and playing at it, keeping it just at that level. Because when you're at the level or at the limit of adhesion, you're essentially just a tiny movement away from spinning out. I mean, it doesn't take much. Then the car starts rotating. Once it starts rotating, you lose grip and you're going for the wall. That balance of danger and skill is what's so intoxicating.
(4:36:34) And it's so much better than racing video games too, because the criticality is taking up two notches. I often think about people who really like gambling, where I think like, aren't you just playing poker? Like no, the point is not poker.
(4:36:52) Pokers may be part of it, but the point is that I could lose my house, right? Like that's the addiction that some people get to gambling, that there's something real on the line. When you're in a race car, there's something very real on the line. If you get it wrong, at the very least, you're gonna spin out and properly hit a wall and it's gonna be expensive. At the very worst, you're not getting out alive.
(4:37:15) And even if modern race cars have gotten way safer than they used to be. There is that element of danger that's real. That there are people who still get seriously hurt or even killed in a race car. It's mercifully rare compared to what it used to be when those maniacs in the '60s would do Formula 1 and whatever, 13% of the grid wouldn't make it to the end of the year because they'd just die in a fiery flaming fireball. But there's still some of it there.
(4:37:43) And I think that since that there's something on the line really contributes to it. But it's more than that. There's just a physical sensation. There's activation of all your forces. There's the flow. And I think that really cements like why I got addicted, 'cause I always, I love that flow I got out of programming, but getting flow out of programming is a very inconsistent process.
(4:38:10) I can't just sit down in front of a keyboard and go like, all right, let's get the flow going. It doesn't happen like that. The problem has to be just right. It has to meet my skills in just a right moment. It's a bit of a lottery. In a race car is not a lottery at all. You sit down in that car, you turn the ignition, you go out on track, and I get flow virtually guaranteed.
(4:38:29) Because you need, or I need at least 100% of my brain processing power to be able to go at the speed I go without crashing. So there's no time to think about the other night or the meeting next week or product launch. It's completely zen in actually the literal sense of the word. I think of someone who's really good at meditation, that's probably kind of state they get into where it's just clear you're in the now, there's nothing but you and the next corner. That's a really addictive experience.
(4:39:02) So after I've had that, I couldn't get enough. I kept going to the track every opportunity I got. Every single weekend for about four years, I would go to the track. And by the end of that time, I'd finally worked up enough skill and enough success with the company that I could afford to go, quote, unquote, "real racing.
(4:39:20) " So I started doing that. I started driving these Porsches, and then as soon as I got into that, as soon as I got into, quote, unquote, "real competition," I was like, I wonder how far you can take this. And it didn't take that long before I decided, you know what? I can take this all the way. My great hero in racing is Tom Kristensen, fellow Dane.
(4:39:39) The Mr. Le Mans, as they call him. The greatest endurance race in the world. The 24 hours of Le Mans has been won more times than any other by Tom Kristensen. He won the race nine times. So Tom just really turned me onto Le Mans. I've been watching Le Mans since I think the '80s. I have my earliest memories of watching that on TV.
(4:40:02) The race has been going since I think 20s. But in the '80s I got kind of into it. And then in the late '90s, early 2000s when Tom started winning, I like pretty much every other Dane started watching the race almost religiously. So I thought, you know what? I wanna get to Le Mans.
(4:40:20) And this is the magic thing about racing, that if I get into basketball, like I can't set a realistic expectation that I'm gonna play in the NBA, then I'm gonna go to the finals. Or I get into tennis and I'm gonna play at Wimbledon, that just doesn't happen. But racing thing is special in this way because it requires a fair amount of money to keep these cars running. It's really expensive.
(4:40:39) It's like having a small startup. You need to fly a bunch of people around the world and buy expensive equipment and so forth. So you need a bunch of capital. And I had some through the success of the company. So I could do it, which meant that I could get to Le Mans. So I set that as my goal. I wanna get to Le Mans. And I started racing in real competition 2009.
(4:40:58) And three years later in 2012, I was at the grid of Le Mans for the first time. - We should say, so Le Mans, 24-hour race endurance. I mean, this is insane. - There are three drivers, mind you. So it's not like one guy just drive for 20 hours, 24 hours straight. But still, it's a pretty tough race both physically and mentally, especially mentally.
(4:41:20) When you've been up for 24 plus hours, you're not quite as sharp as when you first wake up. And this is funny about Le Mans too. It starts at around four o'clock in the afternoon. So you've already been up for half a day by the time the race starts. And then there's 24 hours to go before you're done.
(4:41:39) And you'll be in the car for anywhere from usually an hour and a half to a maximum of four hours. The regulations say four out of six is the max you can do. I've spent perhaps two and a half hours in a single stint at Le Mans. It's pretty taxing. You're going 200 miles an hour into some of these turns, and there's another 60 cars on track.
(4:42:00) Whenever I'm in my normal category, which is the LMP2 category, I have GT cars, which are more like a Ferrari and a Porsche that I have to overtake. And then I have these hypercar, which is the top class that are overtaking me. So you got a lot going on and you gotta stay sharp for two and a half hours straight to do that.
(4:42:19) That is just a guaranteed way to get incredible flow for long, long stretches of time. That's why you get addicted to it. That was why I got addicted. - You gotta talk me through this video. This video of you in these LMP2s. - Oh, yes. - This is such a cool. This is so cool. - And this was probably my favorite battle of my career.
(4:42:37) - [Commentator] And Heinemeier Hansson has beat past to add five... - Yeah, so this is me driving against Nico Muller at the Shanghai International Circuit. - You're on the outside here? - I'm on the outside in the... - How cool is it? - Blue and white. And we go a whole track around with basically a piece of paper between a seat down this back straight.
(4:42:58) I get so close to him because I wanna force him over on the other side of the track, such that he can't just box me in. And we've been fighting already at this point for basically 40 minutes straight. I've been managing to keep this professional driver behind me for 40 minutes. And he finally passes me, but we just keep the battle on for the whole time. And it really just shows both these kinds of cars. The Le Mans prototypes. We don't actually ever touch.
(4:43:20) We get within about an inch. And keep going around Shanghai circuit too. - What a cool. How did you get so good? Like what? I mean that's a fascinating story, right? That you are able to get so good. - I'm pretty good for the kind of driver I am, which is called the gentleman driver, which means I'm not a professional driver.
(4:43:46) And like many good gentlemen drivers, when we're at our really best, we can be quite competitive with even professional drivers who have been doing this their whole life. The difference between us and the professionals is the professionals could do it every time or more or less every time. So I can't be this good all the time. When everything is just right, I can be competitive with professional drivers, but that's not how you win championships.
(4:44:04) That's not how you get paid by factories to drive. You gotta be good every time you go out. So that's a huge difference. But some of it was also just, I really put my mind to it by the time I realized race cars is what I want to do as my serious hobby. I put in thousands of hours.
(4:44:23) - Have you crashed? What's the worst crash? - I've had a lot of crashes, but thankfully, knock on wood, I haven't had any crashes where I've gotten really seriously hurt. - Have you like wrecked the car? - Oh yes, oh yes. I've wrecked many car. - So what's that feel like? Just you wreck a car. Like how do you get... - It feels like total shit if you're in a real race and other people depend on you.
(4:44:42) It's not even so much to car, although it's also sometimes that these cars are expensive to repair and that sucks and it feels so wasteful in a way when you crash some of these cars. But the sense that you're letting a team down. Endurance racing is a team sport. Not only do you have your mechanics, do you usually have co-drivers.
(4:45:01) So when I crash I just feel like, damn it, I could have avoided this. - Yeah, but also you could have died. - Do you know what's funny? I never think about that. I don't think you can. Because I think the moment you start thinking about being able to die, you can't do it. You can't go fast.
(4:45:25) - Well, I'm sure not to go all Carl Jung and Freud here, but I'm sure that's always present in the back of your mind somewhere. You're not just bringing it to the surface. - It is in the sense that it's part of the appeal, it's part of the sense that there's something on the line that this isn't just virtual. I can't just hit reset, restart, reboot.
(4:45:44) If I crash this car, we're gonna be out or we're gonna be disadvantaged or it's gonna get destroyed or I might get hurt. I've gotten lightly hurt a few times. I actually had the year we won 24 hours of Le Mans in our class, I've been training in this formula 3.5 car. It's a really fast car. It's a really nice exercise to do, but it's also, it doesn't have power steering.
(4:46:10) So some of these race cars, especially the open-seaters, they don't have power steering. Which means that the steering wheel is basically directly connected to the front wheels. So if you crash one of those cars and the front wheel suddenly turn, you're really gonna hurt your hands if you don't get your hands off the wheel.
(4:46:27) I hadn't raced enough of those cars to know that I had to get, or to have the instinct, to have developed the instinct that I had to get my hands off the wheel. So I didn't, and I really hurt my hand and this was just I think a month before the 24 of Le Mans. So I thought, ah man, I'm gonna have to miss it this year. I had like a, not a cast, it was just seriously sprained.
(4:46:45) And then somehow miraculously like a week before the event, I was like, yeah, actually it's okay now. So got to do it. And that would've been grave regret if I would've seen my team go on to win the race and I would have to sit on the sidelines.
(4:47:04) But I really have been quite fortunate in the sense that most of my crashes have just been expensive or sporting inconvenient. They've never been something where I got seriously hurt. But I've seen plenty of people who have in fact, my co-driver this year and for several years, Pietro Fittipaldi drove a race car at Spa. Spa is one of the great race tracks of all time. And it has this iconic corner called Eau Rouge, which is probably the most famous corner in all of motorsports.
(4:47:30) It has a great compression before you climb uphill. It's extremely fast, very difficult corner. And just as he does the compression, his car basically sets out and he loses his power steering and he drives straight into the wall and breaks both his legs and basically faced the prospect that maybe his career was over.
(4:47:54) I've had other teammates and people I know have serious injuries that's really hurt them. And yet what's funny, as you say, you'd think that would sink in. The year before we won in 2014, that same car had a Danish driver in it at Le Mans at the race I was driving who died. He lost control of the car when there was a bit of rain on the track and the track went unfortunately designed in such a poor way that there was a very big tree right behind the railing.
(4:48:34) And he hit that tree at full speed, pulled 90 Gs and was dead on the spot, which was just such an extremely awful experience to go through. I finished second that year, which should have been caused for a bunch of celebration. But it was just tainted by the fact that not only did a driver die, a fellow Dane died. A guy I knew died. That was pretty tough.
(4:49:02) - So that throw that into the pile of the things that have to be considered is the weather conditions that you mentioned of the track. The weather is dry or wet. - It's a huge part of it. - Even just last year at Le Mans. It was raining and I was out, and I hadn't made a serious mistake at the 24 hours of Le Mans since I did the first race in 2012 where I put it in the sand trap with like four hours to go and we lost a couple of lap getting pulled out, but it didn't actually change anything for our result
(4:49:38) because that was just how the field was spread out. I'd made minor mistakes over the years but nothing that really set us out. And at the race last year when it was raining, I first clobbered a Ford Mustang when I made an overambitious pass on a damp part of the track and couldn't stop in time and then felt absolutely awful as I sat in the gravel pit for two laps and knew that our race was over. A race where we were highly competitive.
(4:50:09) You're not blessed with a competitive car, a competitive team and competitive setup every year. I know how rare that is. So to know that we had had a chance that year and I sort of squandered it felt really bad, but that got compounded. I got back on track, barely made it another stint and then put it in the gravel trap again when it started raining on the entrance into Porsche.
(4:50:33) So this is part of why racing is so addicting too because the highs are very, very high. When you win a race like the 24 hours of Le Mans. It feels just incredible. There's so much emotion. But if you fuck it up, the lows are very, very low. - What are the things you're paying attention to when you're driving? What are the parameters? What are you loading in? Are you feeling the grip? Are you basically increasing the speed and seeing what like a constant feedback system effect that has on the grip and you're trying to manage that and try to find that optimal slip angle? Are you looking around using your eyes?
(4:51:12) Are you smelling things? Are you listening just feeling the wind or are you looking at the field too? Like how'd you not hit that guy at all? You get close with an inches, right? So you have to pay attention to that too. It's really interesting about that specific battle where we're literally a few inches apart.
(4:51:32) I can't fully explain it, but humans can develop an incredible sense of space. Where I can't see the edge of the back of my car, but I can know exactly where it is. I can have a mental model in my head that gives me the exact dimensions of this car just that I can run within a few inches of a competitor car or within a few inches of the wall and not hit either when things go well. The car is about two meters wide and it's quite long, five meters.
(4:52:02) And you can't see everything. The mirrors are actually kind of shit. There's no rear view mirror in these cars. You can't see out the back. You can only see through your two side mirrors. But you formed this intuitive mental model when you get good enough at this. But what I actually pay attention to most is I run a program.
(4:52:20) What I try to do when I go to a racetrack is I try to load up the best program I know how for every single corner. What's my break point? What's my acceleration point? What's my break trailing curve? And I try to pick up that program in part just by finding it myself and how fast I can go.
(4:52:40) But even more so than that by copying my professional competitors or not competitors, co-drivers. So I usually always race with a pro. And modern race cars produce an absolute enormous amount of data, and you can analyze all that data after each outing. You can see an exact trace of how much you pushed the brake pedal, how much you did in terms of steering inputs.
(4:53:02) When you got on the gas, you can see every millisecond you're losing is evident in those charts. So what I try to do is I try to look at the chart and then I try to load that in and like that's what I gotta do. Oh, in this corner 17, I have to be 10 bar lighter on the brake. So I try to load that program in and then I try to repeat it now. Then there are all the things that changes. Your tires change quite a lot.
(4:53:29) These tires are made to only last 40 minutes in many cases. Sometimes at Le Mans we can go longer, but at some racetracks they'll last as little as 40 minutes before they really fall off. So you gotta manage that. That the grip is constantly changing. So your program have to suddenly fit those changing circumstances.
(4:53:46) And then in endurance racing, you're constantly interacting with other cars because you're passing slower classes or you're getting passed by a faster class. So that's part of the equation. And then you're trying to dance the car around the limit of adhesion. So you got all those factors playing at the same time.
(4:54:03) But above all else for me is to try to become a robot. Like how can I repeat this set of steps exactly as I'm supposed to for two and a half hours straight without making 100 milliseconds worth of mistakes? - Yeah, low latency algorithm. - That's really a huge part of it actually. Your latency is enormously important in terms of being able to catch when the car starts slipping.
(4:54:33) You get this sensation in your body that the G-force are a little off, the slip angle is a little off, and then you have to counter steer. And obviously the best race car drivers just feel like an intuition. I have some intuition. I don't have all of it, so I do occasionally spin my car. But that's the challenge.
(4:54:51) - From everything you've studied and understand, what does it take to achieve mastery in racing? Like what does it take to become the best race car driver in the world? - Obsession is part of it. When I read and hear about Senna and the other greats, they were just singularly focused. Max Verstappen is the current champion of the world and he is the same kind. Max has been fascinating to watch.
(4:55:18) I mean he is a phenomenal race car driver, but he also literally does nothing else. When he is not at the racetrack, he's driving sim racing. Like he's literally in video games doing more racing when he is not doing all the racing he's already doing. - Is there a specific skill that have that like stands out to you as supernatural through all of that obsession? Is it a bunch of factors or are they actually able to, like you said, develop a sense? Is it they're able to get to the very edge of the slip? - They're able to develop very fine tuned sensibilities for when the car's sliding.
(4:55:51) They can feel just these tiny moments or movements in the chassis that transports up usually through their ass. That's why you call us like a butt meter that goes up and you feel like the car is loose or you feel like you're just about to lock up. You can really hone that tuning. Then the other thing is you have to have really good reaction time.
(4:56:16) And when you look at great Formula 1 drivers, they can generally have a reaction time of just under 200 milliseconds, which is awesome. And even 10 milliseconds difference makes a huge difference. You'll see it when the Formula 1 grit, for example, they do a standing start and you see the five red lights come on. And when the last light goes out, they're supposed to release the clutch and get going. And they can time this. So you can see exactly who has the reaction time.
(4:56:41) And even being off by 20 milliseconds can make the difference of whether you're in front or behind at the first corner. - How much of winning is also just the strategy of jostling for a position? - There's some of that and some of it is also just nerve. Who wants it more? That's exactly when that sense of danger comes in.
(4:57:01) There's a great quote from Fernando Alonso when he was driving at Suzuka against Schumacher, I think. They're coming up to this incredibly fast corner. It's very dangerous. And Alonso basically accounts I was gonna make the pass 'cause I knew he had a wife and kids at home. - That's so gangster. - Just absolutely ruthless, right? - Yeah, wow. - That I knew he valued life more than I did.
(4:57:28) So there's a bit of poker sometimes in that. Who's gonna yield? There's a bit of chicken raised in that regard. And sometimes it doesn't work. No one yields and you both crash. But very often one person will blink first. - Can the pass be both on the inside and the outside? - You can pass wherever you want as long as you have just a slight part of the car on the racetrack. - And then you just improvise and take risks. What a sport.
(4:57:56) And then Senna, of course, is like a legendary risk taker. - Yes, and even before him, by the time, I mean he died in the '90s. But by the time we got to the '90s, racing was al already a lot safer than it was when Niki Lauda raced in the '60s. That level of danger is no longer there. There's still just a remnant of it and it is still dangerous, but nothing like that.
(4:58:24) And it's a little hard to compare through the ages, like who's the greatest driver of all time. I think there's a fair argument that Senna is, but we don't have the data. We don't know who he was up against. Like how would he fare if we pitted him against Max Verstappen today. I do think sometimes that you can have a bit of a nostalgia for the all time greats, but the world moves forward and new records are being set all the time and the professionalism keeps improving. Sometimes to the detriment of the sport, I think.
(4:58:49) There's a lot of professional drivers who are not only just very good at driving, but are very good at being corporate spokespeople. And it used to be quite different. There used to be more characters in racing that had a bit more personality that they were allowed to shine because there weren't a billion sponsorships on the line that they were afraid to lose.
(4:59:06) - Ridiculous question, what's the greatest car I ever made, or maybe what's the funnest one to drive? - The greatest car for me of all time is the Pagani Zonda. - [Lex] Okay, I'm looking this up, Pagani Zonda. - [DHH] So the Pagani Zonda was made by this wonderful Argentinian called Horacio Pagani. - [Lex] My god, that's a beautiful car, wow. - It's a gorgeous car. You can look up mine.
(4:59:29) It's the Pagani Zonda HH. So that's a car I had made in 2010 after we visited the factory in Modena. And by sheer accident ended up with this car, but it became my favorite car in the world basically. When I watched an episode of "Top Gear," I think in 2005, where one of the presenters was driving the Pagani Zonda F around. And I just thought, that's the most beautiful car in the world.
(5:00:05) It is the most incredibly sounding car in the world. If I one day have the option, this is what I want. And then I had the option in 2010, I've had the car ever since. I'm never ever gonna sell it. It's truly a masterpiece that stood the test of time. There's some great cars from history that are recognized as being great in their time. This car is still great.
(5:00:32) - Have you taken on the racetrack? - I have, it's terrible at that. Well, I don't wanna say it's terrible that that's not what it's designed for. It's designed for the road and that's why it's great. There are a lot of fast cars that are straddling their race car for the road. You don't actually want a race car for the world. A race car for the world is a pain in the ass. It's way too stiff. It's way too loud. It's way too uncomfortable. You can't actually take it on a road trip.
(5:00:55) - So this actually feels good driving normal roads? - Totally, totally. - And you of course always go to speed limit? - Always. This is why I love having this car in Spain 'cause they're a little more relaxed. Not entirely relaxed, but more relaxed than they are in a lot of places.
(5:01:13) In Denmark, I kid you not, if you are on the highway and you go more than twice the speed limit, they confiscate your car and keep it. You're not getting it back. They don't even care if it's your car or not. Like if you were boring my car and you went twice the speed limit, it's gone. So they don't do that in Spain.
(5:01:31) I mean, in most places except for the German Autobahn, they get pissy if you go twice the speed limit. For all sorts of fair reasons, I'm not advocating that you should be going much more than that. But there are certain special roads where you can't open things up and no one's in harm's way. And that's an incredible sensation. And I do think that some of those speed limits actually are kind of silly. And I'm not just saying that in a vacuum. In Germany, they have the glorious Autobahn.
(5:01:53) And on the Autobahn, there is no speed limit in a bunch of segments. And they're so committed to their speed-limitless Autobahn, which is by the way, very weird of Germans. They usually love rules. They usually very precise about it. And then they have this glorious thing called the Autobahn.
(5:02:15) There was a great case a couple of years ago where a guy took out a Bugatti Chiron; went 400 kilometers an hour on the Autobahn and he filled it and put it on YouTube and a case was brought against him. Because even though they don't have a speed limit, they do have rules that you can't drive recklessly. And he won the case. He wasn't driving recklessly. He was just going very, very fast. I've done the Autobahn a couple of times.
(5:02:34) My wife and I went on a road trip in Europe in 2009, and I got the Lamborghini Gallardo. We were driving up to 200 miles an hour. And I'd driven 200 miles an hour or close to it on a racetrack before. That feels like one thing. Driving on a public road, 200 miles an hour feels really, really fast. - Scary? - Actually A little scary, yes.
(5:02:59) Because you constantly think like on a racetrack, you know the road, you know the surface. You can walk the track most of the time. You can know if there's a dip. On a public road, you can't know if there's suddenly a pothole. Presumably there's not gonna be a pothole on the German Autobahn. but it does feel a little scary, but also exhilarating. Speed is just intrinsically really fun.
(5:03:17) I don't know anyone I've taken out in a fast car. Well, actually I do know a few people. Most people I take out in a fast car, they grin. It's a human reaction to grin when you go really fast. - Do you know what the fastest you've ever gone? - I was probably at Le Mans, I think when the LMP2s were at their maximum power and had 600 horsepower and really sticky tires, we were going 340 kilometers an hour, which is just over 200 miles an hour.
(5:03:43) A bit over 200 miles an hour. That does feel fast. And it's really interesting with speed is that the difference between going, let's say 150 and 160 doesn't feel that much actually those 10 mile an hour. But the difference between going 190 and 200 feels crazy faster, which as a percentage change is actually less than going from 150 to 160.
(5:04:08) But there's some sense of exponentiality once you get up to those limits where it's just on a complete different level. - Yeah, 'cause to me like 110, 120 feels fast. 200? that's crazy. - It really is crazy. - I gotta ask you about the details of your programming setup, the IDE, all that kind of stuff. Let's paint the picture of the perfect programming setup.
(5:04:39) Do you have a programming setup that you enjoy? Are you very flexible? Like how many monitors? What kind of keyboard? What kind of chair? What kind of desk? - It's funny because if you'd asked me, let's see, a year and a half ago, I would've given you the same answer as I would've given anyone for basically 20 years.
(5:05:01) I want a Mac. I like the Magic Keyboard. I like the single monitor. Apple makes an awesome 6K 32-inch XDR screen that I still haven't found anyone who've beaten that I still use, even though I switched away from Apple computers. I still use their monitor because it's just fantastic. But I've always been a single screen kind of guy.
(5:05:25) I do like a big screen, but I don't want multiple screens. I've never found that that really works with my perception. I wanna be able to just focus on a single thing. I don't want all of it all over the place. And I've always used multiple virtual desktops and being able to switch back and forth between those things. But the setup I have today is Linux.
(5:05:46) I switched to a little over a year ago after I finally got fed up with Apple enough that I couldn't do that anymore. And then I use this low profile mechanical keyboard called the Lofree Flow84, which is just the most glorious sounding keyboard I've ever heard.
(5:06:12) I know there are a lot of connoisseurs of mechanical keyboards that'll probably contest me on this. This is too thy or too clicky or too clocky or whatever. But for me, the Lofree Flow84 is just a delight that I did not even know existed. Which is so funny because I mean, I've been programming for a long time. Mechanical keyboards have been a thing for a long time.
(5:06:32) And the keyboard, when you look at it like this, it just kind of, it looks plain, it doesn't look extravagant. But the tactile sensation you get out of pushing those keys, the talky sound that you hear when the keys hit the board. It's just sublime. And I'm kicking myself that I was in this Mac bubble for so long that I wasn't even in the market to find this.
(5:06:59) I knew mechanical keyboards existed, but to be blunt, I thought it was a bit of a nerd thing that only real nerds that were much more nerdy than me would ever care about. And then I got out of the Apple bubble and suddenly I had to find everything again. I had to find a new mouse, I had to find a new keyboard, I had to find everything. And I thought like, all right, let me give mechanical keyboards a try.
(5:07:17) And I gave quite a few of them a try. The Keychron is one of the big brands in that I didn't like that at all. I tried a bunch of other keyboards and then I finally found this keyboard and I just went like, angels are singing. Where have you been my whole life? We spent as programmers so much of our time interacting with those keys.
(5:07:35) It really kind of matters in a way I didn't fully appreciate. I used to defend the Apple Magic Keyboard, like hey, it's great. It's actually a great keyboard. And I think for what it is, this ultra-low profile, ultra low travel is actually a really nice keyboard. But once you've tried a longer travel mechanical keyboard, there's no going back.
(5:07:54) - You do have to remember in many ways, both on the software side and the hardware side, that you do spend a lot of hour behind the computer. It's worth... - It's worth investing in. - And also worth exploring until you find the thing where the angels starts singing, whatever. - That's exactly right. And I actually do regret that a little bit, especially with this damn keyboard.
(5:08:17) I mean, I could have been listening to these beautiful thocky keys for years and years, but sometimes you have to get really off before you open your eyes and see that something else exists. I feel the same way about Linux. So I've been using Linux on the server since late '90s, probably. We ran servers on Linux back then. I never seriously considered it as a desktop option. I never ran Linux before, directly myself.
(5:08:43) I always thought, you know what? I wanna focus on programming. I don't have time for all these configuration files and all this setup bullshit and whatnot. And Apple is close enough. It's built on Unix underpinnings. Why do I need to bother with Linux? And again, it was one of those things.
(5:09:02) I needed to try new things and try something else to realize that there is other things other than Apple. And again, it's not 'cause I hate Apple. I think they still make good computers. I think a lot of the software is still also pretty okay. But I have come to realize that as a web developer, Linux is just better. Linux is just better. It's closer to what I deploy on. The tooling is actually phenomenal.
(5:09:29) And if you spend a bit of time setting it up, you can record a reproducible environment that I've now done with this Omakub concept or project that I've done, that I can set up a new Linux machine in less than 30 minutes. And it's perfect. It's not pretty good. It's not like I still need to spend two hours on.
(5:09:47) It's perfect because you can code all aspects of the development environment into this. And I didn't know. I didn't even know, to be fair, that Linux could look as good as it can. If you look at a stock Ubuntu or Fedora boot, I mean, not that it's ugly, but I'd pick the Mac any day of the week. You look at Omakub, I mean, I'm biased here, of course, 'cause I built it with my own sensibilities. But I look at that and go like, this is better.
(5:10:12) This is beautiful. And then you look at some of those true Linux rising setups where people go nuts with everything and you go, oh yeah, I remember when computers used to be fun in this way, when there was this individuality and this setup. And it wasn't just all planned the sameness.
(5:10:32) And I think that's the flip side sometimes of something like Apple, where they have really strong opinions and they have really good opinions, they have very good taste. And it looks very nice and it also looks totally the same. And Linux has far more variety and far more texture and flavor. Sometimes also annoyances and bugs and whatever. But I run Linux now. It's Ubuntu-based with the Omakub stuff on top.
(5:10:53) The low free keyboard. I use a Logitech, what's it called? The MX 3 mouse, which I love how it feels in my hand. I don't love how it looks. I actually was a Magic Mouse stan for the longest time. I thought it was genius that Apple integrated the track pad into a mouse, and I used that.
(5:11:16) And I always thought it was ridiculous that people would slag it just because you had to charge it by flipping it over. 'Cause the battery would last for three months and then you'd charge it for half an hour. I thought like, that's a perfect compatibility with my sensibilities. I don't mind giving up a little inconvenience if something is beautiful, and that Magic Mouse is beautiful. But it wasn't gonna work on Linux.
(5:11:34) So I found something else. The S3 is nice, but I sometimes do wish like the Magic Mouse. That's pretty good. - Yeah, Linux is really great for customizing everything, for tiling, for macros, for all of that. And I also do the same in Windows with AutoHotKey or just customize the whole thing to your preferences.
(5:11:56) - If you're a developer, you should learn how to control your environment with the keyboard. It's faster, it's more fluid. I think one of those silly things I've come to truly appreciate about my Omakub setup is that I can, in whatever time it takes to refresh the screen, probably five milliseconds switch from one virtual desktop to another. Even on Windows, you can't get it that smooth. You can get close, you can't get it that smooth.
(5:12:21) On macOS, for whatever reason, Apple insists on having this infuriating animation when you switch between virtual desktops, which makes it just that you don't want to. You don't wanna run full screen apps because it's too cumbersome to switch between the virtual desktops.
(5:12:39) The kind of immediacy that you can get from a wonderful Linux setup in that regard, it's just next level. - Yeah, and it seems like a subtle thing, but you know, difference in milliseconds and latency between switching the virtual desktops, for example. I don't know, it changes... - It changes how you use the computer. It really does.
(5:12:57) - Similar thing with VR, right? If there's some kind of latency or like it just completely takes you all out of it. - And it's funny, I actually had to watch, I think it was ThePrimeagen on YouTube when he was showing off his setup and I was seeing how quickly he was switching between those virtual desktops.
(5:13:14) And I'd always been using virtual desktops, but I didn't like switching too much because just of that latency and it's like, oh, you can do that on Linux? Oh, that's pretty cool. So I run that, and then my editor of choice now is Neovim. - Oh good, all right. Well we're out of time. No, all right, you did for many, many years. You used, what is it? TextMate.
(5:13:32) - Yes, TextMate. - TextMate, yeah. - That was actually, that was the main blocker of moving away from Apple. Everything else I thought, do you know what? I can swing it. But TextMate was and is a wonderful editor. One, I helped birth into this world.
(5:13:52) The programmer Allan Odgaard is a good friend of mine, all the way back from the party days when we were lugging our computers around. And he was a big Mac guy. And in 2005, he was writing this editor and I helped him with the project management of kind of keeping him on track, keeping him focused on getting something released. Because I really wanted it for myself.
(5:14:11) And I thought, this was the last editor I thought I was never gonna switch. - Forgive me for not knowing, but how featureful as this editor is this? - It's quite featureful, but it's a GUI-driven editor in some regards. It was really early on with ways of recording macros and having sort of sophisticated syntax highlighting.
(5:14:36) And it did a bunch of firsts and it was just a really pleasant editing experience. I think these days a lot of people would just use VS Code. VS Codes exist in the same universe as TextMate in some ways. And actually I think is compatible with the original TextMate bundles, the original TextMate format.
(5:14:53) So it really trailed a path there, but it also just didn't evolve. Now a lot of people saw a huge problem with that. They were like, "Oh, it needs to have more features. It needs to have all these things." I was like, I'm happy with this text editor. That hasn't changed at all.
(5:15:09) Basically when Allan stopped working on it for a decade or more. I don't need anything else. Because as our original discussion went, I don't want an IDE. I don't want the editor to write code for me. I want to text editor. I want interact with characters directly. And Neovim allows me to do that in some ways that are even better than TextMate. And I love TextMate, but Vi as you know.
(5:15:37) Once you learn the commands and it sounds, I sometimes feel like Vi fans overplay how difficult it is to learn because it makes them perhaps seem kind of more awesome that they were able to do it. It's not that difficult. And it doesn't take that long, in my opinion, to learn just enough combo moves to get that high of holy shit, I could not do this in any other editor. - How long did it take you? And by the way, I don't know, I haven't yet.
(5:16:00) Oh, I know, intellectually. But just like with kids, I haven't gone in all the way in. I haven't used Vim. - You have a treat in mind. Well, I switched in about, I had three days when I switched here about a year ago. I had three days of cursing where I thought it was absolutely terrible and it was never gonna happen. And I had three days of annoyance and already the next week I was like, this is sweet.
(5:16:25) I'm not going anywhere. But I also had a bit of a head start about 20 years ago in early 2000s. I tried Vim for like a summer and it didn't stick. I didn't for whatever reason love it at the time. But Neovim is really good. The key to Neovim is to realize that you don't have to build the whole editor yourself.
(5:16:45) There's a lot of Neovim stans are like, here's how to write the conflict from scratch. Over 17 episodes, that's gonna take you three weeks. Ah, I don't care that much. I love a great editor. I love to tailor it a little bit, but not that much. So you have to pair Neovim with this thing called LazyVim. Lazyvim.
(5:17:08) org is a distribution for Neovim that takes all the drudgery out of getting an amazing editor experience right out of the box. - Ridiculous question, we talked about a bunch of programming languages. You told us how much you love JavaScript. It's your second favorite programming language. Would TypeScript be the third then? - TypeScript wouldn't even be in this universe. I hate TypeScript as much as I like JavaScript.
(5:17:32) - So what you hate, oh man, I'm not smart enough to understand the math of that. Okay, before I ask about other programming languages, if you can encapsulate your hatred of TypeScript into something that could be human interpretable, what would be the reasoning? - The JavaScript smells a lot like Ruby when it comes to some aspects of its metaprogramming.
(5:17:59) And TypeScript just complicates that to an infuriating degree when you're trying to write that kind of code. And even when you're trying to write the normal kind of code, none of the benefits that accrue to people who like it. Like auto completion is something I care about. I don't care about auto completion because I'm not using an IDE.
(5:18:18) Now I understand that that is part of what separates it and why. I don't see the benefits. I only see the costs. I see the extra typing. I see the type gymnastics that you sometimes have to do and where a bunch of people give up and just do any instead, right? Like that they don't actually use the type system because it's just too frustrating to use.
(5:18:36) So I've ever only felt the frustration of TypeScript and the obfuscation of TypeScript in the code that gave me no payoff. Again, I understand that there is a payoff. I don't want the payoff. So for my situation, I'm not willing to make the trade and I'm not willing to take a language that underneath is as dynamic of a language as rubious and then turn it into this pretend statically type language. I find that just intellectually insulting.
(5:19:09) - Do you think it will and do you think it should die TypeScript? - I don't wanna take something away from people who enjoy it. So if you like TypeScript, all the most part of you. If you using TypeScript because you think that's what a professional program is supposed to do, here's my permission, you don't have to use TypeScript.
(5:19:29) - There's something deeply enjoyable about a brilliant programmer such as yourself, DHH is talking shit. It's like one of my favorite things in life. What are the top three programming languages everyone should learn if you're talking to a beginner? - I would 100% start with Ruby. It is magic for beginners in terms of just understanding the core concepts of conditionals and loops and whatever, because it makes it so easy.
(5:19:55) Even if you're just making a shell program that's outputting to the terminal, getting hello-world running in Ruby is basically puts, P-U-T-S, space, start quotes, hello world, end quotes, you're done, right? There's no fluff. There's nothing to wrap it into. There are other languages that does that, especially in the Perl or Python would be rather similar. But Go would not, Java would not.
(5:20:20) There's a lot of other languages that have a lot more ceremony and boilerplate. Ruby has none of it. So it's a wonderful starting language. There's a book called "Learn to Program" by Pine that uses Ruby essentially to just teach basic programming principles that I've seen heavily recommended. So that's a great language. - [Lex] How quickly would you go to Rails? - It depends on what you wanna do.
(5:20:43) If you wanna build web applications, go to Rails right away. Learn Ruby along with Rails, because I think what really helps power through learning programming is to build programs that you want, right? If you're just learning it in the abstract, it's difficult to motivate yourself to actually do it. Well, some people learn languages just for the fun of them. Most people do not.
(5:21:02) Most people learn it because they have a mission. They wanna build a program. They wanna become a programmer. So you gotta use it for something real. And I actually find that it's easier to learn programming that way too because it drives your learning process. You can't just learn the whole thing up front. You can't just sit down and read the language specification, then go like, ooh, like Neo.
(5:21:21) Now I know kung fu. Now I know Ruby. It doesn't download that way. You actually have to type it out in anger on a real program. - Yeah, yeah, for sure. - So I would start there. But then number two, I probably would be JavaScript. Because JavaScript just is the language you need to know if you wanna work with the web.
(5:21:39) And the web is the greatest application platform of all time if you're making business software, collaboration software, all this kind of stuff. If you're making video games, you should probably go off and learn C++ or C or something else like that. But if you're in the realm of web applications, you gotta learn JavaScript.
(5:21:56) Regardless of what else you learn, you gotta learn JavaScript. - So if you're learning Ruby, what does Ruby not have in terms of programming concepts that you would need other languages for? - I dunno if there's any concepts missing, but it doesn't have the speed or the low level access of memory manipulation that you would need to build a 3D gaming engine, for example. No one's gonna build that in Ruby.
(5:22:22) You could build quite low level stuff when it comes to web technologies in Ruby. But at some point, you're gonna hit the limit and you should use something else. I'm not someone who prescribed just Ruby for everything. Just once you reach the level of abstraction that's involved with web applications, Ruby is superb.
(5:22:42) But if you're writing, for example, HTTP proxy, go, it's great for that. We've written quite a few HTTP proxies lately at the company for various reasons, including our cloud exit and so forth. And Kevin, one of the programs I'm working with, he writes all of that in Go. Go just have the primitives and it has the pace and the speed to do that really well. I highly recommend it.
(5:23:05) If you're writing an HTTP general proxy, do it in Go. Great language for that. Don't ride your business logic in Go. I know people do, but I don't see the point in that. - So what would you say the three, so Go, Ruby plus Rails, JavaScript? - Yeah, if you're interested in working with the web, I'd pick those three. Go, Ruby and JavaScript.
(5:23:25) - Go, Ruby and JavaScript, okay. Functional languages? - Someone's talking about Ocaml. - They are always going to show up. It must be some kind of OCaml industrial complex or something like this. But they always say mention OCaml. - I love that there are people who love functional languages to that degree.
(5:23:46) Those people are not me. I don't care at all. Like I care about functional principles when they help me in these isolated cases where that's just better than everything else. But at heart, I'm an object oriented guy. That's just how I think about programs. That's how I like to think about programs.
(5:24:06) That's how I carve up a big problem space into a domain language. Objects are my jam. - Yeah, me too. So I program a Lisp a bunch for like AI applications for basic. So Othello, chess engines, that kind of stuff. And I did try OCaml just to force myself to program just a very basic game of life. A little simulation. It's much, you know, Lisp is just parentheses everywhere. It's actually not readable at all. - That's the problem I've had with Lisp.
(5:24:36) - OCaml is very intuitive, very readable. It's nice. - I really should pick up a language like that at some point. I've been programming long enough that it's a little embarrassing that I haven't actually done anything real in anger in a fully functionally programming language. - Yeah, but like I have to figure out, I'm sure there's an answer to this.
(5:24:54) What can I do that would be useful for me like that I actually wanna build? - Yes, yes, that's my problem - That a functional language is better suited for. - That's right. - Because I really wanna experience the language properly. - [DHH] That's right. - Yeah, 'cause I'm still, yeah. At this point, I'm very object-oriented brained.
(5:25:12) - Yes, and that's my problem too. I don't care as much about these low level problems in computer science. I care about the high level. I care about writing software. I care about the abstraction layer that really floats well with web applications and business logic. And I've come to accept that about myself.
(5:25:32) Even though, as we talked about when I was a kid, I really wanted to become a games programmer. And then I saw what it took to write a collision detection engine. And I go like, yeah, that's not me at all. I'm never gonna be into vector matrix manipulation or any of that stuff.
(5:25:53) It's way too much math and I'm more of a writing person than I'm of a math person. - I mean, just in the way you were speaking today, you have like a poetic literary approach to programming. - Yes. - Yeah. - It's interesting, that's actually exactly right. So I did actually a keynote at RailsConf 10 years ago where I call myself a software writer. I mean, I'm not the first person to say that software writer has been in the vernacular for a long time.
(5:26:17) But the modern identity that most programmers adopt when they're trying to be serious is software engineer. And I reject that label. I'm not an engineer. Occasionally I dabble in some engineering, but the vast majority of the time, I'm a software writer. I write software for human consumption and for my own delight.
(5:26:45) I can get away with that because I'm working in a high level language like Ruby, working on collaboration software and to-do lists and all the other stuff. Again, if I was trying to apply my talents to writing 3D game engines, no, that's not the right mindset. That's not the right identity. But I find that the software engineering identity flattens thing a little bit, I'd like to think that we have software writers and software mathematicians, for example.
(5:27:09) And then those are actually richer ways of describing the abstraction level that you're working at than engineer. - Yeah, and I think if AI becomes more and more successful, I think we'll need software writer skill more and more. Because it feels like that's the realm of which, 'cause it's not writer. You're gonna have to do the software. You're gonna have to be a computer person.
(5:27:37) But there's a more... I don't know, I just don't wanna romanticize it, but it's more poetic, it's more literary. It's more feels like writing a good blog post than... - I actually wish that AI had a bit higher standards for writing. I find the fact that it accepts my sloppy, incomplete sentences a little offensive. I wish there was like a strict mode for AI where it would snap my fingers.
(5:28:03) It was just feeding it key words and like speak proper, do pronunciation, do punctuation, because I love that. I love crafting a just right sentence that hasn't been boiled down, that it has no meat on, it has no character in it. It's succinct. It's not overly flowery.
(5:28:29) It is just right that writing phase, to me, is just addictive. And I find that when programming is the best, it's almost equivalent exactly to that. You also have to solve a problem. You're not just communicating a solution. You have to actually figure out what are you trying to say, but even writing has that.
(5:28:48) Half the time when I start writing a blog post, I don't know exactly what arguments I'm gonna use. They develop as part of the writing process. And that's how writing software happens too. You know roughly the kind of problem you're trying to solve. You don't know exactly how you're gonna solve it. And as you start typing, the solution emerges.
(5:29:07) - And actually, as far as I understand, you and Jason are working on a new book. It's in the early days of that kind of topic. I think he said, he tweeted that it's gonna be titled something like, we don't know what we're doing upfront, something like that. That kind of topic. And you figure out along the way. - That's a big part of it.
(5:29:27) Trying to give more people the permission to trust your own instincts and their own gut and realizing that developing that supercomputer in your stomach is actually the work of a career. And that you should not discard those feelings in preference to over comp... or not even complicated, to analytics, to intellectualism.
(5:29:53) Very often when we look at the big decisions we've had to make, they've come from the gut where you cannot fully articulate like, why do I think this is the right thing? Well, because I've been in this business for 20 years and I've seen a bunch of things and I've talked to a bunch of people, and that is percolating into this being the right answer.
(5:30:11) A lot of people are very skeptical about that in business or unable to trust it because it feels like they can't rationalize. Why are we doing something? Well, because I feel like it, damn it. That's a great privilege of being a bootstrapped independent founder who don't owe their business to someone else and doesn't have to produce a return.
(5:30:31) Because I feel like a lot of the really creeps in when you're trying to rationalize to other people why you do the things you do and why you take the decisions that you do. If you don't have anyone to answer to, you are free to follow your gut. And that's hell of enjoyable way to work. And it's also in very often the correct way to work. Your gut knows a lot, like you can't articulate it, but it's spot on more times than not. - Yeah, having to make a plan can be a paralyzing thing.
(5:30:56) I've often, I mean, I suppose there's different kinds of brains. And first of all, I can't wait to read that book if it materializes. I often feel like in the more interesting things I do in my life, I really don't know what I'm doing up upfront. And I think there's a lot of people around me that care for me that really want me to know what I'm doing.
(5:31:27) They're like, what's the plan? Why are you doing this crazy thing? And if I had to wait until I have a plan, I'm not gonna do it. They have different brains on this kind of stuff. Some people really are planners and it maybe energizes them. I think most creative pursuits, most really interesting, most novel pursuits are like, you kind of have to just take the leap and then just figure out as you go. - My favorite essay in "Rework" is the last one, and it's entitled, Inspiration is Perishable.
(5:31:53) And I think that captures a lot of it, that if you take the time to do a detailed plan, you may very well have lost the inspiration by the time you're done. If you follow the inspiration in that moment and trust your gut, trust your own competence, that you will figure it out, you're gonna get so much more back. You're gonna go on the adventure you otherwise wouldn't have.
(5:32:18) Whether that's just a business decisions or life decision, you have to seize that inspiration. There's a great set of children's books written by this Japanese author about chasing an idea and trying to get a hold of it, and it's beautifully illustrated as an idea something that's floating around as something you have to catch and latch onto.
(5:32:40) That I really feel captures this notion that inspiration is perishable, it'll disappear. If you just put it back on the shelf and say like, well, I gotta be diligent about this. I gotta line up a plan. You may run out and then there's no steam to keep going. - I have to ask about open source.
(5:33:01) What does it take to run a successful open source project? You've spoken about that. It's a misconception that open source is democratic. It's actually meritocratic. That's a beautiful way to put it. So there's often is a kind of also a benevolent dictator at the top often. So can you just speak to that? Having run successful open source projects yourself and being a benevolent dictator yourself.
(5:33:26) - Which is gonna be a bit of a biased piece of evidence here, but I... - But why monarchy is best. - It's great. We should definitely have dictators and they should control everything, especially when the dictator is me. Now, well I think I learned very early on that a quick way to burn out in open source is to treat it as a business, as though your users are customers, as though they have claims of legitimacy on your time and your attention and your direction.
(5:33:56) Because I faced this almost immediately with Ruby on Rails. As soon as it was released, there were a million peoples who had all sorts of opinions about where I ought to take it. And not just opinions, but actually demands. Unless you implement an Oracle database adaptive, this is always gonna be a toy.
(5:34:15) It was actually more or less that exact demand that prompted me to have a slide at one of the early Rails conferences that just said, fuck you. - [Lex] Yeah, I saw that. - I'm not gonna do what you tell me to. I'm here as a bringer of gift. I am sharing code that I wrote on my own time, on my own volition. And you don't have to say thank you. I mean it'd be nice if you did.
(5:34:43) You can take the code and do whatever you want with it. You can contribute back if you want, but you can't tell me what to do or where to go or how to act. I'm not a vendor. This is a fundamental misconception that users of open source occasionally step into, because they're used to buying software from companies who really care about their business. I care about people using my software.
(5:35:07) I think it's great, but we don't have a transactional relationship. I don't get something back when you tell me what to do except grief. And I don't want it, so you can keep it. So my open source philosophy from the start has been, I gotta do this primarily for me. I love when other people find use in my open source. It's not my primary motivation.
(5:35:31) I'm not primarily doing it for other people. I'm primarily doing it for me and my own objectives because as Adam Smith said, it's not for the benevolence of the butcher that we expect our daily meat. It's for his self-interest. And I actually find that to be a beautiful thought that our comments increase in value when we all pursue our self-interest.
(5:35:55) Certainly in the realm of open source. This is also why I reject this notion that open source is in some sort of crisis. That there's a funding crisis that we have to spend more. No, we don't. Open source has never been doing better. Open source has never controlled more domains in software than it has right now. There is no crisis.
(5:36:14) There's a misconception from some people making open source and from a lot of people using open source. That open source is primarily like commercial software. Something you buy and something where you can then make demands as a customer. And that the customer is always right. Customer is not always right. Not even in business, but certainly not in open source.
(5:36:35) In open source, the customer, as it is, is a receiver of gifts. We are having a gift exchange. I show up and give you my code. If you like it, you can use it. And if you have some code that fits in with where I'm going with this, I would love to get those gifts back. And we can keep trading like that. I give you more gifts. You give me some of your gifts.
(5:37:01) Together, we pool all the gifts such that someone showing up brand new, just get a mountain of gifts. This is the magic thing of open source is it increases the total sum value of what's in the common when we all pursue our own self-interest. So I'm building things for Rails that I need. And you know what? You want me to do that.
(5:37:19) You do not want me to build things that I don't need on behalf of other people, 'cause I'll do a crap job. I build much better software when I can evaluate the quality of that software by my own use. I need this feature. I'm gonna build a good version of that feature and I'm gonna build just enough just for me. So I'm not gonna bloat it. I'm not trying to attract the customer here.
(5:37:38) I'm not trying to see some angle. I'm just building what I need. And if you go into open source with that mentality that you're building for you and everything else is a bonus, I think you have all the ingredients to go to distance. I think the people who burn out in open source is when they go in thinking, "I'm making all these gifts.
(5:38:00) I don't really need them myself, but I'm like hoping someone else does and maybe they'll also give me some money." That's a losing proposition. It never basically works. If you want money for your software, you should just sell it.
(5:38:16) We have a perfectly fine model of commercial software that people can make that kind and then they can sell it. But I find a lot of confusion, let's just call it that politely, in open source contributors who want to have their cake and eat it too. They like the mode of working with open source. They maybe even like the status that comes from open source, but they also would like to earn a living for making that open source.
(5:38:35) And therefore, they occasionally end up with the kind of grievances that someone who feels underappreciated at work will develop when others aren't doing enough to recognize their great gifts. - And then they might walk away. I wish I had more insight into their mind state of the individual people that are running these projects, like if they're feeling sad or they need more money or they're like, it's just such a dark box.
(5:39:04) - It can be. - I mean of course there's some communication, but I just sadly see too often they just kind of walk away. - Right, and I think that's actually also part of the beauty of open source. - Is walking away. - You are not obligated to do this code forever. You're obligated to do this for as long as you wanna do it.
(5:39:23) That's basically your own obligation. - But there is a, I know, okay... So you might criticize some pushback. You did write a blog post on forever, until the end of the internet with ta-da list. There is a beautiful aspect and you found a good balance there.
(5:39:42) But I don't know, you're bringing so much joy to people with this thing you created. It's not an obligation, but there's a real beauty to taking care of this thing you've created. There is and not forgetting. I think, what I think what the open source creator is not seeing enough and I mean there's like, how many lives you're making better. There's certain pieces of software that I just quietly use a lot.
(5:40:06) And like they bring my life joy and I wish I could communicate that well. There's ways to donate, but it's inefficient. It's usually hard to donate. - It is. There's some ways for some people that made it easier. GitHub donations is one way of doing it. I donate to a few people even though I don't love the paradigm. I also accept that we can have multiple paradigms.
(5:40:30) I accept that I can do open source for one set of motivations and other people can do open source for other motivations. We don't all have to do it the same way. But I do want to counter the misconception that open source is somehow high in a crisis unless we all start paying for open source. That model already exists. It's commercial software.
(5:40:49) It works very well and plenty of great companies have been built off the back of it. And the expectations are very clear. I pay you this amount and I get this software. Open source, once you start mixing money into it, gets real muddy real fast. And a lot of it's just from those misaligned expectations. That if you feel like you're starving artists as an open source developer and you are owed X amount of money because your software is popular, you're delusional and you need to knock that off.
(5:41:18) Just get back on track where you realize that you're putting gifts into the world. And if you get something back in terms of monetary compensation, okay, that's a bonus. But if you need that money back in terms of monetary compensation, you just charge for software or go work for a software company that will employ you to do open source. There's tons of that.
(5:41:36) That is probably actually the primary mode that open source software is being developed in the world today. Commercial companies making open source that they need themselves and then contributing it back. - So I'm glad you sort of like drew some hard lines here. It is a good moment to bring up what I think is the, maybe one of the greatest open source projects ever, WordPress.
(5:42:07) And you spoke up in October 24 about some of the stuff that's been going on with WordPress's founder Matt Mullenweg in a blog post open source royalty and mad kings. It's a really good blog post on sort of just the idea of Benevolent Dictators For Life, this model for open source projects. And then the basic implication was that Matt as the BDFL of WordPress has lost his way a bit with his battle with WP Engine.
(5:42:36) So I should also say that I really love WordPress. It brings me joy. I think it's a really... it's a beacon of what open source could be. I think it's made the internet better. It allowed a lot of people to create wonderful websites. And I also think, now you might disagree with this, but from everything I've seen, WP Engine just gives me bad vibes.
(5:43:03) I think they're not the good guy in this. I don't like it. I understand the frustration. I understand all of it, but I don't think that excuses the behavior. There is a bit of, see this kind of counter to a little bit what you said, which is when you have a open source project of that size, there is a bit of a, like when you're the king for a project of a kingdom that large, there's a bit of responsibility.
(5:43:38) Anyway, could you speak to your, maybe to your empathy of Matt and to your criticism and maybe paint a path of how he and WordPress can be winning again? - First echo what you said about what a wonderful thing it is that WordPress exists. That there are not many projects in the open source world or in the world at large that has had as big of an impact on the internet as WordPress has. He deserves a ton of accolades for that work.
(5:44:12) So that was my engagement, essentially my premise. Do you know what? I had tremendous respect for what Matt has built with WordPress. What that entire ecosystem has built around itself. It's a true marvel. But there's some principles that are larger than my personal sympathies to the characters involved. I agree.
(5:44:36) The Silver Lake private equity company that's involved with WP Engine is not my natural ally. I'm not the natural ally of private equity doing some game with VP Engine. That's not my interest in the case. My interest is essentially a set of principles. And the principles are if you release something as open source, people are free to use it as they see fit.
(5:45:03) And they're free to donate code or resources or money back to the community as they see fit. You may disagree about whether they've done enough, whether they should do more, but you can't show up after you've given the gift of free software to the world and then say, now that you've used that gift, you actually owe me a huge slide of your business because you got too successful using the thing I gave you for free.
(5:45:33) You don't get to take a gift back. That's why we have open source licenses. They stipulate exactly what the obligations are on both sides of the equation. The uses of open source don't get to demand what the makers of open source do and how they act. And the makers of open source don't get to suddenly show up with a ransom note to the users and say, actually you owe me for all sorts of use. I'm 100% allergic to that kind of interaction.
(5:46:03) And I think Matt, unfortunately, for whatever reason, got so wrapped up in what he was owed that he failed to realize what he was destroying. WordPress and Automatic already makes a ton of money. This is part of the wonder of WordPress. This is a project that generates hundreds of millions of dollars, and Matt didn't feel like he was getting enough of that.
(5:46:31) That's not a good argument, bro. You can't just violate the spirit and the letter of these open source licensees and just start showing up with demand letters even to characters that are not particularly sympathetic. This goes to the root of my interpretation of open source in general.
(5:46:53) The GPL is a particular license that actually demands code from people who use it under certain circumstances. I've never liked the GPL. I don't want your shitty code if you don't wanna give it to me. What am I gonna do with that? Some code dump that youve... I'm not on board with that part of Stallman's vision at all. I love the MIT license. To me that is the perfect license because it is mercifully short.
(5:47:17) I think it's two paragraphs, three paragraphs. Really short, and it basically says, here's some software. It comes with no warranty. You can't sue me. You can't demand anything, but you can do whatever the hell you want with it. Have a nice life. That's a perfect open source interaction in my opinion. And that license needs to be upheld.
(5:47:43) These licenses, in general, even the GPL, even if I don't like it, we have to abide by them. Because if we just set aside those licensees, when we in a moment's notice feel like something's slightly unfair, we've lost everything. We've lost the entire framework that allowed open source to prosper and allowed open source to become such an integral part of commerce too.
(5:48:04) I mean, back when open source was initially finding its feet, it was at war with commercial software. Stallman is at war with commercial software and always has been. Bill Gates was in return at war with open source for the longest time. The open source licensees and the clarity that they provide allowed us to end that war. Today, commercial software and open source software can peacefully coexist. I make commercial software.
(5:48:28) I sell Basecamp, I sell HEY, and then I also make a bunch of open source software that I give away for free as gifts. That can't happen if we start violating these contracts. No commercial company is gonna go, "Let me base my next project off this piece of open source.
(5:48:45) If I'm also running the liability that some mad maker is gonna show up seven years in and demand, I give them $50 million." That's not an environment conducive to commerce collaboration or anything else. And it's just basically wrong. I think there's one analysis that's all about kind of the practical outcomes of this, which I think are bad.
(5:49:05) There's also some an argument that's simply about ethics. This is not right. You can't just show up afterwards and demand something. This is not too dissimilar in my opinion, to the whole Apple thing we talked about earlier. Apple just showing up and feeling like they're entitled to 30% of everyone's business. No, that's not right.
(5:49:26) That's not fair. So I think Matt unfortunately kind of steered himself blind on the indignity he thought was being perpetrated against him because there was all this money being made by VP Engine making a good product and not giving quite enough back in Matt's opinion, tough cookie.
(5:49:54) - I think there, maybe I'm reading too much into it, but there might be some personal stuff too, which they weren't only not giving enough, but probably implicitly promising that they will give and then taking advantage of him in that way in his mind, just like interpersonal interaction. And then you get like interpersonally frustrated, that forget like the bigger picture ethics of it. It's like when a guy keeps saying, you know, promising you'll do something.
(5:50:21) And then you realize you wake up one day like a year or two later. Wait a minute, I was being lied to this whole time and that I don't even know if it's about money. - I'd get mad too. It's totally fine to get mad when people disappoint you. That's not justification for upending decades of open source licensees and the essential de facto case law we've established around it.
(5:50:47) This is why I chose to even weigh in on this because I like WordPress. I don't use WordPress. I'm not a part of that community. I don't actually have a dog in this fight. I'm biased if anything towards Matt just as a fellow BDFL. I would like to see him do well with this. But I also think there's some principles at stake here that ring much louder.
(5:51:10) I don't want Rails to suddenly be tainted by the fact that it's open source and whether companies can rely on it and build businesses on it. Because wait, maybe one day I'm gonna turn Matt and I'm gonna turn Matt King and I'm gonna show up with a demand ransom letter. No, screw that. We have way more to protect here. There's way more at stake than your personal beef with someone or your perceived grievance over what you're owed.
(5:51:40) - What would you recommend? What do you think he should do, can do to walk it back to heal? - Decide, this is the curious thing. He could decide to give this up. That's very, very difficult for driven, ambitious people to do. To accept that they're wrong and to give up and lay down their sword. So I had a hope earlier on in this that that was possible. I haven't seen any evidence that Matt is interested in that and I find that deeply regretful, but that's his prerogative.
(5:52:05) I continue to speak out when he's violating the spirit and ethics of open source, but I wish he would just accept that this was a really bad idea. He made a bad bet and I thought, I think he thought he'd just get away with it. That they'd just pay up and that he could put pressure. I mean, I know that temptation.
(5:52:29) When you sit as the head of a very important project, you know that that comes with a great degree of power and you really need a great degree of discipline to reign that in and not exercise that power at every step where you feel aggrieved. I've felt aggrieved a million times over in the 20 plus years of Ruby on Rails. I've really tried very hard not to let those...
(5:52:52) sometimes petty, sometimes substantial grievances over time seep in to the foundation of the ecosystem and risk ruining everything. - As the king of the Rails kingdom, has the power gotten to your head over the years? - I'm sure it has. I mean, who wouldn't? - Do you pace around in your chamber angry? - I do occasionally.
(5:53:16) And I do marvel at both what's been built, what's been possible. Over a million applications have been made with Ruby on Rails by one estimate that I've seen, businesses like Shopify and GitHub and a million others have been built on top of something that I started. That's very gratifying. But you really have to be careful not to smell your own exhaust too much.
(5:53:39) And you have to be just as careful not to listen too much to the haters and not to listen too much to the super fans either that you assess the value and the sort of principles of what you're working towards on its own merits, on your own scoreboard. I try to block that out and then just go, well I'm working on Rails because I love to write Ruby. I love to use Ruby to make web applications.
(5:54:11) That's my north star. And I'll continue to do that and I'll continue to share all of the open source gifts that I uncover along the ways, and that's it. That's enough too. I don't have to get all of it out of it. This is sometimes just as with the guy who thought I'd given up on being Jira or something, instead of doing Basecamp, there are people over the years who've asked like, why didn't you charge for Rails? Like, don't you know how much money have been made off Rails? If we just look at something like Shopify,
(5:54:42) it's worth billions of dollars. I'm not a billionaire and so freaking what? I got more than enough. I got plenty of my share. I will say though, I'm also introspective enough to realize that if it hadn't panned out as well as it did for me on my own business, maybe I would've been more tempted. Maybe if you see other people build huge successful companies off the back of your work and you really don't have a pot to pissed in, you might be tempted to get a little upset about that.
(5:55:15) I've seen that in the Rails world as well, where there are people who contributed substantial bodies of work and then got really miffed when they didn't feel like they got enough back. I was fortunate enough that the business that Jason and I built with Ruby on Rails was as successful as it was.
(5:55:34) And I made the money I needed to make that I didn't need to chase the rest of it. - But we should also just make explicit that many people in your position chase the money. It's not that difficult to chase. Basically you turned away money. You made a lot of decisions that just turned away money. - Maybe, I also think of this example with Matt.
(5:56:00) He probably thought there was easy money for the taking. And it wasn't so easy, was it? It looked like low hanging dollar bills and they turned out to be some really sour grapes. It turned out he probably destroyed vast sums of money by undermining the whole WordPress trust and the ecosystem and putting question marks in the heads of folks who would choose to use WordPress or something else going forward.
(5:56:26) So I often think when people think like, you left money on the table. First of all, so what? I don't have to have all the money. But second of all, maybe the money wasn't on the table at all. - And maybe the cost. Even if you got the money, maybe the cost in other ways, like we've talked about, would outweigh all the money that you could have possibly gotten.
(5:56:43) Meaning like I think you said that the thing that makes you happy is flow and tranquility. Those two things. Beautifully, really beautifully put. And it, you know, gaining money might assign to you responsibility of running a larger thing that takes away the flow that you gain from being... fundamentally for you what flow means is programming.
(5:57:10) And then tranquility is like, I think you also have a beautiful post of like Nirvana is an empty schedule. - When I look at a upcoming week and I see that I have no scheduled meetings at all, which is quite common, or maybe I just have one thing for one hour on one day. I think to myself, do you know what? This could very easily have been very different.
(5:57:30) We could have been running a company of hundreds of people or thousands of people and my entire calendar would've been packed solid with little Tetris blocks of other people's demands on my attention and time, and I would've been miserable as fuck. And I look at that and go like that, what more can I ask for? Which is a really nice state of being I'd actually say. I didn't have this always.
(5:57:56) I did have early on in my career some sense of like, I need a little more. A little more security. And I remember this really interesting study where a bunch of researchers asked people who would made certain amounts of money, how much money would it take for you to feel secure? They'd ask people who had a million dollars network? How much money do you need? Probably need 2 million. 2 million then I'd be good.
(5:58:23) Then they'd ask people with a net worth of 5 million, how much do you need? Ah, 10, I need 10. Ask people with 10 million, what do you need? 20. Every single time people would need double of what they did. I did that for a couple of doublings until I realized, you know what? This is silly. I am already where I wished I would be and a million times over. So what less is there to pursue? Now that doesn't mean that if more money is coming my way, I'm gonna say no to it. Of course not. But it does mean that I'm free to set other things higher.
(5:58:53) And I also do think you realize, as Jim Carrey would say, I wish everyone would get all the money they wished for and they'd realize it wasn't the answer. That money solves a whole host of problems and anxieties, and then it creates a bunch of new ones. And then it also doesn't touch a huge swath of the human experience at all.
(5:59:14) The world is full of miserable, anxious, hurt, rich people. It's also full of miserable, anxious, poor people and I'd rather be a miserable, anxious, rich person than a poor person. But it isn't this magic wand that make everything go away. And that's again, one of those insights just like having children that you cannot communicate in words.
(5:59:39) I've never been able to persuade a person who's not wealthy that wealth wasn't gonna solve all their problems. - One quote you've returned to often that I enjoy a lot is the Coco Chanel quote of the best things in life are free. And the second best things are very, very expensive. And I guess the task is to focus on surrounding yourself with the best things in life like family and all of this and not caring about the other stuff. - I would easily say you can care about the other stuff. Just know the order of priority.
(6:00:12) If you are blessed with a partner that you love, some children that you adore, you've already won the greatest prize that most humans are able to achieve. Most humans in this world, if they are of marital age and they have children, if you ask them what's the most important thing, they would all say that. They would all say that. No matter whether they're rich or poor.
(6:00:40) It's easy to lose sight of that when you're chasing the second best things. Because do you know what? They're also very nice. I really like that Pagani Zonda. It was a very expensive car and I would have no chance of acquiring it if I hadn't become rather successful in business. So I don't wanna dismiss it either. It's great fun to have money.
(6:01:06) It's just not as fun for quite as long or as deep as you think it is. And these other things, having an occupation and a pursuit that you enjoy, being able to carry burdens with a stiff up of a lip and with, again, a sense of meaning is incredible. To have family, to have friends, to have hobbies, to have all these things that are actually available to most people around the world, that's winning.
(6:01:39) And it doesn't mean you have to discount your ambitions, it doesn't mean you can't reach for more, but it does mean it's pretty dumb if you don't realize that it's not gonna complete you in some hocus-pocus woo sense to make more. It really isn't. - What gives you hope about the future of this whole thing we have going on here? Human civilization.
(6:02:11) - I find it easier to be optimistic than pessimistic, because I don't know either way. So if I get to choose, why not just choose to believe it's gonna pan out? Like we suffer more in our imagination than we do in reality. That's one of the quotes out of stoicism. And I also think we have a tendency, a lot of humans have a tendency to be pessimistic in advance for things they don't know how it's gonna pan out.
(6:02:36) Climate change, for example, is making a lot of people very anxious and very pessimistic about the future. You know nothing. 40 years ago we thought the problem was that the planet was gonna be too cool. I happen to believe that it's probably correct that the planet is getting too hot and that CO2 has something to do with it.
(6:02:54) Whether we have the right measures to fix it in time, if that's even possible or not, is completely up in the air and we don't know. If you convince yourself with such certainty that the world is gonna turn to shit, it is. Right up here, in your head today. Climate change might wipe out this entire species in 200 years is not next year.
(6:03:18) It's not 10 years from now. Life might become more unpleasant and there might be more negative effects, and so on. Yes, okay, but then deal with that hardship when it arrives. Don't take that in advance. How are you helping earth by just walking around being depressed? - I think our whole conversation today is also an indication. It's just two humans talking.
(6:03:43) There's billions of us, and there is something about us that wants to solve problems and build cool stuff. And so we're gonna build our way out of whatever we get ourselves into. This is what humans do. We create problems for ourselves and come up, figure out how to build rocket ships to get out of those problems.
(6:04:11) And sometimes the rocket ships create other problems like nuclear warheads and then, I'm sure, I hope, figure out ways how to avoid those problems. And then there'll be nanobots and then the aliens will come and there'll be a massive war between the nanobots and the aliens, and that will bring all of us humans together. - The funny thing, just to pick up one of the points you mentioned, the atom bomb, for example.
(6:04:30) When that was first invented, a lot of people thought we have essentially ended life on earth, right? Or maybe we prevented World War III from happening in the past 80 years because assured mutual annihilation kept the superpowers from attacking each other at least head on and kept their fighting to proxy wars.
(6:04:53) You know what? Proxy wars are not great, but they're probably better than World War III with nuclear weapons. So it's quite difficult in the moment to tell what's actually benefit and what's not. And I think we should be a bit more humble. I've certainly become more humble over time of thinking I know which way it's gonna turn.
(6:05:12) I think the pandemic was a huge moment for a lot of people where there was so much certainty about whether this intervention worked or that intervention didn't work. And most people were wrong. Certainly a lot of very smart people, very qualified people got that just utterly and catastrophe finely wrong.
(6:05:37) So just a little intellectual humility, I think back upon that and go like, you know what? I'm not a PhD in virology and I don't claim that like I somehow saw how it always gonna play out. But the people who were really experts in it, they got a bunch of it wrong. Nobody knows anything. I keep reminding myself of that every day. No one knows anything.
(6:05:56) We can't predict the economy a month out. We can't predict world affairs a month. The world is just too complicated. - Yeah, when I watched the Netflix documentary "Chimp Empire" and you know, there's a hierarchy of chimps, all of that looks eerily similar to us humans. We're recent descendants. So these experts, some of the chimps are got a PhD, others don't.
(6:06:24) Others are really muscular. Others are like beta male kind. They're sucking up to the alpha. There's a lot of interesting dynamics going on that really maps cleanly to the geopolitics of the day. They don't have nuclear weapons, but the nature of their behavior is similar to ours.
(6:06:43) So I think we barely know what's going on, but I do think there's like a basic will to cooperate. A basic compassion that underlies just the human spirit that's there. And maybe that is just me being optimistic. But if that is indeed there, then we're gonna be okay. - The capacity is certainly there.
(6:07:09) Whether we choose that capacity or not, who knows and in what situation? I think accepting that we all have the capacity for both ways, for both incredible generosity and kindness and also cruelty. I think, Young, with this whole theory of the shadow is really spot on. That we all have that capacity in us and accepting that it's our job to attempt to cultivate the better parts of our human nature is weighed against our propensity to sometime be the worst of ourselves. - I'm excited to find out what's gonna happen.
(6:07:43) It's so awesome to be human. I don't wanna die. I kind of wanna be alive for a while to see all the cool shit we do. And one of the cool things I wanna see is all the software you create and all the things you tweet. All the trouble you get yourself into on Twitter. David, yeah, I'm a huge fan like I said.
(6:08:02) Thank you for everything you've done for the world, for the millions of developers you've inspired, and one of whom is me. And thank you for this awesome conversation brother. - Thanks so much for having me. - Thanks for listening to this conversation with DHH.
(6:08:20) To support this podcast, please check out our sponsors in the description and consider subscribing to this channel. And now let me leave you with some words from "Rework" by DHH and Jason Fried. What you do is what matters, not will you think or say, or plan. Thank you for listening and I hope to see you next time.