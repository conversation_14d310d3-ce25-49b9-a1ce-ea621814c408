#!/usr/bin/env python3
"""
创建DHH访谈EPUB电子书的脚本
"""

import os
import zipfile
import shutil
from pathlib import Path

def create_epub():
    """创建EPUB文件"""
    epub_dir = Path("DHH访谈EPUB")
    output_file = "DHH谈编程未来、AI、Ruby on Rails、生产力与育儿.epub"
    
    # 删除已存在的EPUB文件
    if os.path.exists(output_file):
        os.remove(output_file)
    
    # 创建EPUB文件
    with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as epub:
        # 首先添加mimetype文件（必须是第一个，且不压缩）
        epub.write(epub_dir / "mimetype", "mimetype", compress_type=zipfile.ZIP_STORED)
        
        # 添加META-INF目录
        for root, dirs, files in os.walk(epub_dir / "META-INF"):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(epub_dir)
                epub.write(file_path, arc_path)
        
        # 添加OEBPS目录
        for root, dirs, files in os.walk(epub_dir / "OEBPS"):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(epub_dir)
                epub.write(file_path, arc_path)
    
    print(f"EPUB文件已创建: {output_file}")
    return output_file

if __name__ == "__main__":
    create_epub()
