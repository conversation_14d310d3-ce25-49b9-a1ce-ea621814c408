<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第十二章：人生哲学与成功定义</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第12章</div>
        <h1 class="chapter-title">第十二章：人生哲学与成功定义</h1>
    </div>
    
    <div class="chapter-intro">
        <p>在事业成功的背后，DHH有着深刻的人生哲学。本章探讨了他对金钱、成功、幸福的看法，以及对未来的乐观态度。从Coco Chanel的名言到对人类文明的思考，DHH分享了他的人生智慧。</p>
    </div>

    <div class="conversation">
        <p>有数十亿我们，有一些关于我们想要解决问题并建立酷东西的东西。所以我们要建立我们走出我们让自己进入的任何东西的方式。这是人类做的。我们为自己创造问题并想出，弄清楚如何建立火箭船走出那些问题。</p>

        <p>有时火箭船创造其他问题，像核弹头，然后，我确信，我希望，弄清楚如何避免那些问题的方式。然后会有纳米机器人，然后外星人会来，会有纳米机器人和外星人之间的巨大战争，那会将我们所有人类聚集在一起。</p>

        <p><strong>DHH：</strong> 有趣事情，只是拾起你提到的点之一，例如原子弹。</p>

        <p>当那个首先被发明时，很多人认为我们本质上结束地球上生命，对吧？或者也许我们阻止第三次世界大战在过去80年发生，因为确保相互毁灭保持超级大国不攻击彼此至少正面，保持他们的战斗到代理战争。</p>

        <p>你知道什么？代理战争不伟大，但他们可能比有核武器的第三次世界大战更好。所以在那一刻告诉什么实际上好处，什么不是相当困难。我认为我们应该更谦逊一点。我肯定随时间变得更谦逊，想我知道它会转哪种方式。</p>

        <p>我认为大流行对很多人是巨大时刻，有如此多确定性关于这种干预是否工作或那种干预没有工作。大多数人错了。肯定很多非常聪明人，非常合格人得到那个只是完全和灾难性地错误。</p>

        <p>所以只是一点智力谦逊，我想回到那个并说，你知道什么？我不是病毒学博士，我不声称像我以某种方式看到它总是如何发挥。但真的是它专家的人，他们得到一堆它错误。没有人知道任何东西。我每天提醒自己那个。没有人知道任何东西。</p>

        <p>我们不能预测经济一个月。我们不能预测世界事务一个月。世界只是太复杂。</p>

        <p><strong>Lex：</strong> 是的，当我观看Netflix纪录片"黑猩猩帝国"，你知道，有黑猩猩等级制度，所有那个看起来令人毛骨悚然地类似我们人类。我们是最近后代。所以这些专家，一些黑猩猩得到博士学位，其他没有。</p>

        <p>其他真的肌肉发达。其他像beta男性类型。他们吸到alpha。有很多有趣动力进行，真的干净地映射到当天地缘政治。他们没有核武器，但他们行为性质类似我们的。</p>

        <p>所以我认为我们勉强知道正在进行什么，但我确实认为有像基本合作意志。基本同情，基础只是在那里的人类精神。也许那只是我乐观。但如果那确实在那里，然后我们会好。</p>

        <p><strong>DHH：</strong> 能力肯定在那里。</p>

        <p>我们是否选择那种能力或不，谁知道，在什么情况下？我认为接受我们都有两种方式的能力，对令人难以置信慷慨和善良也残忍。我认为，荣格，与这个整个阴影理论真的准确。我们都有那种能力在我们中，接受试图培养我们人性更好部分是我们的工作，权衡我们有时成为我们自己最糟糕的倾向。</p>

        <p><strong>Lex：</strong> 我兴奋发现会发生什么。</p>

        <p>成为人类如此棒。我不想死。我有点想活一段时间看我们做的所有酷狗屎。我想看的酷事情之一是你创造的所有软件和你推特的所有事情。你在Twitter上让自己陷入的所有麻烦。大卫，是的，我是巨大粉丝，就像我说的。</p>

        <p>谢谢你为世界做的一切，为你启发的数百万开发者，其中一个是我。谢谢这个棒对话兄弟。</p>

        <p><strong>DHH：</strong> 非常感谢有我。</p>

        <p><strong>Lex：</strong> 感谢听这个与DHH的对话。</p>

        <p>支持这个播客，请检查我们在描述中的赞助商，考虑订阅这个频道。现在让我用DHH和杰森·弗里德的"重新工作"中的一些话语离开你。你做什么重要，不是你想或说或计划什么。谢谢听，我希望下次见到你。</p>

        <p><strong>Lex：</strong> 我们要进行切线上的切线上的切线。所以让我们去Chrome。我认为Chrome对人类的积极影响是不可估量的，原因就是你刚才描述的。在技术方面，它们呈现的功能，它们创造的竞争，它刺激了Web技术的美妙繁荣。但无论如何，我必须问你关于最近司法部试图分拆Chrome和谷歌的事情。</p>

        <p>你认为这是一个好主意吗？你认为这会造成伤害吗？</p>

        <p><strong>DHH：</strong> 这是一场灾难。我这样说是作为一个对反垄断斗争非常同情的人，因为我确实认为我们在技术中有反垄断问题。但我们没有这些问题的一个地方，总的来说，是浏览器，是我们用来访问开放网络的工具。</p>

        <p>首先，我们有Firefox。现在Firefox做得不是很好。Firefox多年来一直由谷歌支撑，以阻止正在与司法部发生的事情，即他们是镇上唯一的游戏。苹果有Safari。我对苹果也有很多问题，但我喜欢Safari。我喜欢我们有一个在首要操作系统上运行的首要浏览器，人们不能将网络变成只是Chrome体验的事实。</p>

        <p>但我也认为开放网络需要这个万亿美元的冠军，或者至少从中受益。也许它不需要它，但它肯定从中受益。在技术中垄断形成的所有错误事情中，Chrome是最后一个。</p>

        <p>这就是为什么我有时对反垄断斗争感到如此沮丧，有真正的问题。我们应该首先关注首要问题。比如我们手机上的收费站。它们是更大的问题。不是开放网络。不是我们用来访问开放网络的工具。如果我不想使用Chrome，如果我的在互联网上运行的业务的客户不想使用Chrome，他们不必这样做。</p>

        <p>我们从来没有被迫通过它。开放互联网仍然是开放的。所以我认为司法部选择以这种方式追求谷歌真的很遗憾。我确实认为有其他事情你可以为谷歌钉住，他们的广告垄断也许，或者在控制广告分类账的两边所做的恶作剧，他们既控制供应又控制需求。</p>

        <p>有问题。Chrome不是吗？你最终会让网络变得更糟。这是我们在考虑立法时，当我们考虑垄断斗争时，我们总是必须记住的事情，你可能不喜欢今天的事情看起来如何。你可能想对此做些什么，但你也可能让它变得更糟。</p>

        <p>欧洲GDPR背后的良好意图目前已经达到了什么？每个人在互联网上讨厌的Cookie横幅。这不能帮助任何人做任何更好的事情，任何更有效的事情，以任何方式、形状或形式保存任何隐私，这是一个完全的失败，只丰富了律师和会计师和官僚。</p>

        <p><strong>Lex：</strong> 是的，你说Cookie横幅是欧洲在技术方面做得最差的所有地区的纪念碑。</p>

        <p><strong>DHH：</strong> 这是良好意图直接通向地狱的纪念碑。欧洲实际上在良好意图直接通向地狱方面是世界级的。</p>

        <p><strong>Lex：</strong> 所以地狱看起来像Cookie接受按钮，你必须接受所有Cookie。那就是地狱的样子，一遍又一遍。你实际上永远不会到达网页。</p>

        <p><strong>DHH：</strong> 只是在人类规模上，试着想象每天有多少小时浪费在点击那个上面。我们对网络作为人们享受的平台造成了多少伤害，因为它们。互联网部分是丑陋的，因为Cookie横幅。</p>

        <p>Cookie横幅应该拯救我们免受广告，广告可以让网络变得丑陋。有很多这样的例子，但Cookie横幅在一次感觉中让整个互联网变得丑陋。这是一个完全的悲剧。但更糟糕的是，这就是为什么我称之为欧盟搞错的一切的纪念碑，是我们已经知道这一点十年了。</p>

        <p>没有任何地方认真的人相信Cookie横幅对任何人做任何好事。然而我们一直无法摆脱它。有这一个我认为现在10或12年的立法。在每个可以想象的指标上都是完全失败的。每个人都普遍讨厌它，但我们似乎无法对此做任何事情。</p>

        <p>这是任何假装或假装为不仅仅是公民，而是世界各地的人们让事情变得更好的官僚机构的破产声明。这就是Cookie横幅真正让我恼火的地方。这不仅仅是欧盟。这是整个世界。你在这个星球上的任何地方都无法躲避Cookie横幅。</p>

        <p>如果你去到该死的火星乘坐埃隆的火箭之一，试图访问网页，你仍然会看到Cookie横幅。宇宙中没有人能逃脱这种荒谬。</p>

        <p><strong>Lex：</strong> 可能是火箭上的界面。</p>

        <p><strong>DHH：</strong> 它会更慢。你基本上会有150秒的ping时间。所以从火星通过Cookie横幅需要45秒。</p>

        <p><strong>Lex：</strong> 好吧，让我们回到我们一直在进行的这些递归切线的堆栈。所以Chrome，我们应该说，至少在我看来，不是不公平地获胜。它通过公平的方式获胜，只是更好。</p>

        <p><strong>DHH：</strong> 是的，如果我要为另一边偷人一半秒，人们会说，好吧，也许是的，大多数人确实有点勉强同意这是一个相当不错的浏览器。</p>

        <p>但然后他们会说它获得主导地位的原因是分发。它获得分发的原因是因为谷歌也控制Android，因此可以使Chrome成为所有这些手机上的默认浏览器。现在我不买那个。</p>

        <p>我不买那个的原因是因为在Android上，你实际上被允许发布一个具有与Chrome不同的浏览器引擎的不同浏览器。与iOS不同，如果你想发布浏览器，Chrome，例如，为iOS发布，但它不是Chrome，它是穿着裙子的Safari。iOS上的每个替代浏览器都必须使用Safari Web引擎。那不是竞争。那不是Android上发生的事情。</p>

        <p>再次，我认为有一些细微差别，但如果你缩小并查看我们与大技术的所有问题，Chrome不是它。Chrome凭借优点获胜。我勉强地基于那个认识单独切换到Chrome。作为Web开发者，我只是更喜欢它。我在许多方面喜欢Firefox。我喜欢它的精神，但Chrome是比Firefox更好的浏览器，完全停止。</p>

        <p><strong>Lex：</strong> 顺便说一下，我们从来没有提到Edge。Edge也是一个好浏览器。</p>

        <p><strong>DHH：</strong> 因为它也是穿着裙子的Chrome。</p>

        <p><strong>Lex：</strong> 但它从来没有得到爱。我不认为我曾经使用过Bing，我确信Bing真的很好。</p>

        <p><strong>DHH：</strong> 也许你有，因为你知道什么？Bing穿着裙子吗？</p>

        <p><strong>Lex：</strong> 什么？</p>

        <p><strong>DHH：</strong> DuckDuckGo。这实际上是我使用的搜索引擎。DuckDuckGo从Bing获得其搜索结果，或者至少它曾经这样做。如果他们改变了那个，那对我来说是新闻。</p>

        <p><strong>Lex：</strong> 好吧，也许一切都只是一个包装或裙子。一切都在下面穿着裙子。有一些其他的。</p>

        <p><strong>DHH：</strong> 有一些那个。</p>

        <p><strong>Lex：</strong> 乌龟，所有的裙子一直向下。</p>

        <p>好吧，我们在谈论什么？他们，我们从JavaScript和你学习如何编程到达那里。所以最终，大成功故事是当你用PHP构建了一堆东西，你实际上在发布东西。那就是Ruby故事出现的时候。所以你与编程的大爱情故事从那里开始了吗？你能带我去那里吗？什么是Ruby？告诉我Ruby的故事。</p>

        <p>解释Ruby给我。</p>

        <p><strong>DHH：</strong> PHP是将我从只能摆弄HTML并制作一些网页转换为实际能够自己制作Web应用程序的东西。所以我对PHP在这方面欠下巨大的感激。但我从来没有把PHP看作是一个召唤。我从来没有想过，我是一个写PHP的专业程序员，那就是我是谁，那就是我做的。</p>

        <p>我把PHP看作是我需要用来敲击计算机直到它产生我想要的Web应用程序的工具。这非常是达到目的的手段。我没有爱上PHP。我非常感激它教会了我编程的基础知识。我非常感激它为经济学设定了标准。但直到Ruby我才开始把自己想象成程序员。</p>

        <p>这发生的方式是我第一次被雇佣为专业程序员写代码实际上是由杰森·弗里德，我的商业伙伴。一直回到2001年，我在那时已经在PHP上工作这些游戏网站基本上18个月。没有人为我做代码付钱。我通过从哥本哈根，丹麦发送到芝加哥，伊利诺伊州的电子邮件与杰森·弗里德联系，发送给一个不知道我是谁的人。</p>

        <p>我只是提供主动建议。杰森在互联网上问了一个问题，我发送了答案，他在PHP中问。我发送了那个问题的答案。我们开始交谈，然后我们开始工作。顺便说一下，这是互联网可以允许的奇迹。</p>

        <p>哥本哈根的一个孩子从未见过芝加哥的这个人如何能够通过电子邮件连接并开始一起工作？顺便说一下，我们现在仍然在24年后一起工作。这太不可思议了。但我们开始一起工作，我们开始在一些客户项目上一起工作。杰森会做设计，37signals会做设计，我会带来编程PHP。</p>

        <p>在我们一起在PHP中工作了我认为两个或三个客户项目后，我们一直遇到同样的问题。每当你与客户合作时，你从电子邮件开始那个项目。哦，是的，让我们一起工作。这是我们正在建设的。你开始交易越来越多的电子邮件。在几周过去之前，你必须向项目添加某人。</p>

        <p>他们没有电子邮件。他们没有上下文。你发送它，最新文件在哪里？哦，我已经在FTP上上传了。它就像finalfinal_v06_2.0，对吧？那是要得到的。这只是一团糟。在某些方面是美丽的混乱，在某种程度上仍然运行今天绝大多数项目的混乱。电子邮件是最低公分母。这很棒。</p>

        <p>但我们已经以严重的方式与客户失球几次，我们想我们可以做得更好。我们知道如何制作Web应用程序。我们不能只是制作一个比电子邮件更好的管理项目的系统吗？这不能那么难。我们一直在做博客。我们一直在做待办事项列表。</p>

        <p>让我们把其中一些放在一起，只是制作一个系统，其中参与项目的任何人需要的一切都在一个页面上。它必须足够简单，我不会举办研讨会教你如何使用系统。我只是给你登录代码。你要跳进去。所以那是Basecamp。当我们开始在Basecamp上工作时，我第一次在与杰森的经历中有技术选择的自由。没有客户告诉我，"是的，PHP，听起来不错。</p>

        <p>我们知道PHP。你能在PHP中构建它吗？"我有自由统治。在那个时候，我一直在阅读IEEE杂志和2000年代初的其他几本杂志，戴夫·托马斯和马丁·福勒一直在写关于编程模式和如何编写更好代码的文章。这两个人，特别是，都在使用Ruby来解释他们的概念，因为Ruby看起来像伪代码。</p>

        <p>无论你是在C或Java或PHP中编程，所有三个选区都能理解Ruby，因为它基本上只是读链接英语。所以这些人使用Ruby来描述概念。首先，我会阅读这些文章只是为了他们解释的概念。我会想，我喜欢你解释的概念，但我也想看看编程语言。</p>

        <p>我为什么没有听说过这个？所以我开始研究Ruby，我意识到在那个时候，Ruby可能不被任何人知道，但它实际上已经存在很长时间了。Matz，Ruby的日本创造者，早在93年就开始在Ruby上工作。在互联网甚至是一个东西之前。这里我在2003年，10年后，拿起似乎是这个隐藏的宝石，只是躺在默默无闻中，在众目睽睽之下。</p>

        <p>但戴夫·托马斯和马丁·福勒，我认为成功地让我和其他一些人走上了一种编程语言的轨道，这种语言在西方没有被大量使用，但可能是。所以我拿起Ruby，我想，这非常不同。首先，所有分号在哪里？我一直在PHP、ASP中编程，我甚至做了一些Pascal，我看了一些C。到处都有分号。</p>

        <p>这是第一个打击我的事情，该死的分号在哪里？我开始想，实际上，为什么我们在编程中有分号？它们是告诉解释器有新的指令行，但我作为人类不需要它们。怎么样？哦，有人在这里照顾人类，而不是机器。所以这真的让我感兴趣。</p>

        <p>然后我对自己想，你知道吗？我很了解PHP。我不是一个了不起的程序员。我没有在编程中工作那么长时间，但也许我能弄清楚。我要给自己两周时间。我要写一个概念证明，我与数据库交谈，我拉一些记录，我格式化它们一点，我在HTML页面上显示它们。</p>

        <p>我能在几周内弄清楚吗？大约花了一个周末，我完全着迷了。我完全被震撼了，因为Ruby是为我的大脑制作的，就像一个完美的定制手套，由我从未见过的人制作。这怎么可能？</p>

        <p><strong>Lex：</strong> 我们应该说也许像画Ruby具有的某些品质的图片，也许甚至与PHP相比。我们也应该说有一个荒谬的事情，我习惯于我忘记的，PHP到处都有美元符号。</p>

        <p><strong>DHH：</strong> 是的，是的，有线噪声。这就是我喜欢称呼的。</p>

        <p><strong>Lex：</strong> 有线噪声，这是一个如此美丽的短语。是的，所以有所有这些看起来像程序的东西。用Ruby，我的意思是Python有一些相似之处。它只是看起来像自然语言。</p>

        <p>你可以正常阅读它。</p>

        <p><strong>DHH：</strong> 这里有一个做五次迭代的野生循环。你可以字面上输入数字五，点，现在我在数字五下调用方法。顺便说一下，这是Ruby的美丽方面之一，像整数这样的原语也是对象。你可以调用5.times，开始括号。</p>

        <p>现在你在那个括号中的代码上迭代五次，就是这样。</p>

        <p><strong>Lex：</strong> 好吧，这很好。</p>

        <p><strong>DHH：</strong> 这不仅仅是好的，这是例外的。字面上没有其他我知道的编程语言能够将线噪声煮沸，几乎每个其他编程语言会注入到五次迭代在代码块上到那种程度。</p>

        <p><strong>Lex：</strong> 哇，这真的很好，好吧，谢谢你给那个例子。</p>

        <p>那是一个美丽的例子。哇，我不认为我知道什么编程语言做那个。这真的很好。</p>

        <p><strong>DHH：</strong> Ruby充满了那个。所以让我深入几个例子。因为我真的认为它有助于画图片。让我通过说我实际上，我喜欢Python的精神来为此做前缀。</p>

        <p>我认为Ruby和Python社区分享很多相似之处。它们都是动态解释语言。它们都专注于即时性和生产力以及在很多方面的易用性。但然后它们在许多其他方面也非常不同。它们非常不同的一种方式是美学上。Python，对我来说，我希望我不会太冒犯人们。我以前说过这个，它很丑。</p>

        <p>它在其空间中很丑，因为它充满了多余的指令，这些指令对于Guido在87年制作Python时的遗留原因是必要的，这些原因仍然在2025年这里。我的大脑无法应对那个。让我给你一个基本例子。当你在Python中制作类时，初始化方法，起始方法是def。</p>

        <p>好吧，公平。这实际上与Ruby相同，D-E-F，方法的定义。然后它是下划线，不是一个，下划线，两个，init，下划线，下划线，括号开始，self，逗号，然后第一个参数。</p>

        <p><strong>Lex：</strong> 是的，整个self事情。</p>

        <p><strong>DHH：</strong> 我看着那个说，对不起，我出去了。我做不到。</p>

        <p>关于它的一切都冒犯了我的核心敏感性。这里你有所有新对象或类必须实现的最重要方法。它是我在任何地方见过的最美学上冒犯的输入初始化方式之一。你们对此没问题吗？</p>

        <p><strong>Lex：</strong> 嘿，你让我，你知道，你就像在谈论我的婚姻或类似的东西，我没有意识到我一直在一个有毒的关系中。然而，我只是习惯了它。</p>

        <p><strong>DHH：</strong> 对我来说，顺便说一下，那是Ruby的魔力。它打开了我的眼睛，帮助美丽的程序可能是。我不知道我一直在ASP中工作。我一直在PHP中工作。我甚至没有美学美丽代码是我们可以优化的东西的概念。我们可以追求的东西。甚至超过那个，我们可以追求它超过其他目标。Ruby之所以如此美丽，这不是意外，也不容易。Ruby本身是在C中实现的。解析Ruby代码非常困难，因为Ruby是为人类编写的，人类是混乱的生物。他们喜欢以正确的方式的东西。我无法完全解释为什么__init__让我反感，但它确实如此。当我看Ruby替代品时，它真的很有指导意义。</p>

        <p>所以它是def，同样的部分，D-E-F，空格，初始化，括号。甚至不是括号。如果你不需要在参数内调用它，甚至没有括号。这本身实际上也是一个主要部分。如果人类不需要额外的字符，我们不会只是把它们放进去，因为解析计算机会更好。</p>

        <p>我们要摆脱分号。我们要摆脱括号。我们要摆脱下划线。我们要摆脱所有那些丑陋，所有线噪声，并将其煮沸到其纯粹的本质。同时，我们不会缩写。</p>

        <p>这是Ruby和Python之间美学的关键差异。init，短类型，只有五个字符。初始化要长得多，但它看起来好得多，你不经常输入它。所以你应该看一些漂亮的东西。如果你不必一直这样做，它很长是可以的。那些美学评估在Ruby语言中到处都是。</p>

        <p>但让我给你一个更好的例子。if条件。这是所有编程语言的基石。他们有if条件。如果你采用大多数编程语言，它们都有if。这在几乎每种语言中基本上是相同的。空格，开始括号，我们都这样做。</p>

        <p>然后你有也许，让我们说你调用一个叫做user.isadmin的对象，关闭括号，关闭括号，开始括号，这是我们要做的，如果用户是管理员，对吧？那将是一个正常的编程语言。Ruby不这样做。Ruby几乎煮沸了所有这些。我们从if开始。好吧，那是一样的。</p>

        <p>没有括号必要，因为人类没有歧义来区分下一部分只是一个单一语句。所以你做if，空格，用户，点，管理员，问号。没有开括号，没有括号，什么都没有。下一个，开行。这是你的条件。那个问号对计算机没有意义，但它对人类意味着什么。</p>

        <p>Ruby纯粹作为人类之间的沟通工具放入谓词方法样式。解释器实际上更多的工作能够看到这个问号在这里。为什么这个问号在这里？因为它读得如此好。如果user.admin？</p>
    </div>
</body>
</html>