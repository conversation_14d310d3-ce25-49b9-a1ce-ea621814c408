# AI电商文案生成系统 - 项目结构设计

## 📁 完整项目目录结构

```
ai-ecommerce-copywriter/
├── .augment/                           # Augment AI Context Engineering 配置
│   ├── context/
│   │   ├── project-rules.md           # 项目开发规则和约定
│   │   ├── ecommerce-domain.md        # 电商领域知识和规则
│   │   ├── ai-integration-patterns.md # AI集成模式和最佳实践
│   │   └── platform-specifications.md # 淘宝/天猫平台规范
│   ├── prps/
│   │   ├── active/
│   │   │   └── ai-copywriter-system.md # 当前活跃的PRP
│   │   └── templates/
│   │       ├── feature-prp-template.md
│   │       └── ai-integration-prp.md
│   └── validation/
│       ├── content-quality-checks.md
│       ├── platform-compliance.md
│       └── performance-benchmarks.md
├── frontend/                          # React前端应用
│   ├── public/
│   ├── src/
│   │   ├── components/               # 可复用组件
│   │   │   ├── common/              # 通用组件
│   │   │   ├── forms/               # 表单组件
│   │   │   └── generators/          # 文案生成相关组件
│   │   ├── pages/                   # 页面组件
│   │   │   ├── Dashboard.tsx
│   │   │   ├── TitleGenerator.tsx
│   │   │   ├── ContentGenerator.tsx
│   │   │   └── DesignSuggestions.tsx
│   │   ├── services/                # API服务
│   │   ├── hooks/                   # 自定义Hooks
│   │   ├── utils/                   # 工具函数
│   │   ├── types/                   # TypeScript类型定义
│   │   └── styles/                  # 样式文件
│   ├── package.json
│   └── tsconfig.json
├── backend/                           # Node.js后端服务
│   ├── src/
│   │   ├── controllers/             # 控制器层
│   │   │   ├── titleController.ts
│   │   │   ├── contentController.ts
│   │   │   └── designController.ts
│   │   ├── services/                # 业务逻辑层
│   │   │   ├── ai/                  # AI服务集成
│   │   │   │   ├── providers/       # AI提供商适配器
│   │   │   │   ├── prompts/         # 提示词模板
│   │   │   │   └── processors/      # 内容处理器
│   │   │   ├── ecommerce/           # 电商业务逻辑
│   │   │   └── validation/          # 验证服务
│   │   ├── models/                  # 数据模型
│   │   ├── middleware/              # 中间件
│   │   ├── routes/                  # 路由定义
│   │   ├── utils/                   # 工具函数
│   │   └── config/                  # 配置文件
│   ├── tests/                       # 测试文件
│   ├── package.json
│   └── tsconfig.json
├── ai-services/                       # Python AI服务
│   ├── src/
│   │   ├── models/                  # AI模型封装
│   │   ├── processors/              # 内容处理器
│   │   ├── evaluators/              # 质量评估器
│   │   └── utils/                   # 工具函数
│   ├── tests/
│   ├── requirements.txt
│   └── Dockerfile
├── shared/                           # 共享代码和类型
│   ├── types/                       # 共享类型定义
│   ├── constants/                   # 常量定义
│   └── utils/                       # 共享工具函数
├── docs/                            # 项目文档
│   ├── api/                         # API文档
│   ├── user-guide/                  # 用户指南
│   ├── development/                 # 开发文档
│   └── deployment/                  # 部署文档
├── examples/                        # 示例和模板
│   ├── product-data/                # 示例产品数据
│   ├── generated-content/           # 生成内容示例
│   └── api-usage/                   # API使用示例
├── scripts/                         # 构建和部署脚本
├── docker/                          # Docker配置
├── k8s/                            # Kubernetes配置
├── .env.example                     # 环境变量示例
├── docker-compose.yml              # 本地开发环境
├── README.md                        # 项目说明
└── package.json                     # 根目录包配置
```

## 🔧 核心模块设计

### 1. AI服务集成层 (backend/src/services/ai/)

```typescript
// providers/AIProvider.ts
export interface AIProvider {
  name: string;
  generateTitle(request: TitleRequest): Promise<TitleResponse>;
  generateContent(request: ContentRequest): Promise<ContentResponse>;
  generateDesignSuggestion(request: DesignRequest): Promise<DesignResponse>;
}

// providers/OpenAIProvider.ts
export class OpenAIProvider implements AIProvider {
  private client: OpenAI;
  
  constructor(apiKey: string) {
    this.client = new OpenAI({ apiKey });
  }
  
  async generateTitle(request: TitleRequest): Promise<TitleResponse> {
    const prompt = this.buildTitlePrompt(request);
    const response = await this.client.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 500
    });
    
    return this.parseTitleResponse(response);
  }
}

// providers/ClaudeProvider.ts
export class ClaudeProvider implements AIProvider {
  // Claude API集成实现
}

// AIServiceManager.ts
export class AIServiceManager {
  private providers: Map<string, AIProvider> = new Map();
  
  registerProvider(name: string, provider: AIProvider) {
    this.providers.set(name, provider);
  }
  
  async generateWithFallback<T>(
    operation: (provider: AIProvider) => Promise<T>,
    preferredProvider?: string
  ): Promise<T> {
    // 实现多提供商负载均衡和故障转移
  }
}
```

### 2. 电商业务逻辑层 (backend/src/services/ecommerce/)

```typescript
// PlatformRuleEngine.ts
export class PlatformRuleEngine {
  private rules: Map<string, PlatformRule[]> = new Map();
  
  validateTitle(title: string, platform: Platform): ValidationResult {
    const platformRules = this.rules.get(platform.name) || [];
    const violations: string[] = [];
    
    // 字符长度检查
    if (title.length > platform.maxTitleLength) {
      violations.push(`标题超过${platform.maxTitleLength}字符限制`);
    }
    
    // 禁用词检查
    const forbiddenWords = this.checkForbiddenWords(title, platform);
    violations.push(...forbiddenWords);
    
    // SEO关键词密度检查
    const keywordDensity = this.checkKeywordDensity(title);
    if (keywordDensity.isOptimal === false) {
      violations.push(keywordDensity.suggestion);
    }
    
    return {
      isValid: violations.length === 0,
      violations,
      score: this.calculateComplianceScore(title, platform)
    };
  }
}

// ContentOptimizer.ts
export class ContentOptimizer {
  optimizeForPlatform(content: string, platform: Platform): OptimizedContent {
    // 根据平台特性优化内容
  }
  
  enhanceSEO(content: string, keywords: string[]): string {
    // SEO优化逻辑
  }
  
  improveReadability(content: string): string {
    // 可读性优化
  }
}
```

### 3. 提示词工程模块 (backend/src/services/ai/prompts/)

```typescript
// PromptTemplate.ts
export class PromptTemplate {
  constructor(
    private template: string,
    private variables: string[]
  ) {}
  
  render(context: Record<string, any>): string {
    let result = this.template;
    for (const variable of this.variables) {
      const value = context[variable] || '';
      result = result.replace(new RegExp(`{{${variable}}}`, 'g'), value);
    }
    return result;
  }
}

// TitlePrompts.ts
export const TITLE_GENERATION_PROMPT = new PromptTemplate(`
你是一个专业的电商文案专家，专门为{{platform}}平台创作商品标题。

产品信息：
- 类目：{{category}}
- 品牌：{{brand}}
- 产品名称：{{productName}}
- 核心特性：{{keyFeatures}}
- 目标受众：{{targetAudience}}
- SEO关键词：{{keywords}}

平台要求：
- 标题长度限制：{{maxLength}}字符
- 风格要求：{{style}}
- 必须包含的元素：{{requiredElements}}

请生成5个不同风格的商品标题，每个标题都要：
1. 符合平台规则和字符限制
2. 包含核心SEO关键词
3. 突出产品卖点
4. 吸引目标受众
5. 避免使用禁用词汇

输出格式：
{
  "titles": [
    {
      "content": "标题内容",
      "reasoning": "创作理由",
      "seoScore": 85,
      "appealScore": 90
    }
  ]
}
`, ['platform', 'category', 'brand', 'productName', 'keyFeatures', 'targetAudience', 'keywords', 'maxLength', 'style', 'requiredElements']);
```

### 4. 内容质量评估模块 (ai-services/src/evaluators/)

```python
# content_evaluator.py
class ContentQualityEvaluator:
    def __init__(self):
        self.seo_analyzer = SEOAnalyzer()
        self.readability_analyzer = ReadabilityAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
    
    def evaluate_title(self, title: str, context: dict) -> QualityScore:
        """评估标题质量"""
        scores = {
            'seo_score': self.seo_analyzer.analyze(title, context.get('keywords', [])),
            'readability_score': self.readability_analyzer.analyze(title),
            'appeal_score': self.sentiment_analyzer.analyze_appeal(title),
            'compliance_score': self.check_platform_compliance(title, context.get('platform'))
        }
        
        overall_score = sum(scores.values()) / len(scores)
        
        return QualityScore(
            overall=overall_score,
            breakdown=scores,
            suggestions=self.generate_improvement_suggestions(title, scores)
        )
    
    def evaluate_content(self, content: str, content_type: str) -> QualityScore:
        """评估详情页内容质量"""
        # 实现内容质量评估逻辑
        pass

# seo_analyzer.py
class SEOAnalyzer:
    def analyze(self, text: str, keywords: list) -> float:
        """分析SEO质量"""
        score = 0.0
        
        # 关键词密度分析
        keyword_density = self.calculate_keyword_density(text, keywords)
        score += min(keyword_density * 20, 30)  # 最高30分
        
        # 标题结构分析
        structure_score = self.analyze_title_structure(text)
        score += structure_score  # 最高40分
        
        # 长度优化分析
        length_score = self.analyze_length_optimization(text)
        score += length_score  # 最高30分
        
        return min(score, 100)
```

### 5. 前端组件架构 (frontend/src/components/)

```typescript
// generators/TitleGenerator.tsx
export const TitleGenerator: React.FC = () => {
  const [productInfo, setProductInfo] = useState<ProductInfo>({});
  const [generatedTitles, setGeneratedTitles] = useState<GeneratedTitle[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleGenerate = async () => {
    setIsLoading(true);
    try {
      const response = await titleService.generateTitles({
        productInfo,
        platform: selectedPlatform,
        style: selectedStyle
      });
      setGeneratedTitles(response.titles);
    } catch (error) {
      notification.error({ message: '生成失败', description: error.message });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Card title="智能标题生成">
      <ProductInfoForm 
        value={productInfo} 
        onChange={setProductInfo} 
      />
      <GenerationOptions 
        platform={selectedPlatform}
        style={selectedStyle}
        onPlatformChange={setSelectedPlatform}
        onStyleChange={setSelectedStyle}
      />
      <Button 
        type="primary" 
        loading={isLoading}
        onClick={handleGenerate}
      >
        生成标题
      </Button>
      <TitleResults 
        titles={generatedTitles}
        onSelect={handleTitleSelect}
        onEdit={handleTitleEdit}
      />
    </Card>
  );
};

// forms/ProductInfoForm.tsx
export const ProductInfoForm: React.FC<ProductInfoFormProps> = ({ 
  value, 
  onChange 
}) => {
  return (
    <Form layout="vertical">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="商品类目" required>
            <CategorySelector 
              value={value.category}
              onChange={(category) => onChange({ ...value, category })}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="品牌名称">
            <Input 
              value={value.brand}
              onChange={(e) => onChange({ ...value, brand: e.target.value })}
              placeholder="请输入品牌名称"
            />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item label="产品名称" required>
        <Input 
          value={value.productName}
          onChange={(e) => onChange({ ...value, productName: e.target.value })}
          placeholder="请输入产品名称"
        />
      </Form.Item>
      <Form.Item label="核心特性">
        <FeatureTagInput 
          value={value.keyFeatures}
          onChange={(features) => onChange({ ...value, keyFeatures: features })}
        />
      </Form.Item>
    </Form>
  );
};
```

## 🔄 Context Engineering 配置

### .augment/context/project-rules.md
```markdown
# AI电商文案生成系统开发规则

## 🎯 项目特定规则

### 电商领域知识
- 熟悉淘宝/天猫平台规则和限制
- 理解电商文案的转化逻辑和用户心理
- 掌握SEO优化和关键词策略

### AI集成规范
- 所有AI调用必须有错误处理和重试机制
- 实现多AI提供商的负载均衡
- 内容生成必须经过质量评估和合规检查

### 代码质量要求
- TypeScript严格模式，所有类型必须明确定义
- 单元测试覆盖率 ≥ 85%
- API响应时间 < 3秒
- 支持并发用户数 ≥ 100

### 安全和合规
- 所有用户输入必须验证和清理
- 敏感信息加密存储
- 实现内容审核和敏感词过滤
- 遵循数据保护法规
```

### .augment/context/ecommerce-domain.md
```markdown
# 电商领域知识库

## 淘宝/天猫平台规则

### 标题规范
- 淘宝标题：最多30个字符
- 天猫标题：最多60个字符
- 禁用词汇：极限词、绝对化表述等
- 必需元素：品牌词、产品词、属性词

### SEO优化策略
- 关键词密度：2-8%
- 长尾关键词布局
- 标题结构：品牌+产品+卖点+规格
- 热搜词和流量词的使用

### 文案转化要素
- FABE销售法则应用
- 用户痛点和需求分析
- 情感化表达和信任建立
- 紧迫感和稀缺性营造
```

## 📊 监控和分析

### 性能监控指标
- API响应时间
- 内容生成成功率
- 用户满意度评分
- 平台合规率

### 业务指标
- 日活跃用户数
- 文案生成量
- 用户留存率
- 转化率提升效果

### 质量指标
- 内容质量评分
- 人工审核通过率
- 用户编辑率
- 最终采用率

---

*此项目结构设计确保了系统的可扩展性、可维护性和高质量交付，完全符合Context Engineering方法论的要求。*
