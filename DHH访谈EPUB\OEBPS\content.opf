<?xml version="1.0" encoding="utf-8"?>
<package version="3.0" unique-identifier="BookId" xmlns="http://www.idpf.org/2007/opf">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:identifier id="BookId">urn:uuid:dhh-interview-2024</dc:identifier>
    <dc:title>DHH谈编程未来、AI、Ruby on Rails、生产力与育儿</dc:title>
    <dc:creator><PERSON> (DHH)</dc:creator>
    <dc:contributor><PERSON> (主持人)</dc:contributor>
    <dc:language>zh-CN</dc:language>
    <dc:date>2024</dc:date>
    <dc:publisher><PERSON>idman播客中文翻译</dc:publisher>
    <dc:description>Ruby on Rails创始人DHH在Lex Fridman播客第474期的深度访谈中文翻译。涵盖编程哲学、AI观点、创业经历、育儿体验、赛车运动等多个话题。</dc:description>
    <dc:subject>编程</dc:subject>
    <dc:subject>Ruby on Rails</dc:subject>
    <dc:subject>人工智能</dc:subject>
    <dc:subject>创业</dc:subject>
    <dc:subject>技术访谈</dc:subject>
    <meta property="dcterms:modified">2024-12-19T12:00:00Z</meta>
  </metadata>
  
  <manifest>
    <item id="style" href="Styles/style.css" media-type="text/css"/>
    <item id="toc" href="Text/toc.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter01" href="Text/chapter01.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter02" href="Text/chapter02.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter03" href="Text/chapter03.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter04" href="Text/chapter04.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter05" href="Text/chapter05.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter06" href="Text/chapter06.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter07" href="Text/chapter07.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter08" href="Text/chapter08.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter09" href="Text/chapter09.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter10" href="Text/chapter10.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter11" href="Text/chapter11.xhtml" media-type="application/xhtml+xml"/>
    <item id="chapter12" href="Text/chapter12.xhtml" media-type="application/xhtml+xml"/>
    <item id="ncx" href="toc.ncx" media-type="application/x-dtbncx+xml"/>
  </manifest>
  
  <spine toc="ncx">
    <itemref idref="toc"/>
    <itemref idref="chapter01"/>
    <itemref idref="chapter02"/>
    <itemref idref="chapter03"/>
    <itemref idref="chapter04"/>
    <itemref idref="chapter05"/>
    <itemref idref="chapter06"/>
    <itemref idref="chapter07"/>
    <itemref idref="chapter08"/>
    <itemref idref="chapter09"/>
    <itemref idref="chapter10"/>
    <itemref idref="chapter11"/>
    <itemref idref="chapter12"/>
  </spine>
  
  <guide>
    <reference type="toc" title="目录" href="Text/toc.xhtml"/>
  </guide>
</package>
