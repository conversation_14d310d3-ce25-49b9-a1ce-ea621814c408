## Veritasium：关于人工智能和学习的误解——德里克·穆勒解释

- 原视频标题：Veritasium: What Everyone Gets Wrong About AI and Learning – <PERSON> Explains
- 视频链接：https://www.youtube.com/watch?v=0xS68sl2D70

### 文字实录（中文全译，已去除时间戳）

谢谢。谢谢今晚到场的各位。我非常荣幸能在这里谈谈“AI将如何改变教育”。老实说，我确实有点想把“AI”硬塞进这个演讲，因为我真正热爱的其实是“教育”。所以我的根本问题是：AI会在教育中扮演怎样的角色？但在此之前，我们得先理解一个更大的图景——教育到底是如何有效运作的，又在哪些情况下并不奏效。

你们可能看过这段视频——那是十个月前出现的一位AI导师。我们来看看。它先问学生：能否相对于角α，找出三角形的对边、邻边和斜边？学生回答、讨论，最终AI引导他正确找到了斜边。这相当令人印象深刻——而且那还是十个月前。此后AI家教只会变得更强。

如果五年前你告诉我会有这样的技术，我可能都不信。AI的进步有目共睹地迅猛。那么问题来了：这就是未来吗？大家都会这样学习吗？我想先反思两点。第一，你随时都能抱怨教育。现在可以，十年前、一百年前的人也都在抱怨。抱怨教育的现状，总是说得通的，我也能理解为什么。

我这里有些教育系统“失灵”的例子。比如问：水是由什么组成？有人回答：水。再问：H2O是什么意思？答：就是水。还有另一个例子：问现在大气中的二氧化碳水平在发生什么变化？有人把“树”当作主要的CO2排放源，觉得砍掉树就好了……你看，总能抱怨教育不行。

于是我们可以问：为什么人们没有学会东西？有人会归咎于：教育体系陈旧；它是为了培养工厂工人而设计的；或者：因为我们之前还没有AI导师。我待会会回到这个问题——这确实很重要，值得认真思考我们究竟发生了什么，学校体系带来了怎样的结果，为什么会这样。

第二点，我想说的是“革命化（revolutionize）”这个词。在教育领域，人们频繁使用它。比如你们看到萨尔·可汗（Sal Khan）的书《Brave New ... How AI Will Revolutionize Education》（书名略），以及一堆标题“AI如何革命教育”“AI正在革新教育世界”等。显然，人们期待一场革命。但事实是：期待教育革命的人至少已经存在了一百年。托马斯·爱迪生在1922年就说：“电影注定要革新我们的教育体系，几年内它将大幅、甚至完全取代教科书。”他还说过类似“教科书教学效率2%，电影教学效率98%”的话——我不知道这些数字从何而来，恐怕谁也不知道。

但在他那个时代，电影确实像魔法一样。就像我刚给你们看的AI家教片段，也像魔法。所以“教育革命”的说法很容易令人信服。到了1930年代，人们说广播会革新教育：把专家的课程通过电台“广播”进上千个课堂，教室里只需看护孩子的保姆即可——规模经济、最强老师“一对多”。今天，埃隆·马斯克也说：AI教育就像给每个孩子配一个“爱因斯坦”当老师——同一套思路。

1950年代，人们押注电视。学者们做过研究：一间教室现场授课，隔壁用闭路电视转播同一课程，然后测试两边学生的学习效果。结果“不显著差异”。不意外——因为经历几乎相同（假定电视系统质量不错）。

1980年代，至少麻省理工的人觉得他们抓住了“教育革命”的关键：互动。可以和计算机交互、编程。如果教孩子编程，比如“乌龟绘图”编程，他们的总体推理能力会更强。结果呢？孩子们确实擅长了“操控乌龟”，但这些技能并没有迁移到其他类型的推理上——我们并没有因此培养出“更会思考”的人。

1990年代，人们又押注“影碟（类似DVD的大号光盘，如LaserDisc）”，说它会革新课堂教学。实际呢？并没有。再往后是MOOC（大规模开放在线课程）。十多年前它风靡一时：到处都是“MOOC将革新高等教育”的论调。几年后，媒体又接连发文：“在线教育革命偏航”“MOOC革命并不如想象中激进”“为什么MOOC无法革新高教”……故事十个标题讲完：一开始热情似火，最后冷淡退场。我的观点是：人们太容易把“革命化”这个词贴到教育上。这是“老派学校、新派学校”，其实是“同一所学校”。我们不该这么用“革命化”这个词。

那么，为什么这些“革命”从未真正发生？你可以说：教育机构“惯性”巨大，不愿改变；也可以说：技术被过度炒作；或者说：我们当时还没等来AI的能力，这次不一样。但也许还有别的原因。我想探究一下。我觉得有个线索来自我在洛杉矶问路人的一个问题。

问题是：玩具店里有一支玩具球棒和一个玩具球，总价1.10美元。球棒比球贵1美元。那么球多少钱？很多人会脱口而出“10美分”。但这是错的。因为如果球10美分，球棒就应为1.10美元，两者合计1.20美元。正确答案是5美分。为什么他们都说10美分？因为在听题时，那个数字自动“蹦”进了脑海，听起来很对。他们不知道数字从哪来，但它就是冒了出来，于是大家一起脱口而出。

原因可参考丹尼尔·卡尼曼的《思考，快与慢》。书里讲我们有两套思维系统：快的“系统1”和慢的“系统2”。我受此启发，把它们人格化成两个角色。系统2就像你自我意识中的声音——慢、费力，但能按步骤推演、检错、反思思考过程；比如让它算13×17，它会说“我不想算”，但你逼它，它还是能一步步算出来。系统1则是后台飞速处理的家伙，它从感官中抓取关键信息、丢弃无关刺激，你并未意识到它在做这些。它也联结着你的长期记忆库，因此能快而准地给出“答案”。在刚才“10美分”的例子里，系统1把“10美分”端上来，系统2偷懒地没有检查就同意了。目标不是总用系统2，而是知道什么时候该动用系统2、什么时候系统1就够。

我旅行时遇到过两个路人，几乎“活成”了系统1和系统2。我问他们：地球绕太阳一圈要多久？“系统2”一脸“不想算”，把问题丢给“系统1”。“系统1”立刻说“24小时，一天”。听起来很对：太阳升起、落下，一天。然后谈话继续，我问了恐龙是否与人类同时代之类的问题。过了一会，我看到他脸上出现停顿和“思索”的表情——系统2终于上线了。他忽然反应过来：“等等，地球绕太阳可不是一天……”这就反映出系统2的一个特点：它的容量非常有限。

1956年的经典论文《神奇的数字7±2》指出：工作记忆（可由系统2操作）的容量有限，最初估计大约是7个新信息，后来研究下修到约4个。测量方式之一是“加一（或加三）回忆”任务：你短暂看一串数字，遮住后按节拍逐位读出并每位加一。这个任务很费脑，会引发可观察的生理反应：心跳加快、皮肤电反应增强，瞳孔轻微放大。我用超微距镜头拍朋友做该任务时瞳孔的变化：中段最吃力，结束时又逐渐放松。这个“努力程度”称为“认知负荷”。

认知负荷通常分三类：（1）内在认知负荷：任务本身的复杂性，比如给六个数字，或物理课上看似简单的F=ma，对新手其实包含多个全新而复杂的概念；（2）外在（无关）认知负荷：旁边有人咀嚼、座位不舒服、音画不清晰等会分散注意力的因素；（3）助益（有利于建模的）认知负荷：用系统2进行“元认知”，比如观察自己解题时的思路、识别对将来有用的模式。这部分是我们希望尽量争取到的。

问题来了：既然系统2这么有限，我们如何完成复杂任务？几十年前，研究者试图回答：国际象棋大师为什么强？是更高IQ？更好的空间推理？更大的工作记忆广度？他们让不同水平（初学、进阶、高手）的棋手观察一盘中局棋盘5秒钟，然后遮住棋盘，让他们在另一空棋盘上还原棋子位置。新手第一次大约能放对4个子；大师能还原约16个。这靠的是什么？“组块化”。一些看似零散的点在专家眼里能被“打包成一块”。比如把“5、4、9、1”倒序成“1945”，这就不再是4个数字，而是一个“事件点”——二战结束的年份。语言、公式也是如此：对物理学家，薛定谔方程就是一个“整体组块”，能在工作记忆中当作一个单元使用。

因此，我们通过反复练习与系统2的努力加工，把知识和模式“写入”长期记忆；长期记忆越庞大、连接越丰富，系统1就越能将复杂情境迅速“打包”，让我们好像“不用思考”就知道该怎么做。大师多数时候是在“识别”而非逐步推演。教育应当做的，就是小心且反复地使用系统2，把信息稳固到长期记忆中，让系统1能自动化地处理。

这对教育有哪些启示？第一，尽量消除外在（无关）认知负荷：座位要舒适、看板可读、声音清晰、减少口音/语速障碍等；字幕往往有帮助。第二，限制内在认知负荷：不要一次塞太多新东西。许多物理课之所以“失效”，是因为教师在一节课里塞入4、5、6个新概念，远超多数人的工作记忆承载。教学要从学生的已有水平出发，保持“可咀嚼的小口径”。音乐学习里，先让学生演奏“已熟悉的曲子”，因为识谱本就需要时间；放慢速度、极其刻意地练习，每一步都让系统2介入，经过大量重复，才能达到“看似毫不费力的超人表演”。

在这里我也要说：发现式学习可能是危险的。我上学时主流是“建构主义”，它强调学生是知识的主动建构者——我并不反对。问题在于，有些人把它理解成“既然要学生主动，那‘讲授’就无效”，于是过早撤掉“支架”（scaffolding）：直接丢给学生一个难题，“自己想、自己构建”，结果显而易见。更平衡的做法是“示例-练习效应（worked example effect）”与“渐隐支架”：先给出完整示例，再给出部分示例，最后让学生独立完成。目标都是降低内在负荷，承认“系统2资源有限”的现实，逐步搭桥过河，而不是把学生一下抛到终点自己游。

此外，要“刻意练习直到达成熟练”。掌握乘法表到不假思索，就是把它放进了系统1。好处是：当你做更复杂的问题时，这些基础不再占用系统2，从而避免过载。如果永远达不到熟练，新内容总会被旧问题拖累。

如何激发“额外一层的主动思考”？有个研究用到“认知反思测验（CRT）”，其中就有“球棒与球”那题。这个测验发给全球成千上万人（包括一流高校的新生），约90%的人至少错一道。研究者试过把试题换成“模糊难读的字体、复印几次皱巴巴的纸”再发，错误率降到了35%。因为难读迫使系统2介入，抑制了系统1的“脱口而出”。我不是说“制造混乱是好策略”，但它确实可能有效。广告界似乎也在利用这一点：过去广告用朗朗上口的旋律和清晰信息；如今我们被广告轰炸到系统1学会屏蔽，于是一些广告反其道而行——“让你困惑”。我在悉尼海边看到一个“Un”主题的广告牌：“未解释的电视的力量。Un花地球钱。Un付更多，Un花更多……”一头雾水，却被强烈勾起好奇。过几天在公交站又看到延续海报，最后你才发现：这是保险广告。它让你“不得不思考”。

回到AI在教育里的角色。若你接受我前面这幅图景，我认为AI的积极作用之一是“提供及时反馈”。任何技能的学习都需要反复练习与即时反馈：弹竖琴弹错了你立刻知道；打网球发球出界你立刻知道；这都能有效训练大脑。反之，有些领域“反馈并不可靠”，比如短期内高度随机的股市、政治预测、某些经济预测等——这种反馈对下次行动的指导价值很低。AI在提供“即时、针对性的反馈”方面，大有可为（比如可汗在片头展示的AI家教）。

但我非常担心的是：AI也可能“减少费力练习”。我有四个孩子，8岁、6岁、4岁和0岁。我担心他们会不会不再写作文——当存在一个能替你写的生成式AI时，是什么力量迫使他们付出“打磨句子”的练习？如果不亲自去写，他们的大脑会怎样发育？你之所以能掌控语言、能当众表达、能写清楚自己的思想，是因为你一次次地去做、在糟糕中坚持、不断微调、不断得到反馈。如果他们从不经历这一过程，我担心系统1里到底会沉淀下什么？会有那种“庞大、互联的知识网络”和自动化能力吗？我怕不会。面对“有台魔法机器能替你做”的诱惑，我们如何让人们仍然去进行那种“痛苦、吃力”的必经之路？这也是我对绘画等艺术的担忧——如果一句话就能生成你想要的图画，会发生什么？（顺便说，“球棒与球”的插图就是AI画的，我不会画画。）

我想回到两个大问题：为什么人们没有学会？为什么教育里的“革命”一次次没有实现？对第一个问题，我想对演讲开头那些受访者宽容一些。我们的脑是为了在这个世界上“有效”而进化的：找食物和栖身之所、寻找伴侣、融入社会避免被孤立、与人相处、获得快乐……那才是我们天生该做的事。所以，也许人们不知道水由哪些元素构成、不知道CO2可能带来生存威胁，并不那么“令人惊讶”。他们日常更关注社交媒体、Instagram之类的内容，那是与人联结的一部分。我认为这可以理解。

至于第二个问题：为什么电影、电视、广播、电脑、MOOC、如今的AI都没有“革命”教育？我部分想说：也许我们已经找到了最好的形式——一间房子里，有其他学习者、有一位老师，有时间一起交流。教育是一种社会性活动。人们在乎别人，也被别人影响。我认为“技术炒作”的背后，有一种误解：把“教育的问题”理解成“信息无法传达到学生”。但这并不是问题——现在不是，一百年前也不是。只要有书，信息就在那里（假设能获得）。然而，没有好老师、没有志同道合的同伴、没有“为什么要学”的动机，学生仍然学不到什么。

这里用个类比：世界上到处都是沉重的东西，但大多数人并没有健硕的肌肉；到处都有运动场，但跑步的人并不多；锻炼的方式很多，但肥胖仍然普遍。我把老师看作“私教”。健身房摆在那里，可如果没有一个你约了要见面、会监督你、会说“再来一组、坚持”的人，没有一群和你一起做的人，你很难坚持并见到成效。教育也是这样：老师把人召集起来、点燃能量、监督责任、布置作业、营造学习共同体——这就是关键。也因此，我觉得“这些技术都不会革命教育”。

（主持人）谢谢你，Derek。精彩的演讲！接下来是提问时间。台下的朋友如果有问题，请移步楼梯边的麦克风排队。我们也欢迎线上观众提问。请大家的问题尽量简短，以便照顾到更多人。

提问1（现场）：你刚才谈到孩子。我想问：在解决问题时，直接用AI给答案，和自己查书找资料相比如何？是不是让AI回答更好？——Derek：只要AI的回答“可靠”（如今很多时候确实相当可靠），我不认为有本质差别。更快一些未尝不可。这更多是效率问题，不必刻意让过程变慢。

提问2（线上）：如何把这些对教育的洞见落实到政策上？应该由老师、政府，还是公众推动？——Derek：政策很难。比如澳大利亚最近推“直接教学（direct instruction）”，这与我谈的方向一致：讲授并非错误，这点在过去几十年颇为忌讳。教育研究很难，因为变量众多，研究者往往也不是“利益无关”的旁观者，这会导致p-hacking等问题，研究质量未必经得起推敲。尽管如此，我认为研究仍给出了一些强信号：我们需要构建长期记忆，需要费力的练习，需要把人推到舒适区之外。至于改变从哪里来，我不确定。澳大利亚已有所行动，希望能推广到全世界。

提问3（现场）：关于艺术：很多人选择从事艺术并不是为了金钱或回报。你担心生成式AI会阻止人们去“亲自做”吗？——Derek：如果人们只是因为热爱而创作，那很好、也让人乐观，也许我应该少担心一点。但作为家长，我会担心我的孩子是否还能学会“写作”。看看我们如今的手写体：因为不常写字，大家的笔迹都退步了——打字比手写更强。如果进一步，我们连“写作能力”本身都退化了怎么办？还有一点我差点在演讲里提：专家之所以能有重大洞见，很大程度基于他们脑中那套“庞大的结构”。年轻物理学家常有突破，部分原因是老师的心智网络还包含一些历史上走过的“死胡同”，而学生未必学到那些“死胡同”，于是他们的结构更自由，能想出新可能。无论如何，我觉得要表达思想，需要非常完备的“写作结构”。如果机器替你完成，可能会损失很多重要洞见。

提问4（现场）：你谈到系统1/系统2。感觉有了AI，我们更常用系统1，让系统2“休息”。可好奇心与学习是相伴的。若系统2不再被激发，你怎么看？如果你20岁时就有AI，Veritasium还会出现吗？——Derek：很难的“反事实”问题，我也不知道。某些方面AI会加速查找与学习，这很好。我自己也打算尝试用AI创作，看看能不能做出更厉害的东西。现在AI的名声有时被“低质内容”拖累，但我愿意想象一个“高质量AI”的世界，也许那会很棒。总之我会去实验。或许你们未来会看到带AI元素的Veritasium，再告诉我好坏。

提问5（现场）：你说“教育最好的方式是建立个人连接”。那如何“规模化”这种连接？这可能是AI能帮上的地方？——Derek：你如何规模化一个私教、一个水管工、一个电工？答案是：不要规模化，而是“多雇一些”。我觉得教育可能已经接近“最优”，所以才如此难以“颠覆”。我对“颠覆美国医疗”都比“颠覆教育”更乐观。这更多是资源配置与系统建设的问题：我们需要更多、更好的老师。这是可行的路径。

提问6（现场）：你提到“示例-练习、渐隐支架”的课堂方式。AI在这里能扮演什么角色？——Derek：AI可以提供大量“支架”：提示、补足知识空白、根据需要生成50个自测问题等等，只要你会用，它就是很棒的学习辅助工具。它不好的地方在于：它可能让你“完成工作而不真正做功”。而“做功”正是学习的本质，这也是我最大的担忧。

提问7（现场）：现在用聊天机器人写作业太容易了。我个人从不这么做，但很多同学会。是否应该在教育早期“全面禁用”，等到更成熟再引入？——Derek：我认为一定需要部分禁用。像我们当年用计算器，考试有的部分允许、有的部分不允许。写作也应如此：有些写作必须“无AI”，尤其在低年级，课堂里需要安排长时间的“纯手写/纯个人写作”，让学生经历那一段艰难但必要的过程。

提问8（现场）：如何让更多人“爱上在不懂时咬牙攻克”的感觉？——Derek：这很难，近乎“存在性的问题”。随机街访时，很多基础问题大多数人都答不上来。我怀疑我们能否把“不感兴趣的人”强行变得有兴趣。人各有所好。我们的物种并不是为了“去追求理论物理”而进化的——只有极少数人会爱上它。我们不该因此指责其他人。也许“没有通用的方法”。

提问9（现场）：你的视频时长从短到长。是否存在“内容过载”的甜蜜点？做视频时如何控制？——Derek：YouTube的算法确实把我们推向更长的视频。早期2–3分钟，现在30分钟。为什么？因为如果我们做一支“好的30分钟视频”，它就会被更多人看到，这是平台生态所致。有时在15–20分钟后我们会讲积分、导数之类，可能会流失一些观众，但它仍然能满足愿意继续深挖的观众。视频的优势是：观众可以暂停、回看、配合课本做笔记。我们也会权衡：开头1分钟放什么很关键，25分钟后如果作者想放完整推导——那就来吧，留下的都是“铁粉”。

提问10（现场）：如果AI被装进“像人一样的身体”，和你聊天、成为朋友，那算不算“社会互动”？——Derek：这是个了不起的问题。研究表明“一对一辅导”是最好的学习方式之一，存在所谓“2σ（两西格玛）效应”：这类学生的表现比平均水平高两个标准差。如果我们能用“极其自然、仿佛真人”的AI复制这种效果，可能会非常强大。问题在于：我们究竟需要多少“知道对方是真人”？“假人”能否引发同样的社会感受？这很大的问题。

提问11（现场）：如果“不用‘革命’这个词”，你会用什么词？互联网、智能手机深刻改变了我们的生活，也触及教育，尤其对“自我主导的学习旅程”帮助很大。但在课堂、教学法、课程上，它们并没有带来那样“翻天覆地”的变化。——Derek：我更愿意把它们叫作“工具”。在教育中，这些东西最终成为“老师手中的工具”，被使用、被利用，但并没有改写教育的本质生态。所以我会用“工具”这个词。

提问12（现场）：如果我们并不彻底移除AI，你会在课堂上保留哪些AI工具，又移除哪些？——Derek：我会保留能帮助“反复练习、即时反馈”的那些，比如用于打卡练习、纠错反馈、根据学生水平生成针对性题目等。最危险的是“代做作业”的用法——不幸的是，这也是它现在被大量使用的方式，这是我们必须去抑制的。

提问13（现场，量子力学助教）：我刚批完量子期中，成绩惨不忍睹，但平时作业成绩并未反映这一点。很可能是ChatGPT的影响：作业照抄，考试没得抄。有人主张“加重考试权重、减轻作业权重”，但考试也有“考场焦虑”等问题。你认为在AI时代更好的评估方式是什么？——Derek：也许作业的组织方式必须改变，变得更像“期中/期末”的方式进行，以消除“AI代做”的风险。长期、庞大的知识结构必须在“平时练习”中构建，不是等到期中期末才发现“来不及了”。我曾在悉尼科技大学（UTS）上大课，发给400名学生A/B和C/D的举牌卡，几乎每一两分钟就要他们举牌回答，这既帮助我校准他们的状态，也迫使他们持续用系统2做判断，避免打瞌睡或走神。我们需要把这种“持续互动、强制思考”的机制也带到“作业与练习”里。也许以后要想出一种“AI隔离舱”。——（提问者补充：高年级物理题一个问题要做很久，难以每几分钟就反馈一次。）——Derek：这是很好的提醒。但我仍看不到绕开“评估”的办法：总得有期中、期末。如果因为作业没学会而在考试中失败，那就只能面对现实——没有学会就不能毕业。我自认为讲得很清楚、出题也很合理，但学生期中平均分也只有40%。我理解那种痛苦。

谢谢各位的提问，也谢谢线上朋友。让我们再次感谢Derek！

