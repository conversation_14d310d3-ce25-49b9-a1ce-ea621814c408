# 项目开发规则和约定模板

> 这是一个可定制的项目规则模板，用于在 Augment AI 环境中实施 Context Engineering

## 🔄 项目认知与上下文

### 代码库理解
- **开始新任务前**，使用 `codebase-retrieval` 工具了解相关代码模式：
  ```markdown
  请分析与 [功能描述] 相关的所有代码模式，包括：
  - 现有的实现方法
  - 使用的框架和库
  - 测试模式和约定
  - 错误处理方法
  ```

- **保持架构一致性**：遵循现有的架构决策和设计模式
- **尊重项目约定**：使用项目既定的命名、组织和编码风格

### 上下文管理原则
- **分层理解**：从架构层面到实现细节逐层理解项目
- **模式复用**：优先使用项目中已验证的模式和方法
- **文档驱动**：重要决策和模式要有文档记录

## 🧱 代码结构与模块化

### 文件和模块组织
- **文件大小限制**：单个文件不超过 300 行代码
- **模块职责分离**：每个模块有明确的单一职责
- **清晰的依赖关系**：避免循环依赖，保持依赖图清晰

### 命名约定
```markdown
# 根据项目类型定制以下约定

## Python 项目
- 文件名：snake_case
- 类名：PascalCase
- 函数名：snake_case
- 常量：UPPER_SNAKE_CASE

## JavaScript/TypeScript 项目
- 文件名：kebab-case 或 camelCase
- 类名：PascalCase
- 函数名：camelCase
- 常量：UPPER_SNAKE_CASE

## 其他语言
[根据项目实际使用的语言添加约定]
```

### 导入和依赖管理
- **相对导入**：模块内部使用相对导入
- **绝对导入**：跨模块使用绝对导入
- **依赖分组**：标准库、第三方库、项目内部模块分组导入

## 🧪 测试与可靠性

### 测试要求
- **测试覆盖率**：新代码要求至少 80% 的测试覆盖率
- **测试类型**：每个功能至少包含：
  - 1个正常使用场景测试
  - 1个边界条件测试
  - 1个错误处理测试

### 测试组织
```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── e2e/           # 端到端测试
├── fixtures/      # 测试数据
└── utils/         # 测试工具
```

### 测试模式
```python
# Python 测试示例模板
import pytest
from unittest.mock import Mock, patch

class TestFeatureName:
    def test_normal_case(self):
        """测试正常使用场景"""
        # Arrange
        # Act
        # Assert
        pass
    
    def test_edge_case(self):
        """测试边界条件"""
        pass
    
    def test_error_handling(self):
        """测试错误处理"""
        pass
```

## ✅ 质量保证

### 代码质量检查
```bash
# 根据项目技术栈定制命令

# Python 项目
black .                    # 代码格式化
flake8 .                  # 代码风格检查
mypy .                    # 类型检查
pytest --cov=src          # 运行测试并检查覆盖率

# JavaScript/TypeScript 项目
npm run format            # 代码格式化
npm run lint             # 代码风格检查
npm run type-check       # 类型检查
npm test -- --coverage  # 运行测试并检查覆盖率
```

### 提交前检查清单
- [ ] 代码格式化通过
- [ ] 静态分析无错误
- [ ] 所有测试通过
- [ ] 测试覆盖率达标
- [ ] 文档已更新

## 📚 文档要求

### 代码文档
```python
# Python 文档示例
def process_user_data(user_id: int, data: dict) -> dict:
    """
    处理用户数据并返回处理结果。
    
    Args:
        user_id: 用户ID
        data: 要处理的用户数据字典
        
    Returns:
        处理后的数据字典
        
    Raises:
        ValueError: 当用户ID无效时
        KeyError: 当必需的数据字段缺失时
    """
    pass
```

### 架构文档
- **重要决策**：记录架构决策和原因
- **模式说明**：解释项目中使用的设计模式
- **集成指南**：说明如何与外部系统集成

### 变更文档
- **CHANGELOG.md**：记录版本变更
- **迁移指南**：破坏性变更的迁移说明
- **API 文档**：公共接口的详细说明

## 🔧 开发工具和环境

### 开发环境设置
```bash
# 项目特定的环境设置命令
# 例如：
# python -m venv venv
# source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate     # Windows
# pip install -r requirements.txt
```

### 推荐的开发工具
- **IDE 配置**：[推荐的 IDE 设置和插件]
- **调试工具**：[项目使用的调试工具]
- **性能分析**：[性能分析工具和方法]

## 🚀 部署和发布

### 部署流程
```bash
# 部署前检查
npm run build            # 构建项目
npm run test:e2e        # 运行端到端测试
npm run security-check  # 安全检查

# 部署命令
[项目特定的部署命令]
```

### 环境管理
- **开发环境**：本地开发配置
- **测试环境**：集成测试配置
- **生产环境**：生产部署配置

## 🔒 安全要求

### 安全编码实践
- **输入验证**：所有外部输入必须验证
- **输出编码**：防止 XSS 和注入攻击
- **权限检查**：实施最小权限原则
- **敏感数据**：加密存储敏感信息

### 安全检查
```bash
# 安全扫描命令
npm audit                # 依赖漏洞检查
bandit -r src/          # Python 安全检查
eslint --ext .js,.ts src/ # JavaScript 安全检查
```

## 🎯 性能要求

### 性能标准
- **响应时间**：API 响应时间 < 200ms
- **内存使用**：[具体的内存使用限制]
- **并发处理**：[并发处理能力要求]

### 性能监控
```bash
# 性能测试命令
[项目特定的性能测试命令]
```

## 🐛 错误处理和日志

### 错误处理模式
```python
# 统一的错误处理模式
try:
    # 业务逻辑
    pass
except SpecificException as e:
    # 具体异常处理
    logger.error(f"具体错误描述: {e}")
    raise
except Exception as e:
    # 通用异常处理
    logger.error(f"未预期的错误: {e}")
    raise
```

### 日志规范
- **日志级别**：DEBUG, INFO, WARNING, ERROR, CRITICAL
- **日志格式**：统一的日志格式和字段
- **敏感信息**：不在日志中记录敏感信息

## 📋 代码审查要求

### 审查检查点
- [ ] 代码符合项目约定
- [ ] 测试覆盖充分
- [ ] 文档完整准确
- [ ] 性能影响可接受
- [ ] 安全考虑充分

### 审查流程
1. **自我审查**：提交前的自我检查
2. **同行审查**：至少一名同事的代码审查
3. **技术负责人审查**：重要变更需要技术负责人审查

---

## 📝 定制说明

这个模板需要根据具体项目进行定制：

1. **技术栈适配**：根据项目使用的编程语言和框架调整
2. **团队约定**：加入团队特定的开发约定
3. **工具配置**：配置项目使用的开发和部署工具
4. **业务要求**：添加业务特定的质量和性能要求

### 定制检查清单
- [ ] 更新编程语言特定的约定
- [ ] 配置项目特定的工具命令
- [ ] 添加业务领域特定的要求
- [ ] 设置团队协作流程
- [ ] 定义项目特定的质量标准

---

*将此模板保存为 `.augment/context/project-rules.md` 并根据项目需求进行定制。*
