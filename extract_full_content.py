#!/usr/bin/env python3
"""
提取完整内容并创建EPUB章节
"""

import re
import os
from pathlib import Path

def read_original_file():
    """读取原始翻译文件"""
    with open("DHH谈编程未来、AI、Ruby on Rails、生产力与育儿_中文翻译.txt", "r", encoding="utf-8") as f:
        return f.read()

def extract_chapters(content):
    """提取章节内容"""
    lines = content.split('\n')
    
    # 定义章节分割点（按照原文的实际结构）
    chapter_sections = [
        ("第一章：编程学习之路与早期经历", 24, 138),  # DHH的编程学习之路 到 发现Ruby的魔力
        ("第二章：发现Ruby的魔力", 138, 266),        # 发现Ruby的魔力 到 Ruby的美学哲学
        ("第三章：AI与编程的未来", 416, 748),         # 动态类型的哲学 到 Rails的愿景与教条
        ("第四章：Ruby on Rails的诞生与理念", 748, 1168), # Rails的愿景与教条 到 关于合作伙伴关系的讨论
        ("第五章：创业历程与37signals", 1168, 1560),  # 关于合作伙伴关系的讨论 到 关于苹果的讨论
        ("第六章：与苹果的斗争", 1560, 1696),        # 关于苹果的讨论 到 关于育儿的讨论
        ("第七章：育儿体验与家庭价值观", 1696, 1848), # 关于育儿的讨论 到 关于赛车的讨论
        ("第八章：赛车运动的激情", 1848, 2138),      # 关于赛车的讨论 到 关于编程设置的讨论
        ("第九章：编程工具与开发环境", 2138, 2396),   # 关于编程设置的讨论 到 关于开源的讨论
        ("第十章：编程语言的选择与观点", 2270, 2396), # 编程语言部分
        ("第十一章：开源项目的管理哲学", 2396, 2620), # 关于开源的讨论 到 结尾
        ("第十二章：人生哲学与成功定义", 2550, -1),  # 人生哲学部分到结尾
    ]
    
    chapters = {}
    
    for title, start_line, end_line in chapter_sections:
        if end_line == -1:
            chapter_content = '\n'.join(lines[start_line:])
        else:
            chapter_content = '\n'.join(lines[start_line:end_line])
        
        chapters[title] = chapter_content
    
    return chapters

def format_content_for_xhtml(content):
    """格式化内容为XHTML"""
    # 移除标题行
    content = re.sub(r'^## [^\n]+\n', '', content, flags=re.MULTILINE)
    
    # 分割段落
    paragraphs = content.split('\n\n')
    formatted_paragraphs = []
    
    for para in paragraphs:
        para = para.strip()
        if not para:
            continue
            
        # 处理对话
        if para.startswith('**Lex：**') or para.startswith('**DHH：**'):
            # 替换说话者标记
            para = para.replace('**Lex：**', '<strong>Lex：</strong>')
            para = para.replace('**DHH：**', '<strong>DHH：</strong>')
            formatted_paragraphs.append(f'        <p>{para}</p>')
        else:
            # 普通段落
            if para and not para.startswith('#'):
                formatted_paragraphs.append(f'        <p>{para}</p>')
    
    return '\n\n'.join(formatted_paragraphs)

def create_chapter_xhtml(chapter_num, title, content, intro):
    """创建章节XHTML文件"""
    formatted_content = format_content_for_xhtml(content)
    
    template = f'''<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>{title}</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第{chapter_num}章</div>
        <h1 class="chapter-title">{title.replace(f"第{chapter_num}章：", "")}</h1>
    </div>
    
    <div class="chapter-intro">
        <p>{intro}</p>
    </div>

    <div class="conversation">
{formatted_content}
    </div>
</body>
</html>'''
    
    return template

def main():
    """主函数"""
    print("读取原始文件...")
    content = read_original_file()
    print(f"原文件总行数: {len(content.split(chr(10)))}")
    
    print("提取章节内容...")
    chapters = extract_chapters(content)
    
    # 章节介绍
    chapter_intros = {
        "第一章：编程学习之路与早期经历": "DHH的编程之路并非一帆风顺。从童年时期对Commodore 64的渴望，到多次学习编程的失败尝试，再到最终在互联网时代找到自己的方向，这段经历展现了一个传奇程序员的成长轨迹。",
        "第二章：发现Ruby的魔力": "从PHP到Ruby的转变，标志着DHH编程生涯的重要转折点。Ruby不仅仅是一门编程语言，更像是为他的大脑量身定制的完美工具。本章探讨DHH如何发现Ruby，以及这门语言如何改变了他对编程的理解。",
        "第三章：AI与编程的未来": "人工智能正在改变编程的面貌，但DHH对此有着独特而深刻的见解。他既拥抱AI作为编程助手，又坚持认为程序员必须保持对代码的直接控制。本章探讨AI如何影响编程实践，以及程序员如何在AI时代保持技能和创造力。",
        "第四章：Ruby on Rails的诞生与理念": "Ruby on Rails不仅仅是一个Web开发框架，它是一种哲学的体现。本章深入探讨Rails的核心理念、设计原则，以及它如何改变了Web开发的面貌。从约定优于配置到不要重复自己，Rails的每一个设计决策都体现了DHH对编程美学和生产力的追求。",
        "第五章：创业历程与37signals": "从一个小型设计咨询公司到创造出影响数百万人的产品，37signals的发展历程体现了DHH对小团队、简单解决方案和可持续发展的信念。本章探讨了Basecamp和HEY的创建过程，以及DHH对现代创业文化的独特见解。",
        "第六章：与苹果的斗争": "DHH与苹果App Store的冲突成为了科技界的一个标志性事件，揭示了大科技公司的垄断行为。本章详述了HEY应用被拒绝的经过，以及这场斗争对整个开发者生态系统的影响。",
        "第七章：育儿体验与家庭价值观": "成为父亲彻底改变了DHH的人生观。本章探讨了育儿如何影响他的工作方式、生活优先级，以及对传统家庭价值观的重新思考。DHH分享了关于工作与生活平衡的深刻见解。",
        "第八章：赛车运动的激情": "从25岁才获得驾照到在勒芒24小时耐力赛中获得组别冠军，DHH的赛车之路展现了他对追求卓越的执着。本章探讨了赛车运动如何影响他的人生哲学和风险管理理念。",
        "第九章：编程工具与开发环境": "从Mac到Linux的转变，从TextMate到Neovim的选择，DHH对编程工具的偏好反映了他对简洁和效率的追求。本章探讨了工具选择如何影响编程体验和生产力。",
        "第十章：编程语言的选择与观点": "DHH对编程语言有着强烈而明确的观点。本章探讨了他对不同编程语言的看法，特别是他对TypeScript的批评和对Ruby、JavaScript、Go的推荐。",
        "第十一章：开源项目的管理哲学": "作为Ruby on Rails的创造者和维护者，DHH对开源项目管理有着独特的见解。本章探讨了他的仁慈独裁者理念，以及对WordPress争议的分析。",
        "第十二章：人生哲学与成功定义": "在事业成功的背后，DHH有着深刻的人生哲学。本章探讨了他对金钱、成功、幸福的看法，以及对未来的乐观态度。从Coco Chanel的名言到对人类文明的思考，DHH分享了他的人生智慧。"
    }
    
    # 创建完整的章节文件
    for i, (title, chapter_content) in enumerate(chapters.items(), 1):
        chapter_num = f"{i:02d}"
        intro = chapter_intros.get(title, "本章内容精彩纷呈，值得细读。")
        
        print(f"处理 {title}...")
        print(f"  内容长度: {len(chapter_content)} 字符")
        
        xhtml_content = create_chapter_xhtml(chapter_num, title, chapter_content, intro)
        
        filename = f"DHH访谈EPUB/OEBPS/Text/chapter{chapter_num}.xhtml"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xhtml_content)
        
        print(f"  创建文件: {filename}")
    
    print("所有完整章节创建完成！")

if __name__ == "__main__":
    main()
