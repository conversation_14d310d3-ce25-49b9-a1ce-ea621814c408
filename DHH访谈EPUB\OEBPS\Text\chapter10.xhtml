<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第十章：编程语言的选择与观点</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第10章</div>
        <h1 class="chapter-title">第十章：编程语言的选择与观点</h1>
    </div>
    
    <div class="chapter-intro">
        <p>DHH对编程语言有着强烈而明确的观点。本章探讨了他对不同编程语言的看法，特别是他对TypeScript的批评和对Ruby、JavaScript、Go的推荐。</p>
    </div>

    <div class="conversation">
        <p><strong>Lex：</strong> Go，Ruby和JavaScript，好吧。函数式语言？</p>

        <p><strong>DHH：</strong> 某人正在谈论Ocaml。</p>

        <p><strong>Lex：</strong> 他们总是要出现。必须是某种OCaml工业复合体或类似这样的东西。但他们总是说提到OCaml。</p>

        <p><strong>DHH：</strong> 我爱有人爱函数式语言到那种程度。</p>

        <p>那些人不是我。我根本不关心。就像我关心函数式原则，当它们在这些孤立情况中帮助我时，那只是比其他一切更好。但在心中，我是面向对象家伙。那只是我如何想程序。那是我如何喜欢想程序。</p>

        <p>那是我如何将大问题空间雕刻成域语言。对象是我的果酱。</p>

        <p><strong>Lex：</strong> 是的，我也是。所以我为像AI应用基本编程一堆Lisp。所以奥赛罗，国际象棋引擎，那种东西。我确实试了OCaml只是强迫自己编程只是非常基本生命游戏。小模拟。它很多，你知道，Lisp只是到处括号。它实际上根本不可读。</p>

        <p><strong>DHH：</strong> 那是我与Lisp有的问题。</p>

        <p><strong>Lex：</strong> OCaml非常直觉，非常可读。它好。</p>

        <p><strong>DHH：</strong> 我真的应该在某个点拾起像那样的语言。我编程足够长时间，我实际上没有在完全函数式编程语言中愤怒地做任何真实东西有点尴尬。</p>

        <p><strong>Lex：</strong> 是的，但就像我必须弄清楚，我确信有那个答案。</p>

        <p>我可以做什么对我有用，就像我实际上想建立？</p>

        <p><strong>DHH：</strong> 是的，是的，那是我的问题</p>

        <p><strong>Lex：</strong> 函数式语言更适合。</p>

        <p><strong>DHH：</strong> 那正确。</p>

        <p><strong>Lex：</strong> 因为我真的想适当体验语言。</p>

        <p><strong>DHH：</strong> [DHH] 那正确。</p>

        <p><strong>Lex：</strong> 是的，因为我仍然，是的。在这一点，我非常面向对象大脑。</p>

        <p><strong>DHH：</strong> 是的，那也是我的问题。我不那么关心计算机科学中这些低级问题。我关心高级。我关心写软件。我关心与网络应用和业务逻辑真的很好漂浮的抽象层。我已经接受关于我自己那个。</p>

        <p>即使，正如我们谈论当我是孩子时，我真的想成为游戏程序员。然后我看到写碰撞检测引擎需要什么。我说，是的，那根本不是我。我从来不会进入向量矩阵操作或任何那种东西。</p>

        <p>它太多数学，我更多是写作人，而不是数学人。</p>

        <p><strong>Lex：</strong> 我的意思是，只是在你今天说话的方式中，你有像诗意文学编程方法。</p>

        <p><strong>DHH：</strong> 是的。</p>

        <p><strong>Lex：</strong> 是的。</p>

        <p><strong>DHH：</strong> 有趣，那实际上正确。所以我实际上在10年前RailsConf做了主题演讲，我称自己软件作家。我的意思是，我不是说那个的第一人。软件作家在白话中很长时间。</p>

        <p>但大多数程序员在试图严肃时采用的现代身份是软件工程师。我拒绝那个标签。我不是工程师。偶尔我涉足一些工程，但绝大多数时间，我是软件作家。我为人类消费和我自己喜悦写软件。</p>

        <p>我可以逃脱那个，因为我在像Ruby这样高级语言中工作，在协作软件和待办事项列表和所有其他东西上工作。再次，如果我试图将我的才能应用于写3D游戏引擎，不，那不是正确心态。那不是正确身份。但我发现软件工程身份稍微平坦化事情，我喜欢想我们有软件作家和软件数学家，例如。</p>

        <p>然后那些实际上是描述你工作的抽象水平比工程师更丰富方式。</p>

        <p><strong>Lex：</strong> 是的，我认为如果AI变得越来越成功，我认为我们会越来越需要软件作家技能。因为感觉那是领域，因为它不是作家。你会必须做软件。你会必须是计算机人。</p>

        <p>但有更多...我不知道，我只是不想浪漫化它，但它更诗意，它更文学。它更感觉像写好博客文章，而不是...</p>

        <p><strong>DHH：</strong> 我实际上希望AI对写作有更高标准。我发现它接受我草率，不完整句子的事实有点冒犯。我希望有像AI严格模式，它会打我的手指。</p>

        <p>它只是喂它关键词，像说适当，做发音，做标点，因为我爱那个。我爱制作恰好正确句子，没有被煮沸，它没有肉，它没有字符。它简洁。它不过度花哨。</p>

        <p>它只是正确，那个写作阶段，对我来说，只是令人上瘾。我发现当编程最好时，它几乎完全等同于那个。你也必须解决问题。你不只是传达解决方案。你必须实际弄清楚你试图说什么，但甚至写作有那个。</p>

        <p>一半时间当我开始写博客文章时，我不确切知道我要使用什么论点。他们作为写作过程的一部分发展。那也是写软件如何发生。你大致知道你试图解决的问题类型。你不确切知道你如何解决它。当你开始输入时，解决方案出现。</p>

        <p><strong>Lex：</strong> 实际上，据我理解，你和杰森正在写新书。它在那种主题的早期日子。我认为他说，他推特它会被标题像，我们不知道我们提前做什么，类似那样。那种主题。你沿途弄清楚。</p>

        <p><strong>DHH：</strong> 那是它的大部分。</p>

        <p>试图给更多人许可信任你自己的本能和他们自己的直觉，意识到发展你胃中那个超级计算机实际上是职业生涯的工作。你不应该丢弃那些感觉，偏好过度复杂...或甚至不复杂，分析，智力主义。</p>

        <p>经常当我们看我们必须做的大决定时，他们来自直觉，你不能完全阐述，为什么我认为这是正确事情？好吧，因为我在这个业务中20年，我看到一堆事情，我与一堆人交谈，那正在渗透成这是正确答案。</p>

        <p>很多人对业务中那个非常怀疑，或无法信任它，因为感觉他们不能合理化。我们为什么做某些东西？好吧，因为我感觉像它，该死。那是自举独立创始人的伟大特权，他们不欠他们的业务给其他人，不必产生回报。</p>

        <p>因为我感觉很多真的爬进来，当你试图向其他人合理化你为什么做你做的事情，为什么你做你做的决定。如果你没有任何人回答，你自由跟随你的直觉。那是地狱享受工作方式。它也经常是正确工作方式。你的直觉知道很多，就像你不能阐述它，但它比不更多时候准确。</p>

        <p><strong>Lex：</strong> 是的，必须制定计划可以是瘫痪事情。</p>

        <p>我经常，我的意思是，我想有不同类型大脑。首先，如果它实现，我不能等读那本书。我经常感觉在我生活中做的更有趣事情中，我真的不知道我提前做什么。我认为有很多关心我的人真的想要我知道我在做什么。</p>

        <p>他们像，计划是什么？你为什么做这个疯狂事情？如果我必须等到我有计划，我不会做它。他们在这种东西上有不同大脑。一些人真的是计划者，它也许激励他们。我认为大多数创造性追求，大多数真的有趣，大多数新颖追求像，你有点必须只是跳跃，然后只是弄清楚当你去。</p>

        <p><strong>DHH：</strong> 我在"重新工作"中最喜欢文章是最后一个，它标题，灵感是易腐的。</p>

        <p>我认为那捕获很多它，如果你花时间做详细计划，到你完成时，你很可能失去灵感。如果你在那一刻跟随灵感并信任你的直觉，信任你自己的能力，你会弄清楚它，你会得到如此多更多回来。你会去你否则不会有的冒险。</p>

        <p>无论那只是业务决定或生活决定，你必须抓住那种灵感。有这个日本作者写的伟大儿童书籍集合，关于追逐想法并试图抓住它，它美丽地说明作为想法某些东西，作为你必须捕捉并抓住的某些东西漂浮。</p>

        <p>我真的感觉捕获这个概念，灵感是易腐的，它会消失。如果你只是把它放回架子上说，好吧，我必须对这个勤奋。我必须排列计划。你可能用完，然后没有蒸汽继续。</p>
    </div>
</body>
</html>