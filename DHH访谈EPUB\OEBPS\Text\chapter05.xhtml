<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第五章：创业历程与37signals</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第05章</div>
        <h1 class="chapter-title">第五章：创业历程与37signals</h1>
    </div>
    
    <div class="chapter-intro">
        <p>从一个小型设计咨询公司到创造出影响数百万人的产品，37signals的发展历程体现了DHH对小团队、简单解决方案和可持续发展的信念。本章探讨了Basecamp和HEY的创建过程，以及DHH对现代创业文化的独特见解。</p>
    </div>

    <div class="conversation">
        <p><strong>Lex：</strong> 让我问你关于小团队。所以你多次提到杰森，杰森·弗里德，你们已经是合作伙伴很长，很长时间。</p>

        <p>也许可以公平地说他更多在设计业务方面，你就像技术，工程巫师。你们多年来创造如此多令人惊叹的产品，如何没有互相谋杀？这是合作伙伴关系的伟大故事。你能说什么关于合作？你能说什么关于杰森，你喜欢的，你从中学到的？为什么这有效？</p>

        <p><strong>DHH：</strong> 所以首先我会说我们多年来已经试图互相谋杀几次，但我认为在过去十年中要少得多。</p>

        <p>在早期，我们的产品讨论如此激烈，以至于当我们在办公室有它们并且周围有其他员工时，其中一些人合法地担心公司即将分崩离析。因为从房间出来的音量会如此高，听起来如此尖刻，以至于他们合法地担心整个事情会分崩离析。但你知道有趣的是，在那一刻它从来没有感觉像那样。</p>

        <p>它总是感觉像只是对更好东西的顶峰有力搜索。我们能够承受那种关于想法优点的对抗水平，因为它是关于想法。它不是关于人，它从来没有真正变得个人。不是甚至从来没有真正，它没有变得个人。它不像，杰森，你是混蛋。</p>

        <p>它像，杰森，你是白痴。你是白痴，因为你以错误的方式看这个问题。让我告诉你正确的方式来做它。</p>

        <p><strong>Lex：</strong> 作为小切线，让我说这个。一些人说，哦，可能回到这个，你有时可以在互联网上有脾气爆发等等。我从来不那样看，因为它是同样的类型。</p>

        <p>也许我没有看到正确类型的脾气痕迹，但通常它是关于想法，它只是兴奋，热情的人类。</p>

        <p><strong>DHH：</strong> 那正是我喜欢想的。它不总是那样出现。我可以看到为什么特别是旁观者有时会看到看起来像我在追人而不是球的东西。我确实认为我试图在那方面变得更好。</p>

        <p>但在我与杰森的关系中，我认为它工作得如此好，因为我们有我们完全信任彼此的自己独特的能力领域。杰森信任我做正确的技术决定。我信任他做正确的设计和产品方向决定，然后我们可以重叠并在业务上，在营销上，在写作上，在它的其他方面分享。</p>

        <p>所以那是一件事，如果你与某人开始业务，你做与他们完全相同的事情，你不断竞争谁是更有能力的人，我认为那要困难得多，要不稳定得多。所以如果你开始业务，你们都是程序员，你们都在同样类型的编程上工作，啊，祝你好运。我认为那很难。</p>

        <p>我试图选择与设计师工作的更容易路径，我知道至少一半时间我可以只是委托给他的经验和能力并说，你知道什么？我可能有意见，我一直对设计有意见。但我不必赢得论点，因为我信任你。现在偶尔我们会在业务或方向上有重叠，我们都会感觉我们在游戏中有强烈利害关系，我们都对那个领域有能力声明。</p>

        <p>但然后无论什么原因，我们也都有长期愿景，我会说，你知道什么？我认为我们在这里错了。但正如我从杰夫·贝佐斯学到的，顺便说一下，我会不同意并承诺。那是他给我们的那些早期教训之一，绝对关键，也许甚至有助于确保杰森和我已经一起工作四分之一世纪。</p>

        <p>不同意并承诺是老时代杰夫·贝佐斯的伟大之一。</p>

        <p><strong>Lex：</strong> 我只是惊讶洋子·小野没有出现。你知道我的意思吗？就像这个世界上有如此多洋子。</p>

        <p><strong>DHH：</strong> 它可能已经发生了。如果不是部分因为我们不一直坐在彼此的腿上。我们职业生涯的大部分时间，我们甚至没有住在同一个城市。</p>

        <p>就像我在2005年搬到美国后，我们开始时在芝加哥住了几年。但然后我搬到马里布，然后我住在西班牙，然后我住在哥本哈根。杰森和我从我们关系的基础学会了以非常有效的方式一起工作，我们实际上不必那么多交谈。</p>

        <p>在任何给定的周，如果杰森和我花费超过两小时的直接交换和沟通，我会感到惊讶。</p>

        <p><strong>Lex：</strong> 是的，有时是你只是积累的基本人类摩擦。</p>

        <p><strong>DHH：</strong> 是的，我认为你摩擦另一个人，如果太多太长，那个人最好是你的配偶。</p>

        <p><strong>Lex：</strong> 是的，但即使在那里。</p>

        <p><strong>DHH：</strong> 即使在那里。</p>

        <p><strong>Lex：</strong> COVID真的测试了关系。</p>

        <p>这很迷人观看。</p>

        <p><strong>DHH：</strong> 它有，我确实认为有一些分离，这有点反直觉，因为我认为很多人认为你可以有的合作越多，越好。可以来回反弹的想法越多，越好。杰森和我，无论什么原因，在职业生涯早期得出结论。绝对不是，那是完全胡说八道。</p>

        <p>这就是为什么我们是远程工作的巨大支持者。这就是为什么我享受在我的家庭办公室工作，我可以关门，一次六小时不看到另一个人类。我不想一直与你反弹想法。我想偶尔与你反弹想法，然后我想去实施那些想法。</p>

        <p>有太多反弹正在进行，没有足够得分，没有足够扣篮。我认为这是执行规则的伟大陷阱之一。一旦创始人将自己一直提升到执行者，他们正在做的只是告诉其他人做什么，那是他们24/7生活的领域。他们只是生活在想法领域。哦，我可以只是告诉更多人更多事情做什么，我们可以只是看到它发生。</p>

        <p>如果你实际上必须成为实施那个的一部分，你放慢你的马。你想，你知道什么？我上周有一个好想法。我要保存我的其余好想法直到下个月。</p>

        <p><strong>Lex：</strong> 经理和执行层的人有做某些事情的诱惑，那某些事情通常意味着会议，对吧？所以那就是为什么你说...</p>

        <p><strong>DHH：</strong> 他们的工作是告诉其他人做什么。</p>

        <p><strong>Lex：</strong> 是的，会议，所以这是你反对的大事情之一是会议...</p>

        <p><strong>DHH：</strong> 会议是有毒的。这真的我认为与杰森和我联系。如果我必须计算我们在24年合作中有的会议总数，我们亲自坐在彼此面前并讨论主题，我可能它会少于无论什么，粉丝公司的三个月。我们只是没有那么多做那个。我们没有磨损它。</p>

        <p>这是特朗普在某个时候想出的有趣隐喻之一，人类在他们的生活中有有限数量的步骤，对吧？就像那是这里的长寿论点。你可以做如此多活动，然后你用完。在那个想法中有一些内核可以应用于关系。有一些我们可以有的交换量。</p>

        <p>有一些我们可以一起度过的时间量，你可以磨损它。杰森和我勤奋地不磨损彼此。我认为那绝对是关系长寿的关键，结合那种信任水平。然后只是结合我们真正喜欢工作本身的水平。我们不只是喜欢头脑风暴。</p>

        <p>说我们只是想出好想法的地方。不，我们喜欢做想法，我们喜欢直接自己成为那个过程的一部分。我喜欢编程，他喜欢做设计。我们可以去做我们的小事情长时间。在情况下你，聚在一起说，嘿，让我们启动一个伟大产品。</p>

        <p><strong>Lex：</strong> 这可能听起来像我要求你做治疗，但我发现自己有时想要或渴望会议，因为我孤独。就像因为远程工作只是独自坐着。我不知道，长时间它可能变得真的孤独。</p>

        <p><strong>DHH：</strong> 让我给你一个提示，找个妻子。</p>

        <p><strong>Lex：</strong> 是的，哦，该死。</p>

        <p><strong>DHH：</strong> 找几个孩子。</p>

        <p><strong>Lex：</strong> 好吧。</p>

        <p><strong>DHH：</strong> 就像家庭真的是孤独的伟大解药。</p>

        <p>我尽可能真诚地意味着那个。我肯定在我职业生涯早期有你描述的完全那种感觉，当我远程工作时，我只是一个，就像我住在公寓里。一个完全刻板印象，很长时间当我第一次搬到芝加哥时，我在地板上只有一个床垫，然后我买了这个大电视，我甚至没有安装它。然后我有一堆DVD。</p>

        <p>我基本上，我工作很多时间，然后我只是回家做那个。那不太好。它真的不是。就像我确实认为人类需要人类。如果你不能在工作中得到他们，我实际上有点不想在工作中要他们，至少我不想要他们一周40小时。那不是我喜欢的。</p>

        <p>你需要其他东西。你需要生活中的其他关系，如果你能找到你实际上只是想花很多时间的某人，没有更大的关系深度。那是它的关键。我认为对杰森和我来说，我们相当长时间有家庭是关键，它以这样的方式让你们两个扎根，创业的冲刺可以被交易为持久公司的马拉松。你以某种方式安定下来。我们简短地谈论了有时我被激怒。</p>

        <p>我的意思是很多时候，也许甚至大多数时候我对主题被激怒，但我现在不像我24岁时那样以同样的方式被激怒。我仍然对想法和试图找到正确的事情极其热情，但有家庭会议，我的妻子，围绕那个建立生活只是以完全陈词滥调的方式让一切平静下来。</p>

        <p>但我认为它实际上是关键。我认为如果我们能让更多甚至年轻人不等到他们在该死的30多岁后期或40多岁早期才与某人结合，我们会更好，我们也会有更稳定的商业关系，因为人们会在其他地方得到那种培养人类关系。</p>

        <p>现在当我说所有那些时，我也接受有很多伟大业务多年来已经建立，没有远程建立，已经由一群流氓坐在办公室无数小时建立。我的意思是，约翰·卡马克和蒂姆·斯威尼都在90年代谈论他们的职业生涯，那基本上只是工作，睡觉，与办公室的家伙闲逛，对吧？完全公平。那从来没有吸引我。</p>

        <p>杰森和我都在这个想法上看法一致，每周40小时专门用于工作是足够的，如果我们要走距离，不只是建立VC案例到退出需要的5到7年。但可能10年，20年或更远，我们需要成为完整的人类。因为只有那种完整的人性会走距离，包括在工作之外建立友谊，有爱好，找到伴侣并有家庭。</p>

        <p>那整个更高存在，工作不是生活中唯一事情的凳子的那些腿完全与我们已经存在25年的事实相关。有太多，特别是在美国的虚假权衡。哦，你想建立成功业务？好吧，你可以有金钱享受或家庭或健康选择一个。</p>

        <p>什么？为什么我们必须放弃所有这些？现在再次，我不是说，生活中有你可以冲刺的付费时刻。但我说如果那个冲刺变成十年，你会为它付出代价。你可以以方式为它付出代价。我一次又一次看到似乎是非常糟糕的交易，即使它有效。顺便说一下，大多数时候它没有。大多数时候创业公司破产。</p>

        <p>大多数时候人们花费五，七年或没有成功的东西，他们不得到支付，然后他们只是坐着后悔，我的20多岁发生了什么？早期，杰森和我基本上达成协议，一起工作不会导致那种后悔。我们会允许自己和彼此在工作之外建立完整生活。</p>

        <p>那个有效的事实是我感觉几乎像禁忌知识，肯定在美国的技术圈子中。这是我们试图倡导20年的东西，我们仍然得到松懈。就在两天前，我与某人有另一个Twitter争吵，说，"哦，好吧，也许它有效，但你没有变成Atlassian。所以你是失败。</p>

        <p>Basecamp不是Jira，所以你为什么甚至打扰？"这是如此迷人的赢家通吃心态，除非你在所有方式中主宰其他人，你已经失败了。当生活的如此多对多个赢家更开放，我们可以最终得到多年来赚了数亿美元的业务，我们保留了大部分来做我们想要的任何事情，那就足够了。那很好，那很棒。那实际上是值得渴望的东西。</p>

        <p>肯定它应该是某人考虑选择的路径，而不是主宰一切的VC独角兽或破产心态。</p>

        <p><strong>Lex：</strong> 是的，我想问你关于这个交换，所以你可以向我解释整个传奇，但只是稍微链接那个，我认为有一个概念，技术创始人的成功就像全力工作几年然后退出，有点为，我不知道，数亿美元出售你的公司。那是成功。当在现实中似乎，当你看像你这样的人，</p>

        <p>真正聪明，创造性的人类，他们实际上是谁以及快乐需要什么。它实际上需要一生稍微工作。就像因为你实际上喜欢编程，你喜欢建造，你喜欢设计师，你不想退出。那是你真正雄辩地谈论的东西。</p>

        <p>所以就像你实际上想创造一个生活，你总是在做建造，以不完全接管你生活的方式做它。</p>

        <p><strong>DHH：</strong> 莫吉托岛是海市蜃楼。它总是。对雄心勃勃的人没有退休。没有只是坐在海滩上啜饮莫吉托什么？两周前你变得该死疯狂并想回到行动中？那正是对大多数有能力建立那种退出的人发生的事情。</p>

        <p>我从来没有看到，我不应该说从来。我几乎从来没有看到任何人能够做到那个。然而如此多人认为那就是他们为什么这样做。那就是他们为什么牺牲一切。因为一旦我到达终点线，我是金色的。我赢了，我可以退休，我可以坐回去，我可以只是放松。你发现那种放松实际上是地狱。</p>

        <p>对创造性人来说，浪费他们上帝给予的创造性汁液和能力是地狱。我真的很幸运早期读了米哈伊·奇克森米哈伊的书"心流"</p>

        <p><strong>Lex：</strong> 好的，发音，完美。</p>

        <p><strong>DHH：</strong> 你知道什么？我必须在过去几天与AI练习那个，因为我知道我要引用他，我几次屠杀了他的名字。</p>

        <p>所以AI教我如何发音那个，至少有点正确。但他职业生涯的主要工作本质上是心流概念，来自寻找理解快乐的搜索。为什么一些人快乐？他们什么时候快乐？他学到的相当启发。</p>

        <p>他学到人们在莫吉托岛上坐着时不快乐。当他们摆脱所有义务和责任时，他们不快乐。不，他们在这些时刻快乐，他们正在达到和伸展他们的能力刚好超过他们目前能做的。在那些心流时刻，他们可以忘记时间和空间。</p>

        <p>他们可以坐在键盘前编程一个困难问题，认为20分钟过去了，突然已经三小时了。他们以最大快乐量回顾那些时刻。那就是顶峰快乐。如果你拿走对那种问题的追求，如果你从你的盘子中消除所有问题，你会抑郁。</p>

        <p>你不会有好时光。现在有人可以做那个，但他们不是建立这种公司的同样类型的人。所以你必须接受你是什么样的个人。如果你在这条路径上，不要胡说自己。不要胡说自己认为，我只是要牺牲一切，我的健康，我的家庭，我的爱好，我的朋友，但在10年中我要弥补一切，因为在10年中我可以做它。它从来不那样工作。</p>

        <p>它在它的两端都不工作。如果你成功并出售你的公司，它不工作，因为你会在退休两周后无聊得发疯。如果公司失败，你后悔最后10年为了什么都没有而花费，它不工作。如果一切都工作，你留在业务中，它不工作，因为它从来不变得更容易。</p>

        <p>所以如果你只是说，只有工作，没有其他，你会在所有指标上失败。我不想要那个。我想要心流的快乐。我理解那种洞察是真的，但我想以我可以持续旅程40或50年的方式做它。</p>

        <p><strong>Lex：</strong> 还有另一个有趣的警告，我听你说过，如果你确实退出，你出售你的公司，你想留在，你想做另一个公司，那通常不会那么充实。因为真的你的第一个婴儿，就像。</p>

        <p><strong>DHH：</strong> 你不能再做它，或大多数人不能再做它。A，因为他们的第二个想法不会像第一个那样好。在瓶子中捕获闪电如此罕见，就像我们例如与Basecamp有的。</p>

        <p>我从经验中知道这个，因为我一直试图从那时起建立很多其他业务。其中一些是适度成功，甚至好成功，它们都没有是Basecamp。两次做那个真的困难。但创始人是傲慢的刺，包括我自己。我们喜欢认为，你知道什么？我们成功很大程度上因为我们只是很棒。我们只是比其他人好得多。</p>

        <p>在某些方面，那有时是真的。但你也可以真的擅长在热门时刻重要的东西，那扇门是开的。门现在关闭，你仍然擅长那个东西，但它不重要。没有人关心。有那个部分。然后有它的部分，回到第一次体验事情只在第一次发生。你不能再做它。</p>

        <p>我不知道我是否有它在我身上再次经历早期的胡说八道。我以最亲爱的意义说胡说八道。做它都很棒。我知道太多。这是为什么，每当我被问问题，如果你能告诉你年轻的自己真的会的东西，你会对你年轻的自己说什么？我他妈的不会说任何事情。</p>

        <p>我不会抢夺我年轻自己的所有生活经历，我由于对世界如何工作的无知而被祝福。建立关于世界如何工作的智慧是快乐。你必须一次一个破坏地建立它，如果你只是交给所有结果。就像，哦，我们应该看你的电影吗？这是它如何结束。我现在不想他妈的看电影。你破坏了它。我不想你破坏我的商业经历。</p>

        <p>我不想破坏我的任何无知。当你开始新东西时，一半时间最大的祝福是A，你不知道它会有多难；B，你不知道你不知道什么。就像冒险是要付出，责任是要付出。这是乔丹·彼得森真正教我表达的东西。</p>

        <p>这个概念，责任实际上对我是关键。"人类寻找意义"，维克多·弗兰克尔也谈论这个，我们可以忍受任何困难，如果有为什么的原因。现在他在真正改变生活的集中营中谈论它。但你也可以在较小规模上应用，较少关键性，甚至只是你的日常生活，所有那些建立原始业务的困难，那是你承担的责任，吸引力。</p>

        <p>你承担那个的原因部分是因为你不完全知道它需要什么。如果我提前知道，如果我提前知道，它会有多难？沿途会有多少挫折？如果你只是在我开始之前在叙述中告诉我那个，我会像，呃，也许我应该只是去找工作。</p>

        <p><strong>Lex：</strong> 你在那里说了如此多聪明的事情。</p>

        <p>只是选择一个。有趣的是，有时建议给予者，智慧给予者已经经历了所有胡说八道。所以有一定程度你想犯错误。所以我认为我仍然会给建议，你想有你生活的一段，你工作太努力，包括失败的事情。</p>

        <p>我不认为你可以以任何其他方式学习为什么那是坏想法的教训，除了通过做它。有一定程度，但当然你不。</p>

        <p><strong>DHH：</strong> 我认为你应该伸展。你应该必须伸展十年吗？我不那么确定。</p>

        <p><strong>Lex：</strong> 是的，十年事情是20多岁是特殊时间。</p>

        <p><strong>DHH：</strong> 这是很多交易。你不会得到你的20多岁回来。</p>

        <p>是的，你不会得到你的30多岁回来。你不会得到你的40多岁回来。真的，我个人会后悔，如果我没有做我在20多岁做的其他事情，如果我没有我有的乐趣，如果我没有我有的朋友，如果我没有建立我做的爱好，如果我没有在足够早的年龄开始驾驶赛车以实际变得真的擅长它，如果我只是全力投入业务，因为我会在最后得到同样的。</p>

        <p>这是德里克·西弗斯真正教我的东西，他有这个关于当他去骑自行车时的伟大文章，他可以真的努力全力，他可以我认为在无论什么19分钟内做骑行。或者他可以享受骑行，慢5%，在21分钟内做骑行，意识到只有两分钟分开。</p>

        <p>要么我一直全力，没有其他，我在alt完全疲惫。或者我旅行同样距离，我也许晚两分钟到达，但我得到享受风景，听鸟，闻花。那个旅程也有价值。</p>

        <p>现在我说那个，同时接受和庆祝，如果你想成为世界上一件事的最好，不，你必须牺牲一切。你必须只对那件事着迷。没有世界上某件事最好的人不完全着迷的例子。我不需要成为任何事情的最好。</p>

        <p>这是我早期有的谦逊的罕见祝福，就像，你知道什么？我不那么聪明。我不那么好。我不那么有才华。我可以通过结合我知道的不同方面和元素做有趣的事情，但我不会成为任何事情的最好。那从这种单一痴迷中释放了我，只是说，我要成为世界上最好的程序员。呃，我知道我不是。我在甚至得到条件如何工作之前失败了两次。</p>

        <p>我不够聪明成为任何事情的最好。我不够专注做那个。那有点祝福。我认为作为社会，我们必须跨越庆祝顶峰卓越，我们一直做，庆祝成为那个需要的使命的顶峰强度。然后也说，你知道什么？我们都不需要成为迈克尔·乔丹。</p>

        <p>只会有其中一个。</p>

        <p><strong>Lex：</strong> 好吧，我们应该说有某些追求需要单一痴迷。篮球是其中之一。顺便说一下，可能赛车。如果你想成为世界上F1的最好...</p>

        <p><strong>DHH：</strong> 如果你想成为塞纳，你必须是疯子。</p>

        <p><strong>Lex：</strong> 但我会争论大多数学科像编程允许，如果你想成为，引用，"最好"，无论那意味着什么，我认为那在你生活结束时被判断。通常如果你看那条路径，它会是非线性的。它你不会看起来像奥林匹克运动员的单一焦点生活。</p>

        <p>会有20多岁的一些酸，或者会有几个绕道，真正的伟大。会有绕道。有时他们不会是史蒂夫·乔布斯资产类型的情况。他们只是你工作的不同公司，不同职业或你分配生活的不同努力。但它会是非线性的。它不会是单一焦点。</p>

        <p><strong>DHH：</strong> 我有时想这个的方式是我想要学习的好交易。我可以成为我定义为擅长某事的前5%，容易得多。也许它是20倍容易，100倍容易进入前5%，比进入前0.1%。那几乎不可能难进入那个。</p>

        <p>但如果我满足只是在前5%，我可以一次在五件事的前5%。我可以真的擅长写作。我可以在驾驶赛车上变得体面。我可以变得相当擅长编程。我可以运行公司。我可以有家庭。我可以同时做很多事情。那给我那种几乎被理想化的卡尔·马克思有这个想法的多样性，"哦，我要在早上钓鱼，在晚上锤击，在周末画画，对吧？"对我至少有一种感觉，他的异化诊断是真的。那只是那种隧道视野。只有这一件事我只是要专注于那个</p>

        <p>给我一种我不能忍受的异化感。当我真的深入编程，有时我深入几周，也许甚至在少数情况下几个月，我必须出来透气，我必须去做其他事情。就像，好吧，那是今年的编程。</p>

        <p>我已经做了我的部分，我要去骑行或在互联网上惹恼人们或驾驶一些赛车做其他事情。然后我可以明年再次以全强度做编程事情。</p>

        <p><strong>Lex：</strong> 说到在互联网上惹恼人们，你必须向我解释这个戏剧。好吧，所以这个说的家伙是什么，想象输给Jira但吹嘘你每年有几百万美元。</p>

        <p>所以这与这个现在几乎模因决定离开云有关。DHH离开了云。我认为那字面上是模因，但它也是迷人的决定。你能谈论DHH离开云的完整传奇吗？</p>

        <p><strong>DHH：</strong> 是的。</p>

        <p><strong>Lex：</strong> 离开AWS节省金钱。我猜这个人现在正在做的案例。</p>

        <p><strong>DHH：</strong> 是我们浪费时间优化一个如果我们只是追求月亮可能大100倍的业务。</p>

        <p><strong>Lex：</strong> 追求月亮包括？</p>

        <p><strong>DHH：</strong> 风险投资。</p>

        <p><strong>Lex：</strong> 但也...</p>

        <p><strong>DHH：</strong> 以及不关心成本的其他事情。</p>

        <p><strong>Lex：</strong> 但也因为AGI在拐角处，你应该一直投资AI，对吧？这只是部分？</p>

        <p><strong>DHH：</strong> 有点坦克生姜？我认为这是有点混乱的论点，但如果我们只是在它的顶峰理想中接受它，我实际上认为这是合理的点，是你可以近视地专注于数便士，当你应该专注于获得，对吧？我通过离开云优化了我们在基础设施上的支出</p>

        <p>那花了一些时间，我可以花那个时间制作更多功能，会吸引更多客户，或花更多时间与AI或做其他事情。机会成本是真实的。我不否认那个。</p>

        <p>我推回的想法是，对我们规模的公司每年在我们的基础设施账单上节省200万美元，大约在一半到三分之二之间直接到底线，这意味着它是对杰森或我作为所有者和我们员工的利润分享计划的一部分的回报，完全值得做。</p>

        <p>这个成本不重要的想法是非常硅谷的思维方式，再次，在某种规模的东西上理解也许，但我也实际上认为它在美学上不令人愉快。我发现低效业务，就像我发现充满线噪声的低效程序只是我大脑中的刺。我讨厌看费用报告，只是看到不成比例的浪费。</p>

        <p>当我几年前回顾我们在37signals的支出时，我看到没有通过我的嗅觉测试的账单。我记得我们在云之前曾经在基础设施上花费多少，我看到与我们需要的不成比例的数字。</p>

        <p>计算机随着时间变得如此快，事情不应该变得更便宜吗？为什么我们花越来越多钱服务更多客户？是的，但用更快的计算机，摩尔定律应该降低成本，相反的事情正在发生。为什么那发生？那开始了解开为什么云不像人们喜欢认为的那样伟大交易的旅程。</p>

        <p><strong>Lex：</strong> 是的，我们能看具体情况，只是为了不知道故事的人，然后概括它对云和技术业务的角色意味着什么。所以具体情况是你使用AWS S3为...</p>

        <p><strong>DHH：</strong> 我们为一切使用AWS。HEY.com作为完全云应用启动。它完全在AWS上用于计算，用于数据库，用于所有它。</p>

        <p>我们使用所有系统，因为他们最好规定我们应该。我们与AWS的总云账单，我们的总支出我认为在其顶峰是320万或340万。那有点很多钱。340万，我的意思是我们有大量用户和客户，但仍然那只是让我觉得不合理。</p>

        <p>它如此不合理的原因是因为我耳朵里响着云的推销。嘿，这会更快。这会更容易。这会更便宜。你为什么试图生产你自己的电力，对吧？就像，你有你自己的发电厂吗？你为什么要那样做？把计算机留给超大规模者。他们无论如何都更擅长它。我实际上认为那是令人信服的推销。</p>

        <p>我在那个推销上买了几年，想，你知道什么？我再也不拥有服务器了。我们只是要渲染我们的容量，亚马逊会能够为我们提供比我们自己能买的便宜得多的服务，因为他们会有这些规模经济。我想着杰夫的话响起。我的竞争对手的边际是我的机会。那是他曾经用来驱动amazon.com的东西。</p>

        <p>如果他可以只是赚2%，当另一个家伙试图赚4%时，他会最终得到所有钱。在量上，他仍然会赢。所以我认为那是AWS的操作精神。结果那根本不是真的。顺便说一下，AWS操作它几乎40%的边际。</p>

        <p>所以只是在那个中，有一个线索，竞争对手不能做我们喜欢资本主义的竞争事情，即降低成本等等。所以在我的光学中，云推销，它根本上是虚假的。首先它没有变得更容易。我不知道你最近是否使用过AWS，它是地狱复杂。</p>

        <p>如果你认为Linux难，你从来没有试图设置IAM规则或访问参数或AWS的任何东西。</p>

        <p><strong>Lex：</strong> AWS总是困难。它总是复杂。</p>

        <p><strong>DHH：</strong> 好吧，我认为它变得更困难。但是的，现在其中一些是它困难，因为它非常有能力，你有一堆容量在手。有原因我不认为它们足够好来证明整个jing-a-ma-jing变得如此复杂。但肯定真实的是它不再更容易。</p>

        <p>使用AWS不比运行你自己的机器更容易，我们当我们拉出云并没有雇佣单个额外的人时学到了。即使我们操作我们所有自己的硬件，团队保持完全相同。所以你有这个三方推销，对吧？它会更容易，它会更便宜。</p>

        <p>肯定不更便宜。我们刚刚通过将我们在基础设施上的支出削减一半到三分之二证明了那个。它会更快。最后一点是真的，但太多人高估了那种速度的价值。如果你需要一千台计算机在接下来的15分钟内在线，没有什么击败云。</p>

        <p>你甚至如何采购那个？如果我们只是需要另外20台服务器，得到装在托盘上的盒子运送到数据中心并拆包和机架，所有那些东西，对吧，会花一周或两周？但我们多久需要做那个？如果购买那些服务器便宜得多，我们得到相同金额的钱的大量更多计算，我们多久需要做那个？</p>

        <p>我们可以只是购买更多服务器，甚至不关心我们在计算实用程序上不是超优化的事实吗？我们不必使用像自动缩放这样的东西来弄清楚事情，因为我们必须减少成本？是的，我们可以。所以我们经历了这个旅程，在2023年早期的实现，当我最终受够了我们的账单。</p>

        <p>我想摆脱它们。我想花更少钱。我想自己保留更多钱。在刚好超过六个月中，我们将七个主要应用程序从云中移出，在计算缓存数据库方面到我们自己的服务器上工作。一个光荣，美丽的新舰队从服务器之王迈克尔·戴尔购买，顺便说一下，他真的是我的另一个偶像，我看到他刚刚庆祝了41年的业务。</p>

        <p>41年这个人一直在销售我们整个存在一直使用的很棒服务器。但无论如何，这些托盘在几周内到达，我们机架它们并让一切运行。我们出来了。至少与计算部分。然后我们有长期多年承诺S3，因为顺便说一下，在云中获得体面定价的唯一方式不是按日购买，不是按数据库基础租用，而是将自己绑定到多年合同，计算通常一年。</p>

        <p>那是我们的情况，存储是四年。我们签署了四年合同，将我们的客户文件的PB存储在云中，能够得到只是中途体面负担得起的东西。所以所有这些项目聚集到我们现在字面上节省数百万美元的感觉，预计五年约1000万。它总是困难。</p>

        <p>你如何准确地做会计，TOC这个，那个和其他东西。但它是数百万美元。但不只是那个。它也是离开云意味着回到互联网的更原始想法的事实。互联网不是这样设计的，三台计算机应该运行一切。</p>

        <p>它是分布式网络，这样个别节点可以消失，整个事情仍然会继续。DARPA设计了这个，这样俄国人可以拿下华盛顿，他们仍然可以从纽约反击，整个通信基础设施不会消失，因为没有中心和辐条。它是网络。我总是发现那是极其美丽的愿景。</p>

        <p>你可以有这个光荣的互联网，没有单个节点控制一切。我们已经回到更多单个节点控制一切的想法与这些超大规模者。当US-East-1，AWS的主要和原始区域离线时，多年来已经发生了不止几次。似乎互联网的三分之一离线。</p>

        <p>就像那本身只是对DARPA设计的侮辱。它只是从AWS建立的是奇妙的事实中减损。我认为云已经将如此多事情向前推进如此远，特别是围绕虚拟化，自动化设置。它是系统管理的所有那些巨大向前，现在允许我们能够以闻起来和感觉很像云的方式在本地运行事情，只是以一半成本或更少，以及拥有硬件的自主性和满足感。</p>

        <p>我不知道你上次看像实际服务器并拆开它并看里面是什么时候。这些东西是华丽的。我的意思是，我发布了我们在数据中心的机架的几张图片，人们总是为它们疯狂，因为我们在这个云时代已经变得如此抽象，从底层金属看起来像什么，大多数人没有想法。他们不知道现代CPU有多强大。</p>

        <p>他们不知道你可以在一个U机架中装多少RAM。计算进步真的令人兴奋，特别是我会说在过去四到五年中，TSMC在苹果的帮助下真正推动了信封。我的意思是，我们有点坐在那里一段时间，而英特尔在旋转他们的轮子无处可去。</p>

        <p>然后TSMC与苹果推动他们真正向前移动事情。现在服务器又令人兴奋了。就像你年复一年得到跳跃，15，20%而不是我们被困的单数字。那都意味着拥有你自己的硬件是比以往任何时候都更可行的命题。</p>

        <p>你需要更少机器运行更多，更多人应该做它。因为尽管我爱杰夫和亚马逊，就像他不需要另一个无论什么，我购买运行我们业务的所有技术东西的40%边际。这只是我一直专注的东西。既因为围绕尊重DARPA原始设计的意识形态，运行我们自己硬件的实用性，看我们可以用最新机器推动事情多快，然后节省钱。</p>

        <p>那都如此享受做，但也对很多人如此反直觉，因为似乎，我认为对行业中很多人来说，就像我们都决定我们完成了购买计算机，那是我们只是委托给AWS和Azure和谷歌云的东西，我们不必再拥有这些东西。所以我认为对一些人有一点鞭打，"哦，我认为我们同意了。</p>

        <p>我们完成了那个。"然后我们来说，"啊，你知道什么？也许你应该有计算机。"</p>

        <p><strong>Lex：</strong> 运行你自己的服务器有一些痛点吗？</p>

        <p><strong>DHH：</strong> 哦，很多。操作各种计算机有痛点。你试过只是像这些天使用个人计算机吗？一半时间当我的孩子或我的妻子有问题时，我说，你试过只是关闭并再次打开吗。计算机对人类本质上是痛苦的。</p>

        <p>拥有你自己的计算机虽然有点让一些那种痛苦值得。有责任伴随实际拥有硬件，对我，至少让操作那个硬件的负担似乎稍微更享受。现在有你必须学习的事情，肯定在我们的规模也。</p>

        <p>我的意思是，我们不只是购买单个计算机并插入以太网。我们必须有机架和机架与它们，你必须用网络电缆设置它，在那个中有一些专门专业知识，但它不像那个专业知识像建造核火箭。它不像它不广泛分布。</p>

        <p>字面上整个互联网建立在人们知道如何将计算机插入互联网，对吧？哦，以太网电缆去这里，电源电缆去这里，让我们启动Linux。那是每个人在10，12年前云有点接管之前将任何东西放在线上的方式。所以专业知识在那里，可以重新发现。你也可以学习如何操作Linux计算机。</p>

        <p><strong>Lex：</strong> 是的，当你得到一堆它们时，有一堆闪烁LED，它只是如此令人兴奋。</p>

        <p><strong>DHH：</strong> 哦，它们美丽，平静，令人惊叹。计算机真的有趣。这实际上是我在我们搬出云后甚至更深入的东西。现在我的下一种刺痛是，如果你可以搬出云，你也可以搬出数据中心吗？个人服务器变得真的可怕地快和高效，个人互联网连接与我们只是十年或二十年前连接数据中心的竞争。所以有围绕这个家庭实验概念的整个社区，本质上在你自己的公寓中安装服务器硬件，将它连接到互联网，并直接向互联网暴露那个。那回到90年代的那些光荣日子，当为互联网建造的人会在他们在壁橱中的实际计算机上托管实际网站。</p>

        <p>我对那个相当激动。我正在做一堆实验。我已经从我自己的公寓订购了一堆家庭服务器。我惊叹于我现在可以得到五千兆位，五连接，我认为，你知道五千兆位可能已经将Basecamp带到数百万MRR，以当时的方式，我在2004年技术或可能一百兆位电缆的单个盒子上运行整个业务。</p>

        <p>就像我们在计算和连接方面有访问的容量是人们没有重新调整的东西。这有时在技术中发生，进步偷偷接近你。这与SSD发生。顺便说一下，我喜欢那个。我们围绕有某些寻求率属性的旋转金属磁盘设计了如此多我们的技术和存储方法和数据库设计。</p>

        <p>然后我们去了NVMe和SSD，人们花了相当长时间意识到系统现在必须根本不同地建造。当你不旋转这些金属板与必须从它们读取的小头时，内存和磁盘之间的差异现在要小得多。</p>

        <p>你本质上只是处理另一种类型的内存。我认为当涉及新业务字面上从你该死的卧室启动的容量时，我们有点在同样阶段。</p>

        <p><strong>Lex：</strong> 所以你可以用家庭实验与大用户基础走得相当远？</p>

        <p><strong>DHH：</strong> 绝对地。</p>

        <p><strong>Lex：</strong> 那令人兴奋。那就像老学校。</p>

        <p>那真的令人兴奋，对吧？</p>

        <p><strong>DHH：</strong> 它在字面物理意义上带回车库中的创业。现在其中一些是我们需要吗，如果你不需要很多，你可以得到相对便宜的云容量。</p>

        <p><strong>Lex：</strong> 地狱是的，我们需要。我的意思是自己做那个的感觉，在你自己家中看LED灯。我的意思是没有什么像那个。</p>

        <p><strong>DHH：</strong> 有只是我完全爱上的美学，我想试图推动。现在它不会是与离开云同样的事情。我不确定我们离开云的退出不是离开数据中心的退出。我们基本上只是购买硬件，将它运送到我们甚至实际上没有触摸的专业管理数据中心。</p>

        <p>这是人们对搬出云有的另一个误解。我们有一堆人不断驾驶到某处数据中心机架新盒子并更换死RAM。那根本不是现代世界中事情如何发生。我们有一个叫Summit的公司，以前Deft，那是我们称为白手套的。</p>

        <p>当我们需要像"嘿Deft，你能下去并交换六号盒子中的死SSD吗？"时，他们在数据中心工作。他们做它。我们看到的类似于与云工作的某人会看到的。你看到IP地址在线。你看到驱动器在线。它不那么不同，但当你在我们的规模上操作时，它是整个地狱便宜得多。当然它是。</p>

        <p>当然，如果你需要那些东西年而不是租用它，拥有东西更便宜。在没有其他领域中，我们会混淆那两个事情，长期拥有比租用更便宜。</p>

        <p><strong>Lex：</strong> 有一些灰色区域，就像我有机会与xAI团队一堆互动。我可能回到孟菲斯那里做与Grok发布相关的大播客。</p>

        <p>那些人，为了实现建立集群的速度并解决与GPU，与训练有关的一些新颖方面，他们必须更亲自动手。它是较少白手套。</p>

        <p><strong>DHH：</strong> 哦，我喜欢那个，对吧？他们处理前沿问题，他们不通过从他们的主要竞争对手以巨大加价租用一堆GPU来处理它。</p>

        <p>他们说，"不，去那个。我们要在我们自己的帐篷中放十万GPU，"对吧？并在绝对记录时间内建造它。所以我认为如果有什么，这是拥有硬件可以在小规模，中等规模和计算先锋水平给你优势的想法的证明。</p>

        <p><strong>Lex：</strong> 顺便说一下，你知道，说到团队，那些是xAI，特斯拉是大公司，但所有那些人，我不知道那是什么，你说杰夫真的擅长找到好人，在人们中看到力量。就像埃隆也极其，我不知道那是什么。实际上，我从来没有实际看到，也许你可以说到那个。他擅长找到伟大。</p>

        <p><strong>DHH：</strong> 我不认为他找到那么多，他吸引。他因为他的目标和使命的大胆而吸引人才。他陈述它的清晰度。他不必搜索地球找到最好的人。</p>

        <p>最好的人来到他那里，因为他，这里谈论埃隆，是最单一令人振奋的人物之一。在宇宙这里的同样秩序中，仇恨者和爱好者，对吧？就像他在如此规模上产生如此影响，当然他必须有字面上数百万人认为他是世界上最糟糕的人，他也会有数百万人认为他是人类最伟大的礼物。取决于日子。我在中间某处。</p>

        <p>但我更在人类最伟大礼物的规模端，而不是在另一端。我认为那真的以我们几乎忘记那种大胆水平如此罕见的方式激励人们，当我们看到它时，我们不完全知道如何分析它。我们认为埃隆找到伟大人才，我确信他也擅长那个。</p>

        <p>但我也认为这个使命的灯塔，我们要他妈的去火星。我们要将交通转变为使用电力。我们要用互联网覆盖地球，如此宏大，以至于有些日子我醒来说，我他妈的在用这些待办事项清单做什么？就像耶稣，我应该去注册那样的东西吗？是的，那在某种意义上听起来令人振奋。</p>

        <p>我只能想象像在10，50年代骑自行车回去说，我们应该去诺曼底吗？你可能在路上死去，但哦，男孩，那听起来像旅程和冒险。</p>

        <p><strong>Lex：</strong> 有几个组成部分。有一个肯定是这个比生命更大的使命并真正相信它。你知道，每隔一句话是关于火星，就像真正相信它。它真的不重要什么，就像任何其他人，批评，任何东西。有非常单一专注的大使命。</p>

        <p>但我认为它也与一堆其他组成部分有关。就像一旦人们，一旦灯塔吸引，能够很好地雇佣。我刚刚看到在纸面上不一定有简历与记录的人。我看到真的现在结果是传奇人物。他基本上像向他们抛出领导力的球。</p>

        <p>在他们中看到一些东西并说，"你去。"并给他们所有权，他们与它一起跑。那在每个规模上发生。有真正的精英制度。就像有一些东西，只是像，你可以在这些会议中看到人类智力的繁荣，在这些群体聚集中，他们像，能量是可感知的。对我来说，只是在那周围就令人兴奋。</p>

        <p>没有很多公司我看到那个，因为当公司变得成功和更大时，它以某种方式窒息那种能量。我猜你在早期阶段的创业公司中看到。但就像在实际能够实现规模的大公司中看到它很酷，你知道？</p>

        <p><strong>DHH：</strong> 我认为那里的秘密的一部分是埃隆实际上知道事情。</p>

        <p>当你知道事情时，你可以评估工作产品的质量。当你可以评估工作产品的质量时，你可以非常快速地告诉谁充满胡说八道，谁实际上会带你去火星。你可以解雇充满胡说八道的人，你可以押注让我们到火星的人。</p>

        <p>直接评估个人能力的那种能力，它实际上有点罕见。它不在经理，雇佣经理中广泛分布。它不是你可以容易地委托给在工作本身不非常熟练的人的东西。埃隆显然知道很多关于很多，他可以闻到谁真正知道东西。</p>

        <p>这在我们的小规模上，我试图以同样的顺序做的东西，例如，当我们雇佣程序员时，现在与AI作为新挑战会很有趣。但直到这一点，被雇佣的主要支点不是你的简历，不是你有的学校教育，不是你的成绩，不是你的血统，是你在两件事上做得多好。</p>

        <p>A，你的求职信。因为如果他们是好作家，我只能与人们远程工作。所以如果你不能写适当的大写字母，不能费心努力专门为我们写它，你出局了。二，你必须能够真的很好地编程，到我可以看你的代码并说，是的，我想与那个人工作的程度。</p>

        <p>不仅我想与那个人工作，当我必须在五年后再次看到它来修复一些该死的错误时，我想在那个人的代码上工作。所以我们要给你一个编程测试，模拟我们真正工作的方式，我们要看你如何做。我一次又一次地惊讶，我肯定认为这个候选人是鞋入，他们听起来正确，简历正确，然后你看到代码被交上来。我想，没门。我们绝对不雇佣这个人。</p>

        <p>另一种方式也是真的。我说，我不知道这个家伙或这个女人。呃，我不知道。然后他们交上他们的教练东西，我想，神圣的狗屎，那个人明天可以在我的团队上，最好？评估工作产品的能力在雇佣时是超能力。</p>

        <p><strong>Lex：</strong> 有一个步骤我看到埃隆做得真的很好，就是能够出现并说，这可以做得更简单。</p>

        <p><strong>DHH：</strong> 是的。</p>

        <p><strong>Lex：</strong> 但他知道他在谈论什么。然后工程师，因为埃隆知道足够，工程师的第一反应，你有点可以告诉，就像如果你的父母告诉你一些东西，几乎像翻白眼。这不是，不，我们已经我在这上面工作了一个月，你不...</p>

        <p>但然后当你有那个对话更多一点时，你意识到不，它可以做得更简单。找到方式。所以当两个工程师交谈时有好的，一个可能没有完美信息。但如果高级工程师有像好本能，那是像战斗赢得的，然后你可以说简化。它实际上会导致简化。</p>

        <p><strong>DHH：</strong> 我认为这是真正伟大的标志。他们不仅对做工作需要什么有洞察，而且他们也有超越工程师会做的，程序员会做的超越愿景。我认为如果我们看这些稀有性，显然史蒂夫·乔布斯的神话也是这个。</p>

        <p>即使也许他在许多方面比埃隆技术性更少，他有同样的能力出现在产品团队并真正挑战他们更努力地寻找简化或以会从应该做它的人那里获得不信的方式让事情更伟大。就像这个家伙充满胡说八道。</p>

        <p>就像这疯狂。我们永远不能，然后两个月后它是。所以有一些这个，你需要愿景，你需要它由知道足够关于什么是可能的现实锚定，知道足够关于物理，知道足够关于软件，你不只是建造胡说八道。有很多人可以告诉一群工程师。不，只是做得更快。就像那不是技能。</p>

        <p>它必须锚定在一些真实的东西中，但它也会锚定在，它是疲倦的词，但对结果的热情到你如果做糟糕工作会个人受到侮辱的程度。这是我最近一直在写关于苹果的。</p>

        <p>他们失去了那个会出现并告诉工程师他们做的不够好的混蛋，以实际上也许会让他们在那一刻感觉有点小的方式，但会激发那种真正修复它的热情。现在他们有物流人员，非常擅长采购组件和排列生产甘特图，但你不得到那种魔力。</p>

        <p>现在那整个场景有趣的是，我实际上认为蒂姆·库克运行事情和在苹果运行事情如此长时间多好，也许我们错了。也许我们对史蒂夫·乔布斯对整个使命的关键性错了。也许你可以逃脱不有它。我认为账单会稍后来，现在它有了。</p>

        <p>苹果在所有这些方式中失败，会炸毁史蒂夫的鬼魂并真正侮辱他的某人会说，看？这就是现在正在发生的。所以这里的另一件事，当然，是不可能离婚。</p>

        <p>就像你对什么是系统关键组成部分的感知和生活现实中一百万不同移动部分的混乱现实。你应该一直对你自己的分析和你自己的论文持怀疑态度。</p>
    </div>
</body>
</html>