# PRP (Product Requirements Prompt) 模板集合

> 适用于 Augment AI 环境的各种 PRP 模板

## 基础 PRP 模板

### 标准功能开发 PRP

```markdown
# PRP: [功能名称]

## 📋 基本信息
- **功能名称**: [简洁的功能名称]
- **优先级**: [高/中/低]
- **预估工作量**: [小时数或故事点]
- **负责人**: [开发者姓名]
- **创建日期**: [YYYY-MM-DD]
- **目标完成日期**: [YYYY-MM-DD]

## 🎯 功能概述
**目标**: [用一句话描述要实现的功能]

**业务价值**: [这个功能为用户或业务带来什么价值]

**用户故事**: 
作为 [用户角色]，我希望 [功能描述]，以便 [获得的价值]。

## ✅ 成功标准

### 功能标准
- [ ] [具体的功能要求1]
- [ ] [具体的功能要求2]
- [ ] [具体的功能要求3]

### 质量标准
- [ ] 代码覆盖率 ≥ 80%
- [ ] 所有静态分析检查通过
- [ ] 性能要求满足（如适用）
- [ ] 安全要求满足（如适用）

### 用户体验标准
- [ ] [用户体验要求1]
- [ ] [用户体验要求2]

## 📚 上下文信息

### 相关代码模式
```markdown
使用 codebase-retrieval 查询：
"查找与 [功能相关的关键词] 相关的代码模式，包括：
- 类似功能的实现方式
- 使用的框架和库
- 数据模型和接口设计
- 错误处理模式"
```

### 架构约束
- [现有架构的限制和要求]
- [必须遵循的设计模式]
- [技术栈限制]

### 依赖关系
- **内部依赖**: [项目内部的模块依赖]
- **外部依赖**: [需要的第三方库或服务]
- **数据依赖**: [需要的数据源或数据库变更]

## 🔧 技术实现计划

### 阶段1：准备和设计 (预估: X小时)
- [ ] 分析现有代码模式
- [ ] 设计数据模型（如需要）
- [ ] 设计API接口（如需要）
- [ ] 创建技术设计文档

### 阶段2：核心实现 (预估: X小时)
- [ ] 实现核心业务逻辑
- [ ] 实现数据访问层（如需要）
- [ ] 实现API端点（如需要）
- [ ] 实现用户界面（如需要）

### 阶段3：测试和验证 (预估: X小时)
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 执行手动测试
- [ ] 性能测试（如需要）

### 阶段4：文档和部署 (预估: X小时)
- [ ] 更新API文档
- [ ] 更新用户文档
- [ ] 部署到测试环境
- [ ] 部署到生产环境

## ✅ 验证检查点

### 开发阶段验证
```bash
# 代码质量检查
[项目特定的代码质量检查命令]

# 类型检查
[项目特定的类型检查命令]

# 安全检查
[项目特定的安全检查命令]
```

### 测试阶段验证
```bash
# 单元测试
[运行单元测试的命令]

# 集成测试
[运行集成测试的命令]

# 端到端测试
[运行端到端测试的命令]

# 测试覆盖率检查
[检查测试覆盖率的命令]
```

### 部署阶段验证
- [ ] 构建过程成功
- [ ] 部署脚本执行成功
- [ ] 服务健康检查通过
- [ ] 关键功能冒烟测试通过

## 🚨 风险和注意事项

### 技术风险
- [潜在的技术风险和缓解措施]

### 业务风险
- [潜在的业务风险和缓解措施]

### 依赖风险
- [外部依赖的风险和备选方案]

## 📖 参考资料
- [相关的API文档链接]
- [设计文档链接]
- [相关的技术文章或教程]

## 📝 实施记录

### 决策记录
- [重要的技术决策和原因]

### 问题和解决方案
- [实施过程中遇到的问题和解决方案]

### 经验教训
- [从这次实施中学到的经验]
```

---

## API 开发专用 PRP 模板

```markdown
# API PRP: [API名称]

## 📋 API 基本信息
- **API 名称**: [RESTful API 名称]
- **版本**: [v1.0]
- **基础路径**: [/api/v1/...]
- **认证方式**: [JWT/OAuth/API Key等]

## 🎯 API 概述
**目的**: [API的主要用途和目标]

**目标用户**: [API的使用者：前端应用、第三方服务等]

## 📡 API 规范

### 端点定义
```yaml
# OpenAPI 3.0 格式的 API 定义
paths:
  /api/v1/resource:
    get:
      summary: [获取资源列表]
      parameters:
        - name: page
          in: query
          schema:
            type: integer
      responses:
        200:
          description: 成功返回资源列表
    post:
      summary: [创建新资源]
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceCreate'
      responses:
        201:
          description: 资源创建成功
```

### 数据模型
```yaml
components:
  schemas:
    Resource:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        created_at:
          type: string
          format: date-time
```

### 错误处理
```yaml
components:
  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
              details:
                type: array
                items:
                  type: string
```

## ✅ API 成功标准
- [ ] 所有端点按规范实现
- [ ] 输入验证完整
- [ ] 错误处理统一
- [ ] 响应格式一致
- [ ] 性能要求满足
- [ ] 安全要求满足
- [ ] API 文档完整

## 🔧 实现计划

### 阶段1：设计和规范
- [ ] 完善 API 规范
- [ ] 设计数据模型
- [ ] 定义错误码和消息

### 阶段2：核心实现
- [ ] 实现路由和控制器
- [ ] 实现业务逻辑层
- [ ] 实现数据访问层
- [ ] 实现输入验证

### 阶段3：安全和性能
- [ ] 实现认证和授权
- [ ] 实现速率限制
- [ ] 实现缓存策略
- [ ] 性能优化

### 阶段4：测试和文档
- [ ] API 单元测试
- [ ] API 集成测试
- [ ] 生成 API 文档
- [ ] 编写使用示例

## ✅ 验证检查点

### API 测试
```bash
# API 测试命令
curl -X GET "http://localhost:8000/api/v1/resource" \
     -H "Authorization: Bearer $TOKEN"

# 自动化 API 测试
npm run test:api
```

### 性能测试
```bash
# API 性能测试
ab -n 1000 -c 10 http://localhost:8000/api/v1/resource
```

### 安全测试
- [ ] 认证测试
- [ ] 授权测试
- [ ] 输入验证测试
- [ ] SQL 注入测试
- [ ] XSS 防护测试
```

---

## 数据库变更 PRP 模板

```markdown
# 数据库变更 PRP: [变更名称]

## 📋 变更基本信息
- **变更类型**: [新建表/修改表/删除表/数据迁移]
- **影响范围**: [影响的表和数据量]
- **风险级别**: [高/中/低]
- **回滚策略**: [如何回滚变更]

## 🎯 变更目标
**目的**: [为什么需要这个数据库变更]

**业务需求**: [支持的业务功能]

## 📊 数据库设计

### 新建表结构
```sql
CREATE TABLE example_table (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_example_email ON example_table(email);
CREATE INDEX idx_example_created_at ON example_table(created_at);
```

### 修改现有表
```sql
-- 添加列
ALTER TABLE existing_table 
ADD COLUMN new_column VARCHAR(100);

-- 修改列
ALTER TABLE existing_table 
ALTER COLUMN existing_column TYPE TEXT;

-- 添加约束
ALTER TABLE existing_table 
ADD CONSTRAINT fk_constraint 
FOREIGN KEY (foreign_id) REFERENCES other_table(id);
```

### 数据迁移脚本
```sql
-- 数据迁移逻辑
INSERT INTO new_table (column1, column2)
SELECT old_column1, old_column2 
FROM old_table 
WHERE condition;
```

## ✅ 变更成功标准
- [ ] 数据库结构变更成功
- [ ] 数据迁移完整无误
- [ ] 应用程序兼容性验证
- [ ] 性能影响在可接受范围内
- [ ] 备份和回滚方案验证

## 🔧 实施计划

### 阶段1：准备
- [ ] 数据库备份
- [ ] 变更脚本准备
- [ ] 回滚脚本准备
- [ ] 影响评估

### 阶段2：测试环境验证
- [ ] 在测试环境执行变更
- [ ] 应用程序测试
- [ ] 性能测试
- [ ] 回滚测试

### 阶段3：生产环境实施
- [ ] 维护窗口安排
- [ ] 生产环境备份
- [ ] 执行变更脚本
- [ ] 验证变更结果

## ✅ 验证检查点

### 数据完整性检查
```sql
-- 检查数据迁移结果
SELECT COUNT(*) FROM new_table;
SELECT COUNT(*) FROM old_table WHERE migrated = false;

-- 检查约束
SELECT * FROM information_schema.table_constraints 
WHERE table_name = 'target_table';
```

### 性能影响检查
```sql
-- 检查查询性能
EXPLAIN ANALYZE SELECT * FROM table_name WHERE condition;

-- 检查索引使用
SELECT * FROM pg_stat_user_indexes WHERE relname = 'table_name';
```

## 🚨 风险和回滚

### 风险评估
- **数据丢失风险**: [评估和缓解措施]
- **性能影响**: [预期的性能影响]
- **应用程序兼容性**: [可能的兼容性问题]

### 回滚计划
```sql
-- 回滚脚本
DROP TABLE IF EXISTS new_table;
ALTER TABLE modified_table DROP COLUMN new_column;
-- 恢复数据的具体步骤
```
```

---

## 重构 PRP 模板

```markdown
# 重构 PRP: [重构目标]

## 📋 重构基本信息
- **重构范围**: [模块/类/函数级别]
- **重构类型**: [性能优化/代码清理/架构改进]
- **风险级别**: [高/中/低]
- **预估工作量**: [小时数]

## 🎯 重构目标
**当前问题**: [现有代码的问题和痛点]

**期望改进**: [重构后期望达到的状态]

**业务价值**: [重构带来的业务价值]

## 📊 现状分析

### 代码质量问题
- [具体的代码质量问题]
- [技术债务描述]
- [维护困难点]

### 性能问题
- [性能瓶颈分析]
- [资源使用问题]

### 架构问题
- [架构设计问题]
- [模块耦合问题]

## 🔧 重构策略

### 重构方法
- **策略**: [大重构/小步重构/渐进式重构]
- **工具**: [使用的重构工具]
- **测试策略**: [如何保证重构安全性]

### 重构步骤
1. **准备阶段**
   - [ ] 增加测试覆盖率
   - [ ] 建立性能基准
   - [ ] 创建重构分支

2. **重构实施**
   - [ ] [具体的重构步骤1]
   - [ ] [具体的重构步骤2]
   - [ ] [具体的重构步骤3]

3. **验证阶段**
   - [ ] 运行所有测试
   - [ ] 性能对比验证
   - [ ] 代码审查

## ✅ 重构成功标准
- [ ] 所有现有测试通过
- [ ] 代码质量指标改善
- [ ] 性能不降低（或按预期提升）
- [ ] 新代码更易维护
- [ ] 文档更新完成

## ✅ 验证检查点

### 功能验证
```bash
# 运行完整测试套件
npm test

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e
```

### 性能验证
```bash
# 性能基准测试
npm run benchmark

# 内存使用分析
npm run profile:memory

# CPU 使用分析
npm run profile:cpu
```

### 代码质量验证
```bash
# 代码复杂度分析
npm run complexity

# 代码覆盖率检查
npm run coverage

# 静态分析
npm run lint
```

## 🚨 风险控制

### 风险识别
- **功能回归风险**: [可能的功能回归]
- **性能回归风险**: [可能的性能问题]
- **集成风险**: [与其他模块的集成问题]

### 缓解措施
- **测试保护**: [全面的测试覆盖]
- **渐进式部署**: [分阶段发布策略]
- **监控告警**: [关键指标监控]
- **快速回滚**: [回滚方案]

## 📖 重构记录

### 重构决策
- [重要的重构决策和原因]

### 遇到的问题
- [重构过程中的问题和解决方案]

### 经验总结
- [重构经验和最佳实践]
```

---

## 使用指南

### 选择合适的模板
- **新功能开发**: 使用标准功能开发 PRP
- **API 开发**: 使用 API 开发专用 PRP
- **数据库变更**: 使用数据库变更 PRP
- **代码重构**: 使用重构 PRP

### 定制模板
1. 根据项目特点调整模板内容
2. 添加项目特定的验证检查点
3. 更新工具命令和脚本
4. 调整风险评估标准

### 模板演进
- 定期回顾和更新模板
- 收集团队反馈改进模板
- 根据项目经验优化流程

---

*将这些模板保存到 `.augment/prps/templates/` 目录中，并根据具体项目需求进行定制。*
