#!/usr/bin/env python3
"""
创建包含完整内容的DHH访谈EPUB电子书
"""

import re
import os
from pathlib import Path

def read_original_file():
    """读取原始翻译文件"""
    with open("DHH谈编程未来、AI、Ruby on Rails、生产力与育儿_中文翻译.txt", "r", encoding="utf-8") as f:
        return f.read()

def split_content_by_sections(content):
    """根据标题分割内容"""
    # 定义章节分割点
    chapter_markers = [
        ("第一章：编程学习之路与早期经历", "## DHH的编程学习之路"),
        ("第二章：发现Ruby的魔力", "## 发现Ruby的魔力"),
        ("第三章：AI与编程的未来", "## 动态类型的哲学"),
        ("第四章：Ruby on Rails的诞生与理念", "## Rails的愿景与教条"),
        ("第五章：创业历程与37signals", "## 关于合作伙伴关系的讨论"),
        ("第六章：与苹果的斗争", "## 关于苹果的讨论"),
        ("第七章：育儿体验与家庭价值观", "## 关于育儿的讨论"),
        ("第八章：赛车运动的激情", "## 关于赛车的讨论"),
        ("第九章：编程工具与开发环境", "## 关于编程设置的讨论"),
        ("第十章：编程语言的选择与观点", "## 关于开源的讨论"),
        ("第十一章：开源项目的管理哲学", "## 关于开源的讨论"),
        ("第十二章：人生哲学与成功定义", "感谢听这个与DHH的对话")
    ]
    
    chapters = {}
    lines = content.split('\n')
    
    # 找到每个标记的位置
    marker_positions = {}
    for i, line in enumerate(lines):
        for title, marker in chapter_markers:
            if marker in line:
                marker_positions[title] = i
                break
    
    # 提取每章内容
    sorted_chapters = sorted(marker_positions.items(), key=lambda x: x[1])
    
    for i, (title, start_pos) in enumerate(sorted_chapters):
        if i < len(sorted_chapters) - 1:
            end_pos = sorted_chapters[i + 1][1]
        else:
            end_pos = len(lines)
        
        chapter_content = '\n'.join(lines[start_pos:end_pos])
        chapters[title] = chapter_content
    
    return chapters

def create_chapter_xhtml(chapter_num, title, content, intro):
    """创建章节XHTML文件"""
    # 清理和格式化内容
    content = content.replace('**Lex：**', '<p><strong>Lex：</strong>')
    content = content.replace('**DHH：**', '</p>\n\n        <p><strong>DHH：</strong>')
    content = re.sub(r'\n\n+', '</p>\n\n        <p>', content)
    
    # 移除标题行
    content = re.sub(r'## [^\\n]+\\n', '', content)
    
    template = f'''<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>{title}</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第{chapter_num}章</div>
        <h1 class="chapter-title">{title.replace(f"第{chapter_num}章：", "")}</h1>
    </div>
    
    <div class="chapter-intro">
        <p>{intro}</p>
    </div>

    <div class="conversation">
        {content}
    </div>
</body>
</html>'''
    
    return template

def main():
    """主函数"""
    print("读取原始文件...")
    content = read_original_file()
    
    print("分割章节内容...")
    chapters = split_content_by_sections(content)
    
    print(f"找到 {len(chapters)} 个章节")
    for title in chapters.keys():
        print(f"  - {title}")
    
    # 章节介绍
    chapter_intros = {
        "第一章：编程学习之路与早期经历": "DHH的编程之路并非一帆风顺。从童年时期对Commodore 64的渴望，到多次学习编程的失败尝试，再到最终在互联网时代找到自己的方向，这段经历展现了一个传奇程序员的成长轨迹。",
        "第二章：发现Ruby的魔力": "从PHP到Ruby的转变，标志着DHH编程生涯的重要转折点。Ruby不仅仅是一门编程语言，更像是为他的大脑量身定制的完美工具。",
        "第三章：AI与编程的未来": "人工智能正在改变编程的面貌，但DHH对此有着独特而深刻的见解。他既拥抱AI作为编程助手，又坚持认为程序员必须保持对代码的直接控制。",
        "第四章：Ruby on Rails的诞生与理念": "Ruby on Rails不仅仅是一个Web开发框架，它是一种哲学的体现。本章深入探讨Rails的核心理念、设计原则，以及它如何改变了Web开发的面貌。",
        "第五章：创业历程与37signals": "从一个小型设计咨询公司到创造出影响数百万人的产品，37signals的发展历程体现了DHH对小团队、简单解决方案和可持续发展的信念。",
        "第六章：与苹果的斗争": "DHH与苹果App Store的冲突成为了科技界的一个标志性事件，揭示了大科技公司的垄断行为。本章详述了HEY应用被拒绝的经过。",
        "第七章：育儿体验与家庭价值观": "成为父亲彻底改变了DHH的人生观。本章探讨了育儿如何影响他的工作方式、生活优先级，以及对传统家庭价值观的重新思考。",
        "第八章：赛车运动的激情": "从25岁才获得驾照到在勒芒24小时耐力赛中获得组别冠军，DHH的赛车之路展现了他对追求卓越的执着。",
        "第九章：编程工具与开发环境": "从Mac到Linux的转变，从TextMate到Neovim的选择，DHH对编程工具的偏好反映了他对简洁和效率的追求。",
        "第十章：编程语言的选择与观点": "DHH对编程语言有着强烈而明确的观点。本章探讨了他对不同编程语言的看法，特别是他对TypeScript的批评。",
        "第十一章：开源项目的管理哲学": "作为Ruby on Rails的创造者和维护者，DHH对开源项目管理有着独特的见解。本章探讨了他的仁慈独裁者理念。",
        "第十二章：人生哲学与成功定义": "在事业成功的背后，DHH有着深刻的人生哲学。本章探讨了他对金钱、成功、幸福的看法，以及对未来的乐观态度。"
    }
    
    # 创建完整的章节文件
    for i, (title, chapter_content) in enumerate(chapters.items(), 1):
        chapter_num = f"{i:02d}"
        intro = chapter_intros.get(title, "本章内容精彩纷呈，值得细读。")
        
        xhtml_content = create_chapter_xhtml(chapter_num, title, chapter_content, intro)
        
        filename = f"DHH访谈EPUB/OEBPS/Text/chapter{chapter_num}.xhtml"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xhtml_content)
        
        print(f"创建完整章节: {filename}")
    
    print("所有章节创建完成！")

if __name__ == "__main__":
    main()
