# Augment AI Context Engineering 快速开始指南

> 5分钟内开始使用 Context Engineering 方法论提升您的 AI 辅助开发体验

## 🚀 立即开始

### 第一步：设置项目结构 (2分钟)

在您的项目根目录创建 Context Engineering 结构：

```bash
# 创建 Augment AI Context Engineering 目录结构
mkdir -p .augment/{context,prps/{templates,active},validation}
mkdir -p examples docs
```

### 第二步：创建项目规则文件 (2分钟)

创建 `.augment/context/project-rules.md`：

```markdown
# 项目开发规则

## 🔄 上下文管理
- 开始新任务前使用 codebase-retrieval 了解相关模式
- 遵循现有架构和编码约定

## 🧱 代码质量
- 文件大小不超过 300 行
- 测试覆盖率 ≥ 80%
- 所有静态检查通过

## 📚 文档要求
- 公共 API 需要文档
- 复杂逻辑需要注释
```

### 第三步：创建您的第一个 PRP (1分钟)

创建 `.augment/prps/active/my-first-feature.md`：

```markdown
# PRP: 我的第一个功能

## 🎯 目标
[描述要实现的功能]

## ✅ 成功标准
- [ ] 功能正常工作
- [ ] 测试通过
- [ ] 文档完整

## 📚 上下文收集
使用 codebase-retrieval 查询：
"查找与 [功能关键词] 相关的代码模式"

## 🔧 实现计划
- [ ] 分析现有模式
- [ ] 实现核心逻辑
- [ ] 编写测试
- [ ] 更新文档
```

## 💡 立即体验 Context Engineering

### 使用 Augment AI 收集上下文

在 Augment AI 中运行以下查询来收集项目上下文：

```markdown
请分析当前代码库的整体架构和主要模式，包括：
1. 项目使用的主要技术栈和框架
2. 代码组织结构和模块划分方式
3. 测试模式和约定
4. 错误处理和日志记录方式
5. 配置管理和环境设置方法

请提供具体的代码示例和模式说明。
```

### 创建任务列表

使用 Augment AI 的任务管理功能：

```markdown
请为实现 [您的功能描述] 创建详细的任务列表，包括：
1. 准备和分析阶段的任务
2. 实现阶段的具体步骤
3. 测试和验证任务
4. 文档和部署任务

每个任务应该是15-30分钟可完成的工作单元。
```

### 执行验证循环

在每个关键节点运行验证：

```markdown
请帮我验证当前实现是否符合以下标准：
1. 代码质量检查（格式、类型、静态分析）
2. 测试覆盖率和测试通过情况
3. 与项目现有模式的一致性
4. 性能和安全要求的满足情况

如果发现问题，请提供具体的修复建议。
```

## 🎯 实际应用示例

### 示例1：添加新的 API 端点

```markdown
# 上下文收集
"查找项目中现有的 API 路由定义模式，包括路由组织、中间件使用、错误处理和输入验证方法"

# 任务创建
基于收集的模式，创建实现新 API 端点的任务列表

# 实现验证
在每个步骤后验证代码质量、测试覆盖率和 API 规范一致性
```

### 示例2：重构现有模块

```markdown
# 上下文收集
"分析 [模块名] 的当前实现，包括其职责、依赖关系、测试覆盖情况和潜在的改进点"

# 重构计划
创建渐进式重构计划，确保每步都可验证

# 安全重构
通过全面测试确保重构不破坏现有功能
```

### 示例3：修复 Bug

```markdown
# 问题分析
"分析与 [Bug 描述] 相关的代码区域，包括可能的根本原因、影响范围和相关的测试用例"

# 修复策略
基于分析结果制定修复策略和验证方法

# 回归预防
添加测试用例防止类似问题再次发生
```

## 🔧 常用 Augment AI 工作流程

### 工作流程1：新功能开发

```markdown
1. 使用 codebase-retrieval 收集相关模式
   "查找与 [功能] 相关的所有实现模式"

2. 使用 add_tasks 创建任务列表
   基于 PRP 模板创建详细任务

3. 逐步实现并验证
   每完成一个任务就运行相关验证

4. 使用 update_tasks 跟踪进度
   及时更新任务状态和发现的问题
```

### 工作流程2：代码审查和改进

```markdown
1. 使用 diagnostics 检查代码质量
   发现潜在问题和改进点

2. 使用 codebase-retrieval 查找最佳实践
   "查找项目中类似功能的最佳实现方式"

3. 制定改进计划
   创建具体的改进任务和验证标准

4. 实施改进并验证效果
   确保改进不引入新问题
```

### 工作流程3：问题诊断和解决

```markdown
1. 使用 codebase-retrieval 分析问题区域
   "分析与 [问题描述] 相关的代码和依赖"

2. 使用 launch-process 运行诊断工具
   收集错误日志、性能数据等信息

3. 制定解决方案
   基于分析结果创建修复计划

4. 验证解决方案
   确保问题得到根本解决
```

## 📈 效果评估

### 立即可见的改进

使用 Context Engineering 后，您应该立即看到：

- **更准确的 AI 响应**：AI 更好地理解项目上下文
- **更少的返工**：减少因理解偏差导致的重做
- **更一致的代码**：自动遵循项目模式和约定

### 中期改进 (1-2周)

- **开发速度提升**：熟悉工作流程后效率显著提高
- **代码质量改善**：内置验证确保持续的高质量
- **知识积累**：项目模式和最佳实践得到系统化

### 长期收益 (1个月+)

- **团队协作改善**：统一的工作方式和文档
- **维护成本降低**：更好的代码组织和文档
- **新人上手加速**：完善的上下文和模式库

## 🎓 进阶学习路径

### 第1周：基础掌握
- [ ] 熟练使用 codebase-retrieval 收集上下文
- [ ] 掌握基本的 PRP 创建和执行
- [ ] 建立项目特定的规则和模式

### 第2-3周：工作流程优化
- [ ] 优化验证检查点和质量标准
- [ ] 建立项目模式库和最佳实践文档
- [ ] 实践复杂功能的分阶段实现

### 第4周及以后：高级应用
- [ ] 实施团队级别的 Context Engineering
- [ ] 建立自动化的验证和质量保证流程
- [ ] 持续优化和演进工作方式

## 🆘 常见问题快速解决

### Q: AI 理解不准确怎么办？
**A**: 增加更多具体的上下文信息，使用 codebase-retrieval 提供更多相关示例。

### Q: 验证检查太复杂？
**A**: 从简单的检查开始，逐步增加复杂度。重点关注最重要的质量指标。

### Q: 任务粒度不合适？
**A**: 调整任务大小，确保每个任务15-30分钟可完成，有明确的输入输出。

### Q: 团队成员不适应？
**A**: 从小功能开始实践，展示效果后逐步推广。提供培训和支持。

## 🎉 下一步行动

1. **立即行动**：在您的下一个功能开发中应用这个方法
2. **记录经验**：记录使用过程中的发现和改进点
3. **分享成果**：与团队分享使用效果和最佳实践
4. **持续改进**：根据项目特点不断优化工作流程

---

**记住**：Context Engineering 的核心是为 AI 提供全面、准确的上下文信息。开始时可能需要一些额外的时间投入，但很快您就会看到显著的效率提升和质量改善。

**立即开始**：选择一个小功能，创建您的第一个 PRP，体验 Context Engineering 的威力！

---

*需要更多帮助？查看完整的 [Augment AI Context Engineering 指南](./Augment_AI_Context_Engineering_Guide.md) 或参考 [模板和示例](./templates/) 目录。*
