# Git命令快速参考手册

> 常用Git命令的快速查询手册，适合打印或收藏

## 🚀 日常开发必备命令

### 基础操作
```bash
# 查看状态
git status

# 添加文件
git add .                    # 添加所有文件
git add filename             # 添加指定文件
git add *.js                 # 添加所有js文件

# 提交
git commit -m "提交信息"      # 提交暂存的文件
git commit -am "提交信息"     # 添加并提交已跟踪文件

# 查看历史
git log                      # 详细历史
git log --oneline            # 简洁历史
git log --graph              # 图形化历史
```

### 分支操作
```bash
# 查看分支
git branch                   # 本地分支
git branch -a                # 所有分支
git branch -r                # 远程分支

# 创建分支
git branch branch-name       # 创建分支
git checkout -b branch-name  # 创建并切换
git switch -c branch-name    # 新语法：创建并切换

# 切换分支
git checkout branch-name     # 切换分支
git switch branch-name       # 新语法：切换分支

# 合并分支
git merge branch-name        # 合并指定分支到当前分支

# 删除分支
git branch -d branch-name    # 删除已合并分支
git branch -D branch-name    # 强制删除分支
```

### 远程操作
```bash
# 查看远程仓库
git remote -v

# 添加远程仓库
git remote add origin url

# 推送
git push                     # 推送当前分支
git push origin main         # 推送到指定分支
git push -u origin main      # 首次推送并设置上游

# 拉取
git pull                     # 拉取并合并
git fetch                    # 只拉取不合并

# 克隆
git clone url                # 克隆仓库
git clone url folder-name    # 克隆到指定文件夹
```

## 🔧 问题解决命令

### 撤销操作
```bash
# 撤销工作区修改
git checkout -- filename    # 撤销单个文件
git checkout -- .           # 撤销所有文件

# 撤销暂存区
git reset HEAD filename     # 取消暂存单个文件
git reset HEAD              # 取消暂存所有文件

# 撤销提交
git reset --soft HEAD~1     # 撤销提交，保留修改在暂存区
git reset --mixed HEAD~1    # 撤销提交，保留修改在工作区
git reset --hard HEAD~1     # 撤销提交，删除所有修改

# 修改最后一次提交
git commit --amend -m "新的提交信息"
```

### 暂存操作
```bash
# 暂存当前修改
git stash                    # 暂存修改
git stash save "描述"        # 暂存并添加描述

# 查看暂存
git stash list               # 查看暂存列表
git stash show               # 查看最新暂存的修改

# 恢复暂存
git stash pop                # 恢复并删除最新暂存
git stash apply              # 恢复但不删除暂存
git stash apply stash@{1}    # 恢复指定暂存

# 删除暂存
git stash drop               # 删除最新暂存
git stash clear              # 删除所有暂存
```

### 查看差异
```bash
# 查看修改
git diff                     # 工作区vs暂存区
git diff --cached            # 暂存区vs最后提交
git diff HEAD                # 工作区vs最后提交
git diff branch1 branch2     # 比较两个分支

# 查看文件历史
git log filename             # 文件的提交历史
git blame filename           # 查看文件每行的修改者
git show commit-hash         # 查看指定提交的详情
```

## 📋 配置命令

### 用户配置
```bash
# 设置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 查看配置
git config --list            # 查看所有配置
git config user.name         # 查看用户名
git config user.email        # 查看邮箱

# 设置编辑器
git config --global core.editor "code"  # VS Code
git config --global core.editor "vim"   # Vim
```

### 别名配置
```bash
# 设置常用别名
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'

# 使用别名
git st                       # 等同于 git status
git co main                  # 等同于 git checkout main
```

## 🏷️ 标签操作

```bash
# 查看标签
git tag                      # 列出所有标签
git tag -l "v1.*"           # 列出匹配的标签

# 创建标签
git tag v1.0                # 轻量标签
git tag -a v1.0 -m "版本1.0" # 附注标签

# 推送标签
git push origin v1.0        # 推送指定标签
git push origin --tags      # 推送所有标签

# 删除标签
git tag -d v1.0             # 删除本地标签
git push origin --delete v1.0  # 删除远程标签
```

## 🔍 搜索和查找

```bash
# 搜索内容
git grep "搜索内容"          # 在工作目录中搜索
git grep "搜索内容" HEAD     # 在指定提交中搜索

# 查找提交
git log --grep="关键词"      # 搜索提交信息
git log --author="作者名"    # 按作者搜索
git log --since="2023-01-01" # 按时间搜索
git log --until="2023-12-31"

# 查找文件
git ls-files                # 列出所有跟踪的文件
git ls-files --others       # 列出未跟踪的文件
```

## 🧹 清理操作

```bash
# 清理未跟踪文件
git clean -n                # 预览要删除的文件
git clean -f                # 删除未跟踪的文件
git clean -fd               # 删除未跟踪的文件和目录

# 清理远程分支引用
git remote prune origin     # 清理已删除的远程分支引用

# 垃圾回收
git gc                      # 清理不必要的文件并优化仓库
git gc --aggressive         # 更彻底的清理
```

## 📊 统计信息

```bash
# 提交统计
git shortlog -sn            # 按作者统计提交数
git log --stat              # 显示每次提交的文件统计
git log --numstat           # 显示每次提交的行数统计

# 仓库信息
git count-objects -v        # 显示仓库对象统计
git ls-remote origin        # 显示远程仓库信息
```

## 🚨 紧急情况处理

### 误删文件恢复
```bash
# 恢复已删除的文件
git checkout HEAD -- filename

# 恢复到指定版本
git checkout commit-hash -- filename
```

### 找回丢失的提交
```bash
# 查看引用日志
git reflog

# 恢复丢失的提交
git checkout commit-hash
git checkout -b recovery-branch
```

### 解决合并冲突
```bash
# 查看冲突文件
git status

# 编辑冲突文件后
git add conflicted-file
git commit

# 取消合并
git merge --abort
```

## 💡 实用技巧

### 一行命令技巧
```bash
# 创建并切换分支
git checkout -b feature/new-feature

# 添加并提交
git commit -am "快速提交已跟踪文件"

# 推送新分支
git push -u origin feature/new-feature

# 删除远程分支
git push origin --delete branch-name

# 重命名分支
git branch -m old-name new-name
```

### 查看简化信息
```bash
# 简洁的状态信息
git status -s

# 简洁的分支信息
git branch -v

# 最近的提交
git log -5 --oneline

# 图形化简洁历史
git log --oneline --graph --all
```

## 📝 .gitignore 常用模板

```bash
# 创建 .gitignore 文件
cat > .gitignore << EOF
# 依赖文件
node_modules/
vendor/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 环境配置
.env
.env.local

# IDE配置
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# 构建输出
dist/
build/
*.min.js
*.min.css
EOF
```

---

## 📱 移动端友好版本

### 最常用的10个命令
```bash
git status          # 查看状态
git add .           # 添加所有文件
git commit -m ""    # 提交
git push            # 推送
git pull            # 拉取
git checkout -b     # 创建分支
git merge           # 合并分支
git log --oneline   # 查看历史
git diff            # 查看差异
git stash           # 暂存修改
```

### 紧急救援命令
```bash
git stash           # 暂存当前修改
git reset --hard    # 重置到最后提交
git reflog          # 查看操作历史
git checkout --     # 撤销文件修改
```

---

*💡 提示：建议将此页面加入书签，或打印出来放在桌边，方便随时查阅！*
