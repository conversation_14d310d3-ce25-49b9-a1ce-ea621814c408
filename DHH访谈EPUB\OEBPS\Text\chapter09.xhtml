<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第九章：编程工具与开发环境</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第09章</div>
        <h1 class="chapter-title">第九章：编程工具与开发环境</h1>
    </div>
    
    <div class="chapter-intro">
        <p>从Mac到Linux的转变，从TextMate到Neovim的选择，DHH对编程工具的偏好反映了他对简洁和效率的追求。本章探讨了工具选择如何影响编程体验和生产力。</p>
    </div>

    <div class="conversation">
        <p><strong>Lex：</strong> 我必须问你关于你编程设置的细节，IDE，所有那种东西。让我们描绘完美编程设置的图片。</p>

        <p>你有你享受的编程设置吗？你非常灵活吗？就像多少显示器？什么类型键盘？什么类型椅子？什么类型桌子？</p>

        <p><strong>DHH：</strong> 有趣，因为如果你问我，让我看，一年半前，我会给你与我基本上20年给任何人的同样答案。</p>

        <p>我想要Mac。我喜欢Magic键盘。我喜欢单显示器。苹果制造令人惊叹6K 32英寸XDR屏幕，我仍然没有找到任何人击败，我仍然使用，即使我从苹果计算机切换。我仍然使用他们的显示器，因为它只是奇妙。但我一直是单屏幕类型家伙。</p>

        <p>我确实喜欢大屏幕，但我不想要多个屏幕。我从来没有发现那真的与我的感知工作。我想能够只是专注于单个事情。我不想要它到处都是。我一直使用多个虚拟桌面，能够在那些事情之间来回切换。但我今天有的设置是Linux。</p>

        <p>我在一年多前切换，在我最终对苹果足够厌倦，我不能再那样做之后。然后我使用这个叫Lofree Flow84的低轮廓机械键盘，这只是我听过最光荣声音键盘。</p>

        <p>我知道有很多机械键盘鉴赏家可能会在这上面与我争论。这太thy或太clicky或太clocky或任何东西。但对我来说，Lofree Flow84只是我甚至不知道存在的喜悦。这如此有趣，因为我的意思是，我编程很长时间。机械键盘很长时间是一件事。</p>

        <p>键盘，当你看它像这样，它只是有点，它看起来平淡，它不看起来奢华。但你从推那些键得到的触觉感觉，当键击中板时你听到的talky声音。它只是崇高。我踢自己，我在这个Mac泡泡中如此长时间，我甚至不在市场找到这个。</p>

        <p>我知道机械键盘存在，但坦率地说，我认为它有点书呆子事情，只有比我书呆子得多的真正书呆子会关心。然后我走出苹果泡泡，突然我必须再次找到一切。我必须找到新鼠标，我必须找到新键盘，我必须找到一切。我想，好吧，让我试试机械键盘。</p>

        <p>我试了相当多。Keychron是其中一个大品牌，我根本不喜欢那个。我试了一堆其他键盘，然后我最终找到这个键盘，我只是说，天使在唱歌。你我整个生活在哪里？我们作为程序员花如此多时间与那些键互动。</p>

        <p>它真的有点重要，以我没有完全欣赏的方式。我过去为苹果Magic键盘辩护，就像嘿，它很棒。它实际上是伟大键盘。我认为对它是什么，这个超低轮廓，超低行程实际上是真的好键盘。但一旦你试过更长行程机械键盘，没有回头。</p>

        <p><strong>Lex：</strong> 你确实必须记住在许多方式中，在软件方面和硬件方面，你确实在计算机后面花很多小时。值得...</p>

        <p><strong>DHH：</strong> 值得投资。</p>

        <p><strong>Lex：</strong> 也值得探索，直到你找到天使开始唱歌的东西，无论什么。</p>

        <p><strong>DHH：</strong> 那正确。我实际上确实有点后悔那个，特别是与这个该死键盘。</p>

        <p>我的意思是，我可能多年多年听这些美丽thocky键，但有时你必须真的离开，在你睁开眼睛看到其他东西存在之前。我对Linux感觉同样方式。所以我从90年代后期开始在服务器上使用Linux，可能。我们当时在Linux上运行服务器。我从来没有认真考虑它作为桌面选择。我从来没有直接自己运行Linux。</p>

        <p>我总是想，你知道什么？我想专注于编程。我没有时间所有这些配置文件和所有这些设置胡说八道等等。苹果足够接近。它建立在Unix基础上。我为什么需要打扰Linux？再次，它是那些事情之一。</p>

        <p>我需要尝试新事情并尝试其他东西，意识到有其他事情除了苹果。再次，不是因为我讨厌苹果。我认为他们仍然制造好计算机。我认为很多软件仍然也相当好。但我已经意识到作为网络开发者，Linux只是更好。Linux只是更好。它更接近我部署的。工具实际上现象。</p>

        <p>如果你花一点时间设置它，你可以记录可重现环境，我现在用这个Omakub概念或项目做，我可以在不到30分钟设置新Linux机器。它完美。它不是相当好。它不像我仍然需要花两小时。</p>

        <p>它完美，因为你可以将开发环境的所有方面编码到这个中。我不知道。公平地说，我甚至不知道Linux可以看起来像它可以那样好。如果你看股票Ubuntu或Fedora启动，我的意思是，不是它丑，但我会一周任何一天选择Mac。你看Omakub，我的意思是，我在这里有偏见，当然，因为我用我自己的感性建立它。但我看那个并说，这更好。</p>

        <p>这美丽。然后你看其中一些真正Linux上升设置，人们与一切疯狂，你说，哦是的，我记得当计算机过去以这种方式有趣时，当有这种个性和这种设置。它不只是所有计划同样性。</p>

        <p>我认为那是像苹果这样东西的翻面有时，他们有真的强烈意见，他们有真的好意见，他们有非常好品味。它看起来非常好，它也看起来完全同样。Linux有远更多多样性和远更多纹理和味道。有时也烦恼和错误和任何东西。但我现在运行Linux。它是基于Ubuntu的，顶部有Omakub东西。</p>

        <p>低自由键盘。我使用罗技，它叫什么？MX 3鼠标，我爱它在我手中感觉如何。我不爱它看起来如何。我实际上最长时间是Magic鼠标支持者。我认为苹果将触控板集成到鼠标中是天才，我使用那个。</p>

        <p>我总是认为人们只是因为你必须通过翻转它充电而抨击它是荒谬的。因为电池会持续三个月，然后你会充电半小时。我想，那与我的感性完美兼容。如果某些东西美丽，我不介意放弃一点不便，那个Magic鼠标美丽。但它不会在Linux上工作。</p>

        <p>所以我找到其他东西。S3好，但我有时确实希望像Magic鼠标。那相当好。</p>

        <p><strong>Lex：</strong> 是的，Linux对定制一切，对平铺，对宏，对所有那个真的伟大。我也在Windows中用AutoHotKey做同样或只是定制整个事情到你的偏好。</p>

        <p><strong>DHH：</strong> 如果你是开发者，你应该学习如何用键盘控制你的环境。它更快，它更流畅。我认为我已经真正欣赏关于我Omakub设置的那些愚蠢事情之一是我可以，在刷新屏幕需要的任何时间，可能5毫秒从一个虚拟桌面切换到另一个。甚至在Windows上，你不能得到那么平滑。你可以接近，你不能得到那么平滑。</p>

        <p>在macOS上，无论什么原因，苹果坚持在你在虚拟桌面之间切换时有这个令人愤怒动画，这使得只是你不想要。你不想运行全屏应用，因为在虚拟桌面之间切换太麻烦。</p>

        <p>你可以从美妙Linux设置在那方面得到的那种即时性，它只是下一水平。</p>

        <p><strong>Lex：</strong> 是的，它似乎像微妙事情，但你知道，毫秒差异和在切换虚拟桌面之间的延迟，例如。我不知道，它改变...</p>

        <p><strong>DHH：</strong> 它改变你如何使用计算机。它真的做。</p>

        <p><strong>Lex：</strong> 与VR类似事情，对吧？如果有某种延迟或像它只是完全带你出来。</p>

        <p><strong>DHH：</strong> 有趣，我实际上必须观看，我认为它是YouTube上的ThePrimeagen，当他展示他的设置时，我看到他在那些虚拟桌面之间切换多快。</p>

        <p>我一直使用虚拟桌面，但我不喜欢切换太多，因为只是那种延迟，它像，哦，你可以在Linux上做那个？哦，那相当酷。所以我运行那个，然后我现在的编辑器选择是Neovim。</p>

        <p><strong>Lex：</strong> 哦好，好吧。好吧，我们没时间了。不，好吧，你做了很多，很多年。你使用，它是什么？TextMate。</p>

        <p><strong>DHH：</strong> 是的，TextMate。</p>

        <p><strong>Lex：</strong> TextMate，是的。</p>

        <p><strong>DHH：</strong> 那实际上，那是离开苹果的主要阻碍。其他一切我想，你知道什么？我可以摆动它。但TextMate是并且是美妙编辑器。一个，我帮助诞生到这个世界。</p>

        <p>程序员Allan Odgaard是我的好朋友，一直回到聚会日子，当我们拖我们的计算机。他是大Mac家伙。在2005年，他写这个编辑器，我帮助他项目管理有点保持他在轨道上，保持他专注于得到某些东西发布。因为我真的为我自己想要它。</p>

        <p>我想，这是我认为我从来不会切换的最后编辑器。</p>

        <p><strong>Lex：</strong> 原谅我不知道，但这个编辑器多功能？</p>

        <p><strong>DHH：</strong> 它相当功能，但它在某些方面是GUI驱动编辑器。它真的早期有记录宏和有有点复杂语法高亮的方式。</p>

        <p>它做了一堆第一，它只是真的愉快编辑体验。我认为这些天很多人只是使用VS Code。VS Code在某些方式中存在与TextMate同样宇宙中。实际上我认为与原始TextMate包兼容，原始TextMate格式。</p>

        <p>所以它真的在那里追踪路径，但它也只是没有进化。现在很多人看到那个巨大问题。他们像，"哦，它需要有更多功能。它需要有所有这些事情。"我像，我对这个文本编辑器快乐。那根本没有改变。</p>

        <p>基本上当Allan停止在它上工作十年或更多时。我不需要其他任何东西。因为正如我们原始讨论去，我不想要IDE。我不想要编辑器为我写代码。我想要文本编辑器。我想直接与字符互动。Neovim允许我以甚至比TextMate更好的某些方式做那个。我爱TextMate，但Vi正如你知道。</p>

        <p>一旦你学习命令，它听起来，我有时感觉Vi粉丝过度播放学习它多困难，因为它也许让他们似乎有点更棒，他们能够做它。它不那么困难。在我看来，学习足够组合移动得到那种神圣狗屎的高，我不能在任何其他编辑器中做这个，不花那么长时间。</p>

        <p><strong>Lex：</strong> 你花了多长时间？顺便说一下，我不知道，我还没有。</p>

        <p>哦，我知道，智力上。但就像与孩子，我没有一直进入。我没有使用Vim。</p>

        <p><strong>DHH：</strong> 你有心中待遇。好吧，我在大约一年前切换时大约三天切换。我有三天诅咒，我认为它绝对糟糕，它从来不会发生。我有三天烦恼，已经下周我像，这甜美。</p>

        <p>我不去任何地方。但我也在大约20年前2000年代早期有一点领先开始。我试了Vim像一个夏天，它没有坚持。我无论什么原因当时没有爱它。但Neovim真的好。Neovim的关键是意识到你不必自己建立整个编辑器。</p>

        <p>有很多Neovim支持者像，这是如何从头写冲突。超过17集，那会花你三周。啊，我不那么关心。我爱伟大编辑器。我爱稍微定制它，但不那么多。所以你必须将Neovim与这个叫LazyVim的东西配对。Lazyvim。</p>

        <p>org是Neovim的分发，拿走所有苦差事，立即得到令人惊叹编辑器体验。</p>

        <p><strong>Lex：</strong> 荒谬问题，我们谈论了一堆编程语言。你告诉我们你多爱JavaScript。它是你第二喜欢编程语言。TypeScript会是第三吗？</p>

        <p><strong>DHH：</strong> TypeScript甚至不会在这个宇宙中。我讨厌TypeScript就像我喜欢JavaScript一样多。</p>

        <p><strong>Lex：</strong> 所以你讨厌什么，哦男人，我不够聪明理解那个数学。好吧，在我问关于其他编程语言之前，如果你可以将你对TypeScript的仇恨封装成可以人类解释的东西，推理会是什么？</p>

        <p><strong>DHH：</strong> JavaScript在其元编程某些方面闻起来很像Ruby。</p>

        <p>TypeScript只是将那个复杂化到令人愤怒程度，当你试图写那种代码时。甚至当你试图写正常类型代码时，没有积累给喜欢它的人的好处。就像自动完成是我关心的东西。我不关心自动完成，因为我不使用IDE。</p>

        <p>现在我理解那是分离它和为什么的一部分。我看不到好处。我只看到成本。我看到额外输入。我看到你有时必须做的类型体操，一堆人放弃并只是做任何，对吧？就像他们实际上不使用类型系统，因为使用它只是太令人沮丧。</p>

        <p>所以我只是感觉TypeScript的挫折和TypeScript在代码中的混淆，给我没有回报。再次，我理解有回报。我不想要回报。所以对我的情况，我不愿意做交易，我不愿意拿在下面像Ruby一样动态的语言，然后将它变成这个假装静态类型语言。我发现那只是智力侮辱。</p>

        <p><strong>Lex：</strong> 你认为它会并且你认为它应该死TypeScript吗？</p>

        <p><strong>DHH：</strong> 我不想从享受它的人那里拿走某些东西。所以如果你喜欢TypeScript，你的大部分。如果你使用TypeScript，因为你认为那是专业程序应该做的，这是我的许可，你不必使用TypeScript。</p>

        <p><strong>Lex：</strong> 有一些深深享受关于像你这样聪明程序员，DHH正在说狗屎。它像我生活中最喜欢的事情之一。如果你与初学者交谈，每个人应该学习的前三编程语言是什么？</p>

        <p><strong>DHH：</strong> 我会100%从Ruby开始。它对初学者来说是魔法，在只是理解条件和循环和任何东西的核心概念方面，因为它让它如此容易。</p>

        <p>即使你只是制作输出到终端的shell程序，在Ruby中运行hello-world基本上是puts，P-U-T-S，空格，开始引号，hello world，结束引号，你完成了，对吧？没有绒毛。没有什么包装它。有其他语言做那个，特别是在Perl或Python会相当类似。但Go不会，Java不会。</p>

        <p>有很多其他语言有很多更多仪式和样板。Ruby没有它。所以它是美妙开始语言。有一本叫"学习编程"的书，由Pine写，使用Ruby本质上只是教基本编程原则，我看到大量推荐。所以那是伟大语言。</p>

        <p><strong>Lex：</strong> [Lex] 你多快会去Rails？</p>

        <p><strong>DHH：</strong> 取决于你想做什么。</p>

        <p>如果你想建立网络应用，立即去Rails。与Rails一起学习Ruby，因为我认为真正帮助推动学习编程的是建立你想要的程序，对吧？如果你只是抽象地学习它，很难激励自己实际做它。好吧，一些人只是为了它们的乐趣学习语言。大多数人不。</p>

        <p>大多数人学习它，因为他们有使命。他们想建立程序。他们想成为程序员。所以你必须为某些真实东西使用它。我实际上发现以那种方式学习编程也更容易，因为它驱动你的学习过程。你不能只是提前学习整个事情。你不能只是坐下来读语言规范，然后说，哦，像尼奥。</p>

        <p>现在我知道功夫。现在我知道Ruby。它不那样下载。你实际上必须在真实程序上愤怒地输入它。</p>

        <p><strong>Lex：</strong> 是的，是的，肯定。</p>

        <p><strong>DHH：</strong> 所以我会从那里开始。但然后第二，我可能会是JavaScript。因为如果你想与网络工作，JavaScript只是你需要知道的语言。</p>

        <p>网络是有史以来最伟大应用平台，如果你制作业务软件，协作软件，所有这种东西。如果你制作视频游戏，你可能应该去学习C++或C或其他类似东西。但如果你在网络应用领域，你必须学习JavaScript。</p>

        <p>无论你学习什么其他，你必须学习JavaScript。</p>

        <p><strong>Lex：</strong> 所以如果你学习Ruby，Ruby在编程概念方面没有什么，你需要其他语言？</p>

        <p><strong>DHH：</strong> 我不知道是否有任何概念缺失，但它没有速度或你需要建立3D游戏引擎的内存操作低级访问，例如。没有人会在Ruby中建立那个。</p>

        <p>当涉及网络技术时，你可以在Ruby中建立相当低级东西。但在某个点，你会击中限制，你应该使用其他东西。我不是为一切只是规定Ruby的某人。只是一旦你达到与网络应用涉及的抽象水平，Ruby是极好的。</p>

        <p>但如果你写，例如，HTTP代理，go，它对那个很棒。我们最近在公司为各种原因写了相当多HTTP代理，包括我们的云退出等等。Kevin，我与之工作的程序之一，他在Go中写所有那个。Go只是有原语，它有步伐和速度真的很好地做那个。我高度推荐它。</p>

        <p>如果你写HTTP一般代理，在Go中做它。对那个伟大语言。不要在Go中骑你的业务逻辑。我知道人们做，但我看不到那个点。</p>

        <p><strong>Lex：</strong> 所以你会说三个，所以Go，Ruby加Rails，JavaScript？</p>

        <p><strong>DHH：</strong> 是的，如果你有兴趣与网络工作，我会选择那三个。Go，Ruby和JavaScript。</p>

        <p><strong>Lex：</strong> Go，Ruby和JavaScript，好吧。函数式语言？</p>

        <p><strong>DHH：</strong> 某人正在谈论Ocaml。</p>

        <p><strong>Lex：</strong> 他们总是要出现。必须是某种OCaml工业复合体或类似这样的东西。但他们总是说提到OCaml。</p>

        <p><strong>DHH：</strong> 我爱有人爱函数式语言到那种程度。</p>

        <p>那些人不是我。我根本不关心。就像我关心函数式原则，当它们在这些孤立情况中帮助我时，那只是比其他一切更好。但在心中，我是面向对象家伙。那只是我如何想程序。那是我如何喜欢想程序。</p>

        <p>那是我如何将大问题空间雕刻成域语言。对象是我的果酱。</p>

        <p><strong>Lex：</strong> 是的，我也是。所以我为像AI应用基本编程一堆Lisp。所以奥赛罗，国际象棋引擎，那种东西。我确实试了OCaml只是强迫自己编程只是非常基本生命游戏。小模拟。它很多，你知道，Lisp只是到处括号。它实际上根本不可读。</p>

        <p><strong>DHH：</strong> 那是我与Lisp有的问题。</p>

        <p><strong>Lex：</strong> OCaml非常直觉，非常可读。它好。</p>

        <p><strong>DHH：</strong> 我真的应该在某个点拾起像那样的语言。我编程足够长时间，我实际上没有在完全函数式编程语言中愤怒地做任何真实东西有点尴尬。</p>

        <p><strong>Lex：</strong> 是的，但就像我必须弄清楚，我确信有那个答案。</p>

        <p>我可以做什么对我有用，就像我实际上想建立？</p>

        <p><strong>DHH：</strong> 是的，是的，那是我的问题</p>

        <p><strong>Lex：</strong> 函数式语言更适合。</p>

        <p><strong>DHH：</strong> 那正确。</p>

        <p><strong>Lex：</strong> 因为我真的想适当体验语言。</p>

        <p><strong>DHH：</strong> [DHH] 那正确。</p>

        <p><strong>Lex：</strong> 是的，因为我仍然，是的。在这一点，我非常面向对象大脑。</p>

        <p><strong>DHH：</strong> 是的，那也是我的问题。我不那么关心计算机科学中这些低级问题。我关心高级。我关心写软件。我关心与网络应用和业务逻辑真的很好漂浮的抽象层。我已经接受关于我自己那个。</p>

        <p>即使，正如我们谈论当我是孩子时，我真的想成为游戏程序员。然后我看到写碰撞检测引擎需要什么。我说，是的，那根本不是我。我从来不会进入向量矩阵操作或任何那种东西。</p>

        <p>它太多数学，我更多是写作人，而不是数学人。</p>

        <p><strong>Lex：</strong> 我的意思是，只是在你今天说话的方式中，你有像诗意文学编程方法。</p>

        <p><strong>DHH：</strong> 是的。</p>

        <p><strong>Lex：</strong> 是的。</p>

        <p><strong>DHH：</strong> 有趣，那实际上正确。所以我实际上在10年前RailsConf做了主题演讲，我称自己软件作家。我的意思是，我不是说那个的第一人。软件作家在白话中很长时间。</p>

        <p>但大多数程序员在试图严肃时采用的现代身份是软件工程师。我拒绝那个标签。我不是工程师。偶尔我涉足一些工程，但绝大多数时间，我是软件作家。我为人类消费和我自己喜悦写软件。</p>

        <p>我可以逃脱那个，因为我在像Ruby这样高级语言中工作，在协作软件和待办事项列表和所有其他东西上工作。再次，如果我试图将我的才能应用于写3D游戏引擎，不，那不是正确心态。那不是正确身份。但我发现软件工程身份稍微平坦化事情，我喜欢想我们有软件作家和软件数学家，例如。</p>

        <p>然后那些实际上是描述你工作的抽象水平比工程师更丰富方式。</p>

        <p><strong>Lex：</strong> 是的，我认为如果AI变得越来越成功，我认为我们会越来越需要软件作家技能。因为感觉那是领域，因为它不是作家。你会必须做软件。你会必须是计算机人。</p>

        <p>但有更多...我不知道，我只是不想浪漫化它，但它更诗意，它更文学。它更感觉像写好博客文章，而不是...</p>

        <p><strong>DHH：</strong> 我实际上希望AI对写作有更高标准。我发现它接受我草率，不完整句子的事实有点冒犯。我希望有像AI严格模式，它会打我的手指。</p>

        <p>它只是喂它关键词，像说适当，做发音，做标点，因为我爱那个。我爱制作恰好正确句子，没有被煮沸，它没有肉，它没有字符。它简洁。它不过度花哨。</p>

        <p>它只是正确，那个写作阶段，对我来说，只是令人上瘾。我发现当编程最好时，它几乎完全等同于那个。你也必须解决问题。你不只是传达解决方案。你必须实际弄清楚你试图说什么，但甚至写作有那个。</p>

        <p>一半时间当我开始写博客文章时，我不确切知道我要使用什么论点。他们作为写作过程的一部分发展。那也是写软件如何发生。你大致知道你试图解决的问题类型。你不确切知道你如何解决它。当你开始输入时，解决方案出现。</p>

        <p><strong>Lex：</strong> 实际上，据我理解，你和杰森正在写新书。它在那种主题的早期日子。我认为他说，他推特它会被标题像，我们不知道我们提前做什么，类似那样。那种主题。你沿途弄清楚。</p>

        <p><strong>DHH：</strong> 那是它的大部分。</p>

        <p>试图给更多人许可信任你自己的本能和他们自己的直觉，意识到发展你胃中那个超级计算机实际上是职业生涯的工作。你不应该丢弃那些感觉，偏好过度复杂...或甚至不复杂，分析，智力主义。</p>

        <p>经常当我们看我们必须做的大决定时，他们来自直觉，你不能完全阐述，为什么我认为这是正确事情？好吧，因为我在这个业务中20年，我看到一堆事情，我与一堆人交谈，那正在渗透成这是正确答案。</p>

        <p>很多人对业务中那个非常怀疑，或无法信任它，因为感觉他们不能合理化。我们为什么做某些东西？好吧，因为我感觉像它，该死。那是自举独立创始人的伟大特权，他们不欠他们的业务给其他人，不必产生回报。</p>

        <p>因为我感觉很多真的爬进来，当你试图向其他人合理化你为什么做你做的事情，为什么你做你做的决定。如果你没有任何人回答，你自由跟随你的直觉。那是地狱享受工作方式。它也经常是正确工作方式。你的直觉知道很多，就像你不能阐述它，但它比不更多时候准确。</p>

        <p><strong>Lex：</strong> 是的，必须制定计划可以是瘫痪事情。</p>

        <p>我经常，我的意思是，我想有不同类型大脑。首先，如果它实现，我不能等读那本书。我经常感觉在我生活中做的更有趣事情中，我真的不知道我提前做什么。我认为有很多关心我的人真的想要我知道我在做什么。</p>

        <p>他们像，计划是什么？你为什么做这个疯狂事情？如果我必须等到我有计划，我不会做它。他们在这种东西上有不同大脑。一些人真的是计划者，它也许激励他们。我认为大多数创造性追求，大多数真的有趣，大多数新颖追求像，你有点必须只是跳跃，然后只是弄清楚当你去。</p>

        <p><strong>DHH：</strong> 我在"重新工作"中最喜欢文章是最后一个，它标题，灵感是易腐的。</p>

        <p>我认为那捕获很多它，如果你花时间做详细计划，到你完成时，你很可能失去灵感。如果你在那一刻跟随灵感并信任你的直觉，信任你自己的能力，你会弄清楚它，你会得到如此多更多回来。你会去你否则不会有的冒险。</p>

        <p>无论那只是业务决定或生活决定，你必须抓住那种灵感。有这个日本作者写的伟大儿童书籍集合，关于追逐想法并试图抓住它，它美丽地说明作为想法某些东西，作为你必须捕捉并抓住的某些东西漂浮。</p>

        <p>我真的感觉捕获这个概念，灵感是易腐的，它会消失。如果你只是把它放回架子上说，好吧，我必须对这个勤奋。我必须排列计划。你可能用完，然后没有蒸汽继续。</p>
    </div>
</body>
</html>