<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第二章：发现Ruby的魔力</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第02章</div>
        <h1 class="chapter-title">第二章：发现Ruby的魔力</h1>
    </div>
    
    <div class="chapter-intro">
        <p>从PHP到Ruby的转变，标志着DHH编程生涯的重要转折点。Ruby不仅仅是一门编程语言，更像是为他的大脑量身定制的完美工具。本章探讨DHH如何发现Ruby，以及这门语言如何改变了他对编程的理解和追求。</p>
    </div>

    <div class="conversation">
        <p><strong>Lex：</strong> 所以可以公平地说，没有PHP就不会有DHH，因此你把你所有的成功都归功于PHP吗？</p>

        <p><strong>DHH：</strong> 100%这是真的。甚至比这更好，因为PHP对我来说不仅仅是在制作我自己的Web应用程序方面给了我一个开始，它实际上给了我一个标准。在许多方面，我认为开发者、Web开发者人体工程学的顶峰是90年代末的PHP，你写这个脚本，你FTP它到服务器，立即部署。立即可用。</p>

        <p>你改变那个文件中的任何东西，你重新加载。砰，它就在那里。没有Web服务器，没有设置。只有一个运行mod PHP的Apache。它本质上是让动态网页启动和运行的最简单方法。这是我基本上在我职业生涯的其余部分一直在追求的高度之一。</p>

        <p>在90年代中后期为互联网制作东西是如此容易。我们如何失去了允许我们不仅以这种方式工作，而且让新人进入行业给他们成功体验的敏感性，就像我有的那样。在HTML页面上添加一个该死的闪烁标签，将PHP页面FTP到Apache Web服务器，而不真正了解任何关于任何东西的知识，不了解框架，不了解设置。</p>

        <p>所有这些东西真的把我们带到了一个地方，有时感觉我们的处境并没有好多少。网页与90年代末、2000年代初的网页并没有太大不同。它们仍然只是表单。它们仍然只是写入数据库。我认为很多人对这样一个事实感到非常不舒服：他们本质上是CRUD猴子。</p>

        <p>他们只是制作创建、读取、更新或删除数据库行的系统，他们必须通过过度复杂化来补偿这种存在主义的恐惧。现在这有点夸张。还有更多。你可以从更复杂的思考方式中学到东西。但这里仍然有一个理想，这就是为什么我很高兴你有Pieter Levels，因为他仍然基本上是这样工作的。</p>

        <p>我看着那个说，天哪，这太棒了。</p>

        <p><strong>Lex：</strong> 是的，你在追求那种高度。他一直都很高，使用PHP、jQuery和SQLite。</p>

        <p><strong>DHH：</strong> 我认为这很棒，因为他证明了这不仅仅是一个怀旧的梦想。他实际上在做这件事。他在经营所有这些业务。现在其中一些是，正如他会首先承认的，他只是一个人。</p>

        <p>当你只是一个人时，你可以做不同的事情。当你在团队中工作时，当我开始与杰森·弗里德在Basecamp上工作时，我们起初没有一起使用版本控制。我为自己使用版本控制。</p>

        <p>然后我想，你知道吗？设计师，啊，他们可能不够聪明来弄清楚CVS。因此我就像，不，不，不，你只是FTP它。你只是FTP它。我知道他们知道如何做FTP。然后在我第三次覆盖他们的更改后。我想，该死的。我必须教杰森CVS不要再这样做。但我认为我们仍然可以以我们在90年代的方式工作，以Pieter今天的工作方式，甚至在团队环境中，有更多的真理。</p>

        <p>我们太愿意将我们的开发者人体工程学交给复杂性商人。</p>

        <p><strong>Lex：</strong> 你一直在Rails 8中追求这一点。那么你如何带来现代框架的所有酷炫功能，并使其无需构建，使创建和发布东西像90年代的PHP一样容易？对我来说，很难击败Pieter Levels的方法。发布一些PHP是如此容易。</p>

        <p><strong>DHH：</strong> 应该是这样。为什么应该比那更难？我们今天的计算机几乎比90年代的计算机快无限倍。所以我们不应该能够以更简单的方式工作吗？我们应该回顾90年代说，哦，那太复杂了。</p>

        <p>现在我们有更复杂的技术，速度更快，它允许我们以这些更容易使用的方式工作。但这不是真的。但现在你可以看到我在Ruby on Rails工作中画的线，特别是Rails 8。对我来说，无构建是回到那种90年代的感觉，现在我们可以做一些这些事情而不放弃所有的进步。因为我确实认为你可能过于怀旧。</p>

        <p>我确实认为你可以开始幻想90年代的一切都更好。不是的。我的意思是，我在那里。有很多糟糕的事情。如果我们能以某种方式找到一种方法来结合我们在过去20年中取得的优势和进步与开发者人体工程学的便利性，我们就能赢。</p>

        <p>无构建是对我在过去10、15年中最讨厌的Web开发部分的拒绝，那就是JavaScript场景。我不是说我讨厌JavaScript的人。我的意思是，我经常开玩笑说JavaScript。这是我第二喜欢的编程语言。这是一个非常遥远的第二名。Ruby是迄今为止第一名，但我实际上喜欢JavaScript。我不认为这是一种糟糕的语言。</p>

        <p>它受到很多抨击。人们添加一个字符串的二加一，它给出一些无意义的东西。我只是想，是的，但你为什么要这样做？只是不要这样做。这种语言实际上相当可爱，特别是现代版本。ES6真正引入了适当的类语法。所以我可以以许多与我喜欢使用Ruby工作的相同方式使用JavaScript，使事情变得更好。</p>

        <p>但在2010年代初直到最近，所有这些进步都发生在预处理中，发生在构建管道中。浏览器无法说出令人愉快的JavaScript方言。所以每个人都开始预编译他们的JavaScript，以便能够使用更现代的编程方式与被视为陷入古老JavaScript版本的浏览器，没有人真正想要使用。</p>

        <p>这对我来说是有意义的，但它也是深深不愉快的。我记得在那个时候，我称之为黑暗时代，与JavaScript，这不可能是最终目的地。我们不可能将互联网变成如此不愉快的工作场所。我会开始在JavaScript中使用webpack和所有这些依赖项处理项目。</p>

        <p>我会把它放下五分钟，这个东西就不会再编译了。JavaScript社区，特别是其框架和工具，在2010年到2020年的十年中经历的流失量是荒谬的。你必须被困在那个疯人院里才能不意识到我们让自己陷入了多么完全变态的情况。</p>

        <p>为什么一切都一直在破坏？我的意思是，笑话不仅仅是软件会破坏，这会让我个人恼火。但然后我会去Hacker News，我会看到一些关于某个框架的最新JavaScript发布的线程。线程会是这样的，有人会问，"好吧，我们不是在使用三个月前刚刚使用的东西吗？"人们会说，"那个东西太过时了。那是三个月前的事。</p>

        <p>你必须跟上新程序。我们正在完全重写第无数次的一切。"你在过去一段时间在框架上学到的任何东西，都是无用的。你必须扔掉一切，你必须重新开始。</p>

        <p>你为什么不这样做，愚蠢的白痴？</p>

        <p><strong>Lex：</strong> 这是一种接管开发者社区的大规模歇斯底里吗？你认为你必须不断创建新框架和新框架。我们过了那个黑暗时代吗？</p>

        <p><strong>DHH：</strong> 我认为我们正在摆脱它，我们正在摆脱它，因为浏览器变得更好了。浏览器技术有停滞。其中一些是一直追溯到IE5的遗留问题。所以IE5基本上将整个互联网开发体验置于深度冻结中，因为微软在2000年代中期赢得了浏览器战争，然后他们基本上解散了他们的浏览器开发团队。因为他们想，"好吧，工作完成了。我们不需要在互联网上进行更多创新。</p>

        <p>我们现在可以回到编写Windows表单或其他什么，现在我们控制一切？"直到显然Firefox点燃了一点东西。然后Chrome进入了场景，谷歌认真地推动网络前进。你有一个点燃，也许浏览器可以更好。也许浏览器没有在2005年及时冻结。</p>

        <p>也许浏览器实际上可以像它所是的开发平台一样进化。但然后发生的是你有很多聪明的人涌入网络，因为网络结果是有史以来最伟大的应用程序开发平台。这是所有钱被赚的地方。这是所有亿万富翁被铸造的地方。</p>

        <p>这是Facebook和世界上的其他人出现的地方。所以你有所有这些脑力应用于如何与网络合作的问题。有一些非常聪明的人有一些，我确信非常好的想法，他们没有程序员快乐作为他们的第一动机。他们有其他优先事项，这些优先事项允许他们贴现甚至合理化他们在各处注入的复杂性。其中一些复杂性来自组织结构。</p>

        <p>当你有一个像Facebook这样的公司，例如，它确实依赖于网络并想要推动它前进，但已经将开发角色工作切成这些微小的小生态位。我是前端glob管道配置器。哦，是的，我是前端工程师。突然网络开发者不再是一个人。</p>

        <p>它是15个不同的角色。这本身就注入了大量的复杂性。但我也想在这里给出大胆的案例，这是一些复杂性是必要的，以达到我们今天的位置。复杂性是一座桥梁。它不是目的地，但我们必须穿过那座桥梁才能到达我们今天的位置，浏览器坦率地说是令人难以置信的。</p>

        <p>你可以在文本文件中编写的JavaScript，然后在Web服务器上为浏览器摄取提供服务，这是惊人的。这实际上是一个非常好的体验。你不需要任何预处理。你可以只写文本文件，将它们发送到浏览器，你有一个令人难以置信的开发。</p>

        <p><strong>Lex：</strong> 我们也应该说它可能有点破碎，至少HTML，但即使JavaScript也可能有点破碎，它仍然有点工作，也许是半屁股工作。</p>

        <p>但就像，浏览器必须处理的混乱代码的数量是疯狂的。</p>

        <p><strong>DHH：</strong> 这是当今计算中最困难的问题之一，就是解析整个互联网。因为对我们作为Web开发者来说幸运的是，但对浏览器开发者来说可能不是那么多，除了Flash的短暂时期，每个曾经创建的网页今天仍然运行。</p>

        <p>我在九年级做的网页今天会在现代浏览器上渲染，30年后。</p>

        <p><strong>Lex：</strong> 这太疯狂了。</p>

        <p><strong>DHH：</strong> 这完全疯狂。当你想到我们与网络的进化量，我们如何使它变得更好，浏览器采用了多少更多标准。今天创建一个新浏览器本质上是一个阿波罗项目，这就是为什么它不经常发生，这就是为什么即使像微软这样的公司也必须扔毛巾说，"我们做不到。"现在我实际上不认为这对网络有好处。</p>

        <p>如果我们只是得到一个运行一切的单一浏览器引擎，就有单一文化的危险，我们处于这种危险中。我喜欢Ladybird项目，例如，正在尝试从头开始制作一个新的浏览器引擎。我支持了那个项目。我会鼓励人们研究那个。</p>

        <p>这真的是一个美妙的事情。它由一群过去在其他浏览器项目上工作的人组成。</p>

        <p><strong>Lex：</strong> 真正独立的Web浏览器。</p>

        <p><strong>DHH：</strong> 我们真的需要那个。但我可以在我的脑海中保持那个想法。同时，我在我的脑海中保持这样的想法，谷歌的Chrome对网络作为首要Web开发平台的生存至关重要。</p>

        <p>如果不是因为谷歌和他们的整个业务依赖于蓬勃发展的开放网络，苹果、微软，我认为会很乐意看到网络消失，消失成为只是服务于他们可以完全控制的本机Web应用程序或本机移动应用程序和本机桌面应用程序的东西。所以我对谷歌有各种各样的问题，但不是Chrome。</p>

        <p>Chrome是对世界各地Web开发者的完全礼物，对网络作为开发平台。他们应该得到巨大的信贷，我认为为此。即使它与他们的商业模式纠缠在一起，Chrome的一半是监视你或通知有针对性广告的代码。还有一堆事情，我不是很喜欢。我可以将其与我们需要网络角落的冠军这一事实分开，他们有数万亿美元的市值价值骑在开放网络上。</p>

        <p><strong>Lex：</strong> 我们要进行切线上的切线上的切线。所以让我们去Chrome。我认为Chrome对人类的积极影响是不可估量的，原因就是你刚才描述的。在技术方面，它们呈现的功能，它们创造的竞争，它刺激了Web技术的美妙繁荣。但无论如何，我必须问你关于最近司法部试图分拆Chrome和谷歌的事情。你认为这是一个好主意吗？你认为这会造成伤害吗？</p>

        <p><strong>DHH：</strong> 这是一场灾难。我这样说是作为一个对反垄断斗争非常同情的人，因为我确实认为我们在技术中有反垄断问题。但我们没有这些问题的一个地方，总的来说，是浏览器，是我们用来访问开放网络的工具。</p>

        <p>首先，我们有Firefox。现在Firefox做得不是很好。Firefox多年来一直由谷歌支撑，以阻止正在与司法部发生的事情，即他们是镇上唯一的游戏。苹果有Safari。我对苹果也有很多问题，但我喜欢Safari。我喜欢我们有一个在首要操作系统上运行的首要浏览器，人们不能将网络变成只是Chrome体验的事实。</p>

        <p>但我也认为开放网络需要这个万亿美元的冠军，或者至少从中受益。也许它不需要它，但它肯定从中受益。在技术中垄断形成的所有错误事情中，Chrome是最后一个。</p>

        <p>这就是为什么我有时对反垄断斗争感到如此沮丧，有真正的问题。我们应该首先关注首要问题。比如我们手机上的收费站。它们是更大的问题。不是开放网络。不是我们用来访问开放网络的工具。如果我不想使用Chrome，如果我的在互联网上运行的业务的客户不想使用Chrome，他们不必这样做。</p>

        <p>我们从来没有被迫通过它。开放互联网仍然是开放的。所以我认为司法部选择以这种方式追求谷歌真的很遗憾。我确实认为有其他事情你可以为谷歌钉住，他们的广告垄断也许，或者在控制广告分类账的两边所做的恶作剧，他们既控制供应又控制需求。</p>

        <p>有问题。Chrome不是吗？你最终会让网络变得更糟。这是我们在考虑立法时，当我们考虑垄断斗争时，我们总是必须记住的事情，你可能不喜欢今天的事情看起来如何。你可能想对此做些什么，但你也可能让它变得更糟。</p>

        <p>欧洲GDPR背后的良好意图目前已经达到了什么？每个人在互联网上讨厌的Cookie横幅。这不能帮助任何人做任何更好的事情，任何更有效的事情，以任何方式、形状或形式保存任何隐私，这是一个完全的失败，只丰富了律师和会计师和官僚。</p>

        <p><strong>Lex：</strong> 是的，你说Cookie横幅是欧洲在技术方面做得最差的所有地区的纪念碑。</p>

        <p><strong>DHH：</strong> 这是良好意图直接通向地狱的纪念碑。欧洲实际上在良好意图直接通向地狱方面是世界级的。</p>

        <p><strong>Lex：</strong> 所以地狱看起来像Cookie接受按钮，你必须接受所有Cookie。那就是地狱的样子，一遍又一遍。你实际上永远不会到达网页。</p>

        <p><strong>DHH：</strong> 只是在人类规模上，试着想象每天有多少小时浪费在点击那个上面。我们对网络作为人们享受的平台造成了多少伤害，因为它们。互联网部分是丑陋的，因为Cookie横幅。</p>

        <p>Cookie横幅应该拯救我们免受广告，广告可以让网络变得丑陋。有很多这样的例子，但Cookie横幅在一次感觉中让整个互联网变得丑陋。这是一个完全的悲剧。但更糟糕的是，这就是为什么我称之为欧盟搞错的一切的纪念碑，是我们已经知道这一点十年了。</p>

        <p>没有任何地方认真的人相信Cookie横幅对任何人做任何好事。然而我们一直无法摆脱它。有这一个我认为现在10或12年的立法。在每个可以想象的指标上都是完全失败的。每个人都普遍讨厌它，但我们似乎无法对此做任何事情。</p>

        <p>这是任何假装或假装为不仅仅是公民，而是世界各地的人们让事情变得更好的官僚机构的破产声明。这就是Cookie横幅真正让我恼火的地方。这不仅仅是欧盟。这是整个世界。你在这个星球上的任何地方都无法躲避Cookie横幅。</p>

        <p>如果你去到该死的火星乘坐埃隆的火箭之一，试图访问网页，你仍然会看到Cookie横幅。宇宙中没有人能逃脱这种荒谬。</p>

        <p><strong>Lex：</strong> 可能是火箭上的界面。</p>

        <p><strong>DHH：</strong> 它会更慢。你基本上会有150秒的ping时间。所以从火星通过Cookie横幅需要45秒。</p>

        <p><strong>Lex：</strong> 好吧，让我们回到我们一直在进行的这些递归切线的堆栈。所以Chrome，我们应该说，至少在我看来，不是不公平地获胜。它通过公平的方式获胜，只是更好。</p>

        <p><strong>DHH：</strong> 是的，如果我要为另一边偷人一半秒，人们会说，好吧，也许是的，大多数人确实有点勉强同意这是一个相当不错的浏览器。但然后他们会说它获得主导地位的原因是分发。它获得分发的原因是因为谷歌也控制Android，因此可以使Chrome成为所有这些手机上的默认浏览器。现在我不买那个。</p>

        <p>我不买那个的原因是因为在Android上，你实际上被允许发布一个具有与Chrome不同的浏览器引擎的不同浏览器。与iOS不同，如果你想发布浏览器，Chrome，例如，为iOS发布，但它不是Chrome，它是穿着裙子的Safari。iOS上的每个替代浏览器都必须使用Safari Web引擎。那不是竞争。那不是Android上发生的事情。</p>

        <p>再次，我认为有一些细微差别，但如果你缩小并查看我们与大技术的所有问题，Chrome不是它。Chrome凭借优点获胜。我勉强地基于那个认识单独切换到Chrome。作为Web开发者，我只是更喜欢它。我在许多方面喜欢Firefox。我喜欢它的精神，但Chrome是比Firefox更好的浏览器，完全停止。</p>

        <p><strong>Lex：</strong> 顺便说一下，我们从来没有提到Edge。Edge也是一个好浏览器。</p>

        <p><strong>DHH：</strong> 因为它也是穿着裙子的Chrome。</p>

        <p><strong>Lex：</strong> 但它从来没有得到爱。我不认为我曾经使用过Bing，我确信Bing真的很好。</p>

        <p><strong>DHH：</strong> 也许你有，因为你知道什么？Bing穿着裙子吗？</p>

        <p><strong>Lex：</strong> 什么？</p>

        <p><strong>DHH：</strong> DuckDuckGo。这实际上是我使用的搜索引擎。DuckDuckGo从Bing获得其搜索结果，或者至少它曾经这样做。如果他们改变了那个，那对我来说是新闻。</p>

        <p><strong>Lex：</strong> 好吧，也许一切都只是一个包装或裙子。一切都在下面穿着裙子。有一些其他的。</p>

        <p><strong>DHH：</strong> 有一些那个。</p>

        <p><strong>Lex：</strong> 乌龟，所有的裙子一直向下。</p>

        <p>好吧，我们在谈论什么？他们，我们从JavaScript和你学习如何编程到达那里。所以最终，大成功故事是当你用PHP构建了一堆东西，你实际上在发布东西。那就是Ruby故事出现的时候。所以你与编程的大爱情故事从那里开始了吗？你能带我去那里吗？什么是Ruby？告诉我Ruby的故事。解释Ruby给我。</p>

        <p><strong>DHH：</strong> PHP是将我从只能摆弄HTML并制作一些网页转换为实际能够自己制作Web应用程序的东西。所以我对PHP在这方面欠下巨大的感激。但我从来没有把PHP看作是一个召唤。我从来没有想过，我是一个写PHP的专业程序员，那就是我是谁，那就是我做的。</p>

        <p>我把PHP看作是我需要用来敲击计算机直到它产生我想要的Web应用程序的工具。这非常是达到目的的手段。我没有爱上PHP。我非常感激它教会了我编程的基础知识。我非常感激它为经济学设定了标准。但直到Ruby我才开始把自己想象成程序员。</p>

        <p>这发生的方式是我第一次被雇佣为专业程序员写代码实际上是由杰森·弗里德，我的商业伙伴。一直回到2001年，我在那时已经在PHP上工作这些游戏网站基本上18个月。没有人为我做代码付钱。我通过从哥本哈根，丹麦发送到芝加哥，伊利诺伊州的电子邮件与杰森·弗里德联系，发送给一个不知道我是谁的人。</p>

        <p>我只是提供主动建议。杰森在互联网上问了一个问题，我发送了答案，他在PHP中问。我发送了那个问题的答案。我们开始交谈，然后我们开始工作。顺便说一下，这是互联网可以允许的奇迹。</p>

        <p>哥本哈根的一个孩子从未见过芝加哥的这个人如何能够通过电子邮件连接并开始一起工作？顺便说一下，我们现在仍然在24年后一起工作。这太不可思议了。但我们开始一起工作，我们开始在一些客户项目上一起工作。杰森会做设计，37signals会做设计，我会带来编程PHP。</p>

        <p>在我们一起在PHP中工作了我认为两个或三个客户项目后，我们一直遇到同样的问题。每当你与客户合作时，你从电子邮件开始那个项目。哦，是的，让我们一起工作。这是我们正在建设的。你开始交易越来越多的电子邮件。在几周过去之前，你必须向项目添加某人。</p>

        <p>他们没有电子邮件。他们没有上下文。你发送它，最新文件在哪里？哦，我已经在FTP上上传了。它就像finalfinal_v06_2.0，对吧？那是要得到的。这只是一团糟。在某些方面是美丽的混乱，在某种程度上仍然运行今天绝大多数项目的混乱。电子邮件是最低公分母。这很棒。</p>

        <p>但我们已经以严重的方式与客户失球几次，我们想我们可以做得更好。我们知道如何制作Web应用程序。我们不能只是制作一个比电子邮件更好的管理项目的系统吗？这不能那么难。我们一直在做博客。我们一直在做待办事项列表。</p>

        <p>让我们把其中一些放在一起，只是制作一个系统，其中参与项目的任何人需要的一切都在一个页面上。它必须足够简单，我不会举办研讨会教你如何使用系统。我只是给你登录代码。你要跳进去。所以那是Basecamp。当我们开始在Basecamp上工作时，我第一次在与杰森的经历中有技术选择的自由。没有客户告诉我，"是的，PHP，听起来不错。</p>

        <p>我们知道PHP。你能在PHP中构建它吗？"我有自由统治。在那个时候，我一直在阅读IEEE杂志和2000年代初的其他几本杂志，戴夫·托马斯和马丁·福勒一直在写关于编程模式和如何编写更好代码的文章。这两个人，特别是，都在使用Ruby来解释他们的概念，因为Ruby看起来像伪代码。</p>

        <p>无论你是在C或Java或PHP中编程，所有三个选区都能理解Ruby，因为它基本上只是读链接英语。所以这些人使用Ruby来描述概念。首先，我会阅读这些文章只是为了他们解释的概念。我会想，我喜欢你解释的概念，但我也想看看编程语言。</p>

        <p>我为什么没有听说过这个？所以我开始研究Ruby，我意识到在那个时候，Ruby可能不被任何人知道，但它实际上已经存在很长时间了。Matz，Ruby的日本创造者，早在93年就开始在Ruby上工作。在互联网甚至是一个东西之前。这里我在2003年，10年后，拿起似乎是这个隐藏的宝石，只是躺在默默无闻中，在众目睽睽之下。</p>

        <p>但戴夫·托马斯和马丁·福勒，我认为成功地让我和其他一些人走上了一种编程语言的轨道，这种语言在西方没有被大量使用，但可能是。所以我拿起Ruby，我想，这非常不同。首先，所有分号在哪里？我一直在PHP、ASP中编程，我甚至做了一些Pascal，我看了一些C。到处都有分号。</p>

        <p>这是第一个打击我的事情，该死的分号在哪里？我开始想，实际上，为什么我们在编程中有分号？它们是告诉解释器有新的指令行，但我作为人类不需要它们。怎么样？哦，有人在这里照顾人类，而不是机器。所以这真的让我感兴趣。</p>

        <p>然后我对自己想，你知道吗？我很了解PHP。我不是一个了不起的程序员。我没有在编程中工作那么长时间，但也许我能弄清楚。我要给自己两周时间。我要写一个概念证明，我与数据库交谈，我拉一些记录，我格式化它们一点，我在HTML页面上显示它们。</p>

        <p>我能在几周内弄清楚吗？大约花了一个周末，我完全着迷了。我完全被震撼了，因为Ruby是为我的大脑制作的，就像一个完美的定制手套，由我从未见过的人制作。这怎么可能？</p>

        <p><strong>Lex：</strong> 我们应该说也许像画Ruby具有的某些品质的图片，也许甚至与PHP相比。我们也应该说有一个荒谬的事情，我习惯于我忘记的，PHP到处都有美元符号。</p>

        <p><strong>DHH：</strong> 是的，是的，有线噪声。这就是我喜欢称呼的。</p>

        <p><strong>Lex：</strong> 有线噪声，这是一个如此美丽的短语。是的，所以有所有这些看起来像程序的东西。用Ruby，我的意思是Python有一些相似之处。它只是看起来像自然语言。你可以正常阅读它。</p>

        <p><strong>DHH：</strong> 这里有一个做五次迭代的野生循环。你可以字面上输入数字五，点，现在我在数字五下调用方法。顺便说一下，这是Ruby的美丽方面之一，像整数这样的原语也是对象。你可以调用5.times，开始括号。现在你在那个括号中的代码上迭代五次，就是这样。</p>

        <p><strong>Lex：</strong> 好吧，这很好。</p>

        <p><strong>DHH：</strong> 这不仅仅是好的，这是例外的。字面上没有其他我知道的编程语言能够将线噪声煮沸，几乎每个其他编程语言会注入到五次迭代在代码块上到那种程度。</p>

        <p><strong>Lex：</strong> 哇，这真的很好，好吧，谢谢你给那个例子。那是一个美丽的例子。哇，我不认为我知道什么编程语言做那个。这真的很好。</p>

        <p><strong>DHH：</strong> Ruby充满了那个。所以让我深入几个例子。因为我真的认为它有助于画图片。让我通过说我实际上，我喜欢Python的精神来为此做前缀。我认为Ruby和Python社区分享很多相似之处。它们都是动态解释语言。它们都专注于即时性和生产力以及在很多方面的易用性。但然后它们在许多其他方面也非常不同。它们非常不同的一种方式是美学上。Python，对我来说，我希望我不会太冒犯人们。我以前说过这个，它很丑。</p>

        <p>它在其空间中很丑，因为它充满了多余的指令，这些指令对于Guido在87年制作Python时的遗留原因是必要的，这些原因仍然在2025年这里。我的大脑无法应对那个。让我给你一个基本例子。当你在Python中制作类时，初始化方法，起始方法是def。</p>

        <p>好吧，公平。这实际上与Ruby相同，D-E-F，方法的定义。然后它是下划线，不是一个，下划线，两个，init，下划线，下划线，括号开始，self，逗号，然后第一个参数。</p>

        <p><strong>Lex：</strong> 是的，整个self事情。</p>

        <p><strong>DHH：</strong> 我看着那个说，对不起，我出去了。我做不到。关于它的一切都冒犯了我的核心敏感性。这里你有所有新对象或类必须实现的最重要方法。它是我在任何地方见过的最美学上冒犯的输入初始化方式之一。你们对此没问题吗？</p>

        <p><strong>Lex：</strong> 嘿，你让我，你知道，你就像在谈论我的婚姻或类似的东西，我没有意识到我一直在一个有毒的关系中。然而，我只是习惯了它。</p>

        <p><strong>DHH：</strong> 对我来说，顺便说一下，那是Ruby的魔力。它打开了我的眼睛，帮助美丽的程序可能是。我不知道我一直在ASP中工作。我一直在PHP中工作。我甚至没有美学美丽代码是我们可以优化的东西的概念。我们可以追求的东西。甚至超过那个，我们可以追求它超过其他目标。Ruby之所以如此美丽，这不是意外，也不容易。Ruby本身是在C中实现的。解析Ruby代码非常困难，因为Ruby是为人类编写的，人类是混乱的生物。他们喜欢以正确的方式的东西。我无法完全解释为什么__init__让我反感，但它确实如此。当我看Ruby替代品时，它真的很有指导意义。</p>

        <p>所以它是def，同样的部分，D-E-F，空格，初始化，括号。甚至不是括号。如果你不需要在参数内调用它，甚至没有括号。这本身实际上也是一个主要部分。如果人类不需要额外的字符，我们不会只是把它们放进去，因为解析计算机会更好。</p>

        <p>我们要摆脱分号。我们要摆脱括号。我们要摆脱下划线。我们要摆脱所有那些丑陋，所有线噪声，并将其煮沸到其纯粹的本质。同时，我们不会缩写。</p>

        <p>这是Ruby和Python之间美学的关键差异。init，短类型，只有五个字符。初始化要长得多，但它看起来好得多，你不经常输入它。所以你应该看一些漂亮的东西。如果你不必一直这样做，它很长是可以的。那些美学评估在Ruby语言中到处都是。</p>

        <p>但让我给你一个更好的例子。if条件。这是所有编程语言的基石。他们有if条件。如果你采用大多数编程语言，它们都有if。这在几乎每种语言中基本上是相同的。空格，开始括号，我们都这样做。</p>

        <p>然后你有也许，让我们说你调用一个叫做user.isadmin的对象，关闭括号，关闭括号，开始括号，这是我们要做的，如果用户是管理员，对吧？那将是一个正常的编程语言。Ruby不这样做。Ruby几乎煮沸了所有这些。我们从if开始。好吧，那是一样的。</p>

        <p>没有括号必要，因为人类没有歧义来区分下一部分只是一个单一语句。所以你做if，空格，用户，点，管理员，问号。没有开括号，没有括号，什么都没有。下一个，开行。这是你的条件。那个问号对计算机没有意义，但它对人类意味着什么。</p>

        <p>Ruby纯粹作为人类之间的沟通工具放入谓词方法样式。解释器实际上更多的工作能够看到这个问号在这里。为什么这个问号在这里？因为它读得如此好。如果user.admin？</p>

        <p>这就是Ruby的美学。这就是为什么我如此深深地爱上了这种编程语言。几乎有一种神圣灵感的感觉，无论Matz在编写Ruby的初始版本时在哪里，都超越了时间，以至于没有人甚至开始达到它。这是我总是觉得迷人的另一件事。我通常相信有效市场理论，如果有人想出更好的捕鼠器或更好的想法，其他人最终会复制他们，以至于也许原来的捕鼠器甚至不再被记住。没有人能够复制Ruby的那种本质。</p>

        <p>他们借用了元素，这完全没问题，但Ruby在这些指标上，在这种对人类和程序员的信任上仍然比其他人都高。</p>

        <p><strong>Lex：</strong> 我们也应该说，你知道，也许在那个指标上的完美编程语言，然后有成功的语言，这些往往是不同的。关于Brendan Eich创建JavaScript的故事有一些美妙的东西。JavaScript接管世界的方式有一些真正美丽的东西。我最近有机会访问亚马逊丛林，我最喜欢做的事情之一就是看蚂蚁接管任何东西，一切。就像它是一个很好的分销系统。</p>

        <p>这是一个混乱的东西，似乎没有秩序，但它只是工作和它的机制。</p>

        <p><strong>DHH：</strong> 更糟糕是更好的。更糟糕是更好的。我的意思是，这实际上是软件开发中的一个模式的名称，以及其他如何做的方式是Linux的模式。Linux在数量上比我认为当时是Minx更糟糕。</p>

        <p>其他方式更像大教堂，不那么奇怪，它仍然是一个有一些东西的，不完美可以帮助某些东西前进。这实际上是我研究的一个技巧，以至于我现在几乎在我做的所有开源中都融入了。我确保当我发布我工作的任何新东西的第一个版本时，它有点破碎。</p>

        <p>它在邀请人们进来帮助我的方式上有点破碎。因为没有比放出他们知道如何修复和改进的东西更容易获得其他程序员合作的方法了。</p>

        <p><strong>Lex：</strong> 是的，这太棒了。</p>

        <p><strong>DHH：</strong> 但Ruby在某种程度上，或者至少在这方面有点不同。不是在所有方面。Matz得到了语言的精神，语言的设计恰到好处。但Ruby的第一个版本非常慢。花了，我的意思是数百人年才让Ruby既美丽又高效且真正快速。</p>

        <p><strong>Lex：</strong> 我们应该说让你爱上这种特定编程语言的东西是元编程吗？</p>

        <p><strong>DHH：</strong> 是的，所以这采用了我们刚才谈论的所有这些元素，并将它们调到11。我会快速解释元编程。</p>

        <p><strong>Lex：</strong> 是的，请。</p>

        <p><strong>DHH：</strong> 元编程本质上是5.days的一个版本。你可以向语言添加关键字。Active Record是Rails中与数据库通信的部分。这是一个系统，其中数据库中的每个表都由一个类表示。</p>

        <p>所以如果我们再次采用用户示例，你做类用户从active record base下降，然后你可以写的第一行是这个。我希望我的用户有很多帖子或有很多评论。让我们这样做。我们正在制作一些用户可以发表评论的系统。下一行是has_many，空格，冒号评论。现在你已经在用户和评论之间建立了依赖关系，这将为用户提供整个主机的访问和工厂方法，以便能够拥有评论，创建评论，在该行中更新评论，单独has many看起来像一个关键字。</p>

        <p>它看起来像Ruby语言的一部分。那就是元编程。当Rails能够将这些元素添加到你如何定义类中，然后运行代码，向使用类添加一堆方法时，那就是元编程。当元编程以这种方式使用时，我们称之为领域特定语言。</p>

        <p>你采用像Ruby这样的通用语言，并将其定制到某个领域，比如在对象级别描述数据库中的关系。这是你可以做的早期例子之一，用户有很多评论。Belongs_to :account。现在你已经建立了一对一关系，在我们有一对多关系之前。Rails充满了所有这些领域特定语言，有时它甚至不像Ruby。</p>

        <p>你无法识别Ruby关键字。你只能识别在其自己的编程语言中看起来像关键字的东西。现在再次，我知道Lisp和其他人也做这些东西。他们只是用可以塞进编程语言的最大量的线噪声来做。</p>

        <p>Ruby在一个你无法区分我的元编程和Matz的关键字的水平上做，并且零线噪声。</p>

        <p><strong>Lex：</strong> 是的，我应该说我的初恋是Lisp。所以有一个你看不到的慢眼泪。</p>

        <p><strong>DHH：</strong> 我实际上从来没有自己写过任何真正的Lisp。</p>

        <p><strong>Lex：</strong> 好吧，那你怎么能如此严厉地判断它呢？</p>

        <p><strong>DHH：</strong> 因为我有两只眼睛，我可以看代码，我的美学敏感性禁止我甚至走得更远。这是一个限制，我知道。</p>

        <p>我实际上应该深入Lisp，因为我发现我学到了很多，只是深入，也许我在这里再次侮辱Lisp，但编程语言的过去，例如Smalltalk。我认为Smalltalk是一个令人难以置信的实验，也有效，但不适合今天的编程环境。</p>
    </div>
</body>
</html>