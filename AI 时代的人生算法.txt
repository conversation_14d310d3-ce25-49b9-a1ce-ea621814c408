# AI 时代的人生算法

**副标题：当 AI 比 99% 的人类更会思考和写作，我们的人生还有意义吗？**

*本文整理自 INDIGO TALK - EP30 对话节目*

---

## 核心观点预览

人生算法的本质包含两个要素：第一，找到你真正的内核——你有优势、有价值、愿意长期投入且不会疲惫的事情；第二，想办法有效、高效、大规模地复制它。

我不认为AGI来临后大家都可以躺平。即使AI想做这件事，但AI的老板也不会同意。所以这种想法属于痴心妄想。

ChatGPT Pro的深度思考功能应该是一个重要跨越点。它给我的感觉不是好用与否的问题，而是让我怀疑：我写的这些东西还有意义吗？

---

## 第一章：嘉宾介绍与创业经历

**Indigo：** 欢迎回到Indigo Talk。这一期我邀请了一位新朋友，其实我们认识比较久了——老喻。他是知名公众号"孤独大脑"的主理人，我的微信好友里有将近五分之一的人都关注了他，这对一个资深账号来说很不错。

因为我们现在住得比较近，算是邻居，平时会有一些日常聊天。我想到可以一起录制一期节目，因为他之前在得到上有一个很有名的课程叫"人生算法"，我也买过，推荐过很多朋友。今天我们正好聊聊AI时代的人生算法如何结合。

老喻，先自我介绍一下吧。

**老喻：** 大家好，我是老喻。我也不知道怎么介绍，随便说说吧。我出生在湖北，和Indigo是老乡。大学毕业后就开始创业，正好赶上房地产那一波，参与做了一些房地产项目。中间也做过一个科技公司。

那段时间运气还不错，房地产那块我们喝了点汤，确实收获不少。科技公司也参与了一些行业互联网化的事情，当时运气也挺好，2008年卖给了一家上市公司，2009年还去纳斯达克转了一圈。

我比较早就退休了。大概20多岁的时候，我就有一个奇怪的念头——要在35岁退休。我设定了这个目标，选择35岁只是因为它是个整数，而且年轻时觉得35岁好老。现在过了50岁，反而觉得35岁好年轻。

后来就到了温哥华。大概2013年左右，我40岁的时候到了温哥华。大家知道温哥华是个很无聊的地方。
Indigo在我们这里算是最活跃的，是天花板级别的存在，像我这种就特别无聊。那个时候就开始写公众号"孤独大脑"。

这个公众号在前四年，整整四年时间里几乎没什么人看，因为写的内容特别反流行。我也不知道要写什么，只是希望能在上面探索一些未知的世界，涉及一些非常边缘的东西。所以在相当长的时间内，只有3000多个订阅者。

做到这一点其实挺不容易的。那时候随便写个公众号，我有个朋友写美食公众号，发了两篇文章就有六七万订阅者。我的公众号写了4年时间，只有3000多订阅者。这个朋友跟我开玩笑说："你得非常努力才能做到这一点——让别人不来看你的。"所以也挺奇怪的。

大概2017年1月份，得到的罗振宇把这个号挖掘出来，推了一下，后来这个号就出圈了。从3500到7000到7万，到现在差不多有将近100万订阅者。非常幸运能有这样一个业余爱好，也不知道要干什么，它就像是一个人生的礼物。当然所有礼物背后也可能有陷阱，在这个礼物的引导下，我也干了很多傻事。什么事都交织在一起。

**Indigo：** 100万公众号粉丝在以前是非常厉害的。现在公众号确实没有视频号那么活跃了，大家的阅读习惯也在减少。

**老喻：** 是的，因为文章太长了。现在大家还是更喜欢刷短视频。
## 第二章：思考的动力与创作的意义

其实前段时间我看到一个研究，说人去思考、去动脑筋其实是一种特别不舒服的状态。舒服的状态就是躺着刷视频，这是最舒服的。所以只有极少部分人能够从主动思考当中获得乐趣。

比如说Indigo，你经常去思考、研究行业，你觉得这种动力和乐趣来自哪里？如果躺着刷视频没有负罪感的话，你会觉得很爽吗？

**Indigo：** 我觉得我自己还是比较有计划的，做事情比较有规划，已经习惯了。实际上大家说来了温哥华都属于退休状态，我2019年来这里，疫情期间都属于退休状态。我也感受过退休一段时间，但我比较喜欢折腾，那时候就搞股票。

现在短视频更加流行了，我自己会有控制，因为我会有一些目标。我觉得每天要完成一点什么东西。
没有具体工作做的时候，我也想完成分享这个目标。比如今天看到什么东西，我想把它组织一下再分享出去。这是一个我认为比较好的促进自己学习的习惯。

因为你分享，就要阅读、要组织，哪怕是发一条140个字的推文，或者再长一点几百个字，也得重新组织思路。当你组织思路的时候，就会更加聚焦，就会把很多空余时间填住——人要假装很忙。

分享是一个很好的让自己变得"假装很忙"的方式，对自己学习是有帮助的。而且最大的好处是，如果你一旦形成正反馈循环，对你的网络建立是有帮助的。你看你有100多万粉丝的网络，所以你发一篇文章，用户的反馈很高，你会马上得到回应。

就像AI训练一样，反馈信号越高，奖励越及时，对训练效果就越好。

**老喻：** 确实如此，我自己也是这样想的。
## 第三章：AI辅助创作的实践与思考

**Indigo：** 我记得前两周你跟我说过，你的"孤独大脑"公众号现在回归了，要改成日更。至少试了一阵子，差不多接近一个月的日更。

我们俩现在都在用AI，而且我们两个人的背景不一样。我是纯理工科背景，以前做社交网络、做程序，后面也做个人投资。可能后面的路线我们比较像，但前面你是做房地产，我们俩完全是不一样的行业。不过我们都算是赶上了中国两个大行业的成长期——地产和互联网。

两个背景不一样的时候，我有我用AI的方法，你有你用AI的方法。我很好奇，你上次也跟我提到，你在改回日更的时候AI对你帮助很大。你为什么会这样做？这是创作，也是刚才你问我的问题——怎么让自己不无聊。所以你改成日更
肯定有一部分想法是让自己的内容更加充实，或者说让自己的时间被填得更加充实一些。第二个，如何用AI来帮助现在的公众号做日更，我很好奇你有没有什么方法可以给大家分享一下？

**老喻：** 其实这个话题我可以放得稍微更大一点。看起来是一个如何运用AI来创作内容、写东西的问题，但其实可能比这个要更加复杂一点。

首先，我自己写公众号是一个相当个人化的事情。因为我本身其实也不是一个写作爱好者，我是一个思考的爱好者。我觉得公众号的文章，在某种意义上来说是自己思考的一个呈现。

所以我对用AI来创作内容这件事，其实是一种挺怀疑、也有一点点迷茫的状态。举个例子：
(10:37) 我觉得这个转折点 大概其实也就是最近这几个月 我大概去年呢 从大概是从五六月份开始 说是在准备得到的第二门课程 就决策算法 这个呢要写100讲 所以其实内容压力还是挺大的 因为100讲 每一篇大概是差不多4,000-4,500字 嗯然后呢 因为他是要录成音频 因为和我们想象中不太一样 我们总觉得音频 口语化嘛 好像口水多嘛 那叫口水文 但事实上 如果大家试一试 你如果要 做成像这种所谓的音频课程 它其实挺吃信息的 就你写文字可能写个500字 但是录成音频啊 可能100字就搞定了 所以它其实挺消耗信息的 所以那大概是在 Chat GPT的Pro版出来的时候呢 我就开始借助于Chat GPT Pro 然后呢 我来去嗯协助我来写这些东西 但这次过程中呢 其实我们知道那个时候 事实上最近一段时间
(11:47) 其实整个大模型的进步 是非常非常快的 就中间有几个跨越点嘛 我觉得这个(Chat GPT)Pro的这个深度思考 应该是跨越点之一 那他给我的一个感觉呢 其实不是说好用不好用的问题 而是更往前一步 是让我怀疑我写的这东西还有意义吗 就是这个结论 就是OK 那对对对 因为我可能就是 是一个比较怀疑论者 因为我觉得大多数文字啊 大多数出版的图书啊 其实都是浪费纸张 大多数的这个话语啊 都是废话是吧 但大家出于社交的需求 出于这个都是老生常谈的话 说来说去 那么当大模型可以用一种更系统 更跨专业更深入 嗯我觉得不管从任何一个角度 都远超过99.9%的人类的时候 写字还有意义吗 其实是这个AI对于写作这件事 对我第一的触动
(12:55) 嗯所以呢 在完成决策算法的 当然我们再说一下 就是我在怎么用 我怎么用去协助他 虽然一方面怀疑啊 一方面觉得挺好的 那就用起来嘛 那我基本上我使用的方式呢 我是同时用几个 嗯 我还是把它当做一个思考的一个助手 而非 而非单纯的写作的助手 因为你希望能够有些稍微独特一点的观点 那么我大概同时用3-4个 Gemini是后来用的 因为Gemini 我们知道后来的进步速度特别快嘛 嗯之前呢 是以ChatGPT和Claude这两个为主 嗯那我会用 让这三四个AI 有点像我的一个团队一样 假如我有某一个主题 我会抛给他们 然后我希望 能给我一些相关的思路 或者我的思路大家能够做一些延展 然后延展了之后 这是第一轮 然后我会把这个反馈收集过来 然后我只会选择 当中哪些让我心里咯噔一下的 我觉得哎 这个还不错 然后我再结合我的内容
(14:03) 然后再进行第二轮 嗯这样的话 它其实有一点点像头脑风暴 一方面像头脑风暴 另外一方面呢 其实在我看来 它形成了这种AI之间的杠杆效应 就是 这种叠加在一起 其实还是挺强的 他经常会通过这种层层递进的方式 而且是这种交织式的递进的方式 嗯我想呢 不管从物理的角度 还是从数学的角度 它很容易得出一种解释 就是这种它 它得出来的内容的质量 其实它的一个进化的速度 会非常非常快 当然这里面有一个特别 特别重要的一件事呢 就是作为写作者 你自己的一个taste 可能会非常非常重要 就有点像 比如说我们通常所说 不管是Elon Musk 还是比如说乔布斯 其实他们他们虽然比如说马斯克 他可能算是有技术背景 但是乔布斯并不算是特别的技术背景 是吧我们可以说 但他其实有特别好的技术的taste 所以呢我觉得嗯OK 你有一个很好的一个taste 你可以去让他去用啊
(15:11) 这是一个我使用AI的a面 我觉得它是帮助我去思考啊 帮助我去扩展啊 等等等等 能够嗯 而且呢嗯 他如你所知啊 上次我们好像也聊过这个话题 其实他在跨学科方面是最强大的 对我想在人类可能很难找到类似的 人类可以同时在很多个学科 达到一个非常高的一个水准 一旦叠加在一起 他这个是他的一个独特性 几乎是独一无二的 那么这部分是创造性的部分 嗯另外一部分呢 就我把它嗯 当做我用在AI 在完成课程或者写作的b面 b面呢就有点像体力活的那部分 所谓的体力活的部分呢 我相信你也是啊 我相信你是一个呃 技术背景的一个思考者 你可能很多时候你有一个idea 对你来说 这个整个的思考的过程已经完成了 嗯假如你跟一个很聪明的人 你一句话 你们两个人都互相咯噔一下哇 太爽了你这个想法太有趣了 但是如果你要给很多人表达的时候呢 你还得在想 对我怎么把这个东西变成一篇文章 变成一个表达 变成因果 其实你有一点点在做向下兼容的事儿
(16:23) 嗯然后呢 有些东西呢 未必是 你特别感兴趣的 要探索的那部分 但你还是得去做 我发现这个时候呢 AI它特别帮忙 就是它能够把这种基本的组织工作 能够把你完成的特别特别好 所以呢这两件事呢 就是特别像 打个简单的比方啊 我觉得AI在很多方面 就有点像红玫瑰和白玫瑰 这个创造性的 激情的灵感的那部分 有点像红玫瑰 嗯 然后另外那一部分 就是叫把事情弄得妥妥贴贴的 是吧啊 在家把你家的娃带的好好的 白玫瑰 哈哈哈 你这你这个比喻很形象啊 对对对是 嗯我补充一下 正好就是你刚我们刚才聊到的 是你怎么用AI来做你的日更创作嘛 我觉得这个和我的用法其实挺像的 然后呢因为我现在我自己 首先我做两部分内容 一部分是嗯 可能主要是短社交的短内容 就是我每天看了一些什么东西 我就分享出去啊
(17:32) 这个时候呢 AI主要是帮我总结 帮我总结 而且很快 速度很快 总结的方法呢 我可能你刚刚说的挺对的 有一点你看冗长的内容啊 比如Podcast对谈啊 AI有时候发现的东西呢 不一定有我这样子 人看的时候那个触动点是不一样的 因为我会就人类有个很特别的东西 就是我的观点 他这一点打动我 我觉得大家在用 AI的时候 这一点是不能替代自己的啊 就是你必须得去 哪怕让AI总结完 那他尽可能详细的总结之后呢 比如说你用o3或者o1 Pro 或者是Gemini 很详细总结 你说请详请详述这个东西就好了 好他会非常详细的 而且你还可以让他有些提示 让他把这东西抽象出来 然后你可以找到一个点 然后跳到一点去看 然后噢听完之后 之后我就会有感觉了 然后我知道 关键是他总结出来我没法用 我得提问 而且你说的这个问题你得问他 而且问的时候呢 我会两个AI同时用 我可能会把Gemini总结的详细内容 我问一下o3 我觉得o3真的很聪明 他就聪明到已经言语太简单了 太简洁了 就简洁的非常高效
(18:39) 那种感觉 就是他没有一句废话 然后很快的 就可以把逻辑给你提炼出来 而且有时候会让我有些惊喜 他的提炼啊 这个是我感觉 怎么说呢 我用了这个Gemini 2.5 包括Claude 4 Claude 4可以用它来产生点子和写文章 但是呢 提炼内容的时候 我还是喜欢用O3和做解释 O3做解释特别好 因为它有搜索 这一点我觉得是 我的感觉是在这儿 然后另外一个呢 我自己在 我现在也在创作 我在写书 你刚才说的挺好的 我现在有点怀疑人生 我为什么要写书 你直接问AI不就好了嘛对吧 对 哈哈哈 我也在怀疑自己这个决策 但是我觉得呢嗯 我们作为人吧 还是要留下一点什么东西 对这个书其实就是人 就是包括你做的这些视频 或者这些东西 我们想留下一一点 对这个世界 我认为还有一点价值的东西 代表我自己的东西 所以说我觉得有一些那种纯粹的 那种填充时间的 或者填空的 或者帮大家填时间的 那些人类吭吭哧吭哧要去创作东西 其实不需要做了 以后让AI自动化生成就好了 但是呢 人要做的就是能够把我自己的体验
(19:52) 把我自己的想法 把我自己的这个理解给表达出来 他就是我 这就代表我 哪怕我写的东西可能很废话啊 但是他就是我 嗯就是我 就是我觉得这点是作为一个创作者 我觉得我 我应该我 我想坚持的 而且我会让这个东西呢 其实我自己的视角啊 AI有个特点唉 你让他用什么视角 他都能够用什么视角哈 他视角太多了 视角太多就是没有视角对不对 但是人呢 我总是很偏执的啊 我就是我 我很固执 我总是用这样的一个角度来看问题 那就是我 所以说我觉得呢 我在写这本书的时候 我在写与AI共生嘛 那就我自己的视角 我可能会用问了好多问题 问问这个 问O3 他给我详解 解释完了 各种来源引用的资料啊 这些什么东西 但是 最终我还是会把它变成我自己的视角 然后可能会让 变成一个非常详细的一个思考过程 我再让Claude再帮我创作一遍 他创作的 Claude创作一个好处 他有时候会产生很多 很有创意的金句 你可能意想不到的 就他特别适合做creative的事情 Claude 4所以说这两个我 我把这几个模型 就是用Gemini当牛马是吧 让o3做提问 你把o3当成一个特别好的导师问他
(21:05) 他都可以给你很surprise的回答 然后呢把Claude当成一个灵感捕手 你就说我 我扔一堆乱七八糟的 我可能我想的很混乱的语言 你帮我重组一下 然后给我一些经典的这种综述 他可以一下子来一个 很让我一下surprise的东西 然后呢我会 我会从它里面五六个surprise东西 我挑选一个出来 我觉得把我自己的这个这个沉淀 就这样子给完成了一个部分 所以我觉得这算是 这也算是我 我和他共 我和AI共生的一个价值啊 我把我放进去了啊 我自己的观点 我的独特的想法 然后呢我想在这一个时刻 2025年AGI诞生的前两年 是吧我留下一个作品啊 以后见证这个AGI诞生 所以我这次写这个写书的目标吧 我觉得是人生体验的一个刻画 可能你做当时 你做孤独大脑的这个账号的时候 也是这样 你要有很多想法 我觉得这个人生怎么决策啊 这个我想的就是这是我自己的想法 然后我们正好对我们说到决策 其实我觉得把这个话题往前面引一个 我要有第一个问题 因为我想问你几个 你的人生算法里面 几个能够有代表性的和AI结合的问题
(22:16) 然后第一个问题呢 我们的教育系统 其实没有教过我们怎么做决策 我感觉是这样子的 我们的教育系统特别是 中国这个在华语圈里面教育 我们更多的是做题嘛 我们找标准答案 我们解题 然后重复练习 一直到刻意练习到答题非常快 然后就可以考大学了 那这样一个教育是一种服从 或者说是一种很标准化的教育 然后那你怎么来看这个 其实还有一个 我还有一个观点 其实很多人人生也做不了太多决策 你不当领导 你不去做一个创业的时候 你其实做不了太多决策 如果一直上班啊 那么你想想看这个在AI的这个时代 就是结合你之前的那个人生算法 里面提到的内容 AI如何强化 你之前的那些这个算法里面的决策 你有没有什么一些建议给大家 的确啊就是嗯 选择和决策 它一方面的确非常重要 但另外一方面呢 的确我们又 从来没有好像从来没有教这些东西啊 嗯嗯 但是但是当然了 我们在小说啊 电影电视啊 或者我们从小的我们传统文化里面 很多时候也都涉及到这些
(23:28) 你面对未知世界 你做出了一些选择 有些选择好 有些选择不好 他是一个对我们可能生存是最最重要的 因为很多时候我们仔细想一想 嗯我们周围的人能够过得不错的 他都是挺擅长于做 选择挺擅长于做决策的 嗯反而是 大多数时候 比如说 我们在传统教育中间所学的东西 其实在现实中帮助 并不是特别大 并不是特别大的一个原因是在于啥呢 就是在于我们的一个教育体系呢 就本质上 不光是我们的教育体系 或者包括我们自己成年之后 我们所做的所有的事情 都是基于因果推理的 都是一些特别清晰的一个因果推理的 嗯他基本上都是充分的 就是已知条件是充分的 然后呢 基本上有一个接近于标准的一个答案 嗯但是什么叫决策 什么叫选择 嗯他有几个特点 他一定是充满不确定性的
(24:36) 否则的话 大家都能做对的选择 就像有些人说 哎呀我好后悔 我当初没有做这么明显的对的选择 就是这样 嗯就是因为当初你不甘心 当时你认为嗯 你自己做出来的选择是最好的 所以哲学上有一个说法呢 就是嗯 对于选择来说 它的一个原因和结果永远是不对称的 而如果是对称的 如果是线性的 嗯选择就没有意义了 嗯他一定是不对称的 非线性的 一定是嗯 超出预期的 嗯要么好一点 要么坏一点 永远是一个 比三体问题还要更复杂一点 嗯这个时候 选择才有价值 才有意义 当然 这个也很容易从物理意义上 去解释这一点 嗯就有点像 就是我们通常所说的哎 这是一个 假如我整个世界完全是因果分明的 那么我们此刻我们俩思考的问题
(25:45) 我们俩讨论的话语 嗯假如我们每个人 我们的大脑 又是由无数个原子和分子来组成的 所以 那么我们此刻所说的话 做的事情 可能是1亿年前 这个太空中的 整个宇宙中间的星辰的这种运动 已经决定好了的 如果这样的话 我们的自由意志就不存在了 当然我们并不能证明 自由意志真的能够存在 但的确了 嗯 的确就是 在某种意义上来说呢 这种不确定性 这种因果的不连续性 其实构成了这种选择的本质 但是恰恰是这些和我们的传统的教育 有完全不一样 所以呢这就是 我自己一直以来 对这个话题比较感兴趣的原因 一方面是我们四书特别关注的 另外一方面 我们又看到他的背后对应着 数学的物理的 哲学的问题 进而呢 假如你在这方面 你觉得我能够 发展出某种超乎别人的能力 你就有机会从这中间获得某些回报
(26:54) 比如说选股票 投资的机会 比如说选女朋友 选老公啊 等等 这中间你就可能从中间 就会有一些超额的一些回报了 嗯 我记得你之前提到过 反正角色有几个 一个是概率 就是其实刚刚提到的这个 我们之前可能教育 传统教育里面教我们都是稳定性的 答案嘛就是因果推论嘛 其实另外一个呢 其实世界有的情况下是概率的 就是它是一个随机分布的 对我们可能会很多事情并不是 并不是有因必有果的 而且很多情况下复杂问题 他是有非常多的选项和结果的 这是复杂问题 但是我们学校里面上学的时候 你就很简单 你考试拿好成绩 然后考好大学 那就很简单 你选择就是我考好分数 我就能上好大学 这个叫做 我们叫做虽然看上去工作起来很难 但是它是个简单问题 对不对 是个简单问题 但是如果说一涉及到复杂问题 就是像金融市场是个复杂问题 国际政治是个复杂问题 他有太多选项了 其实我们我们人生 如果说大家不去做经营啊 不去做商业啊 或者不去做科研 或者什么地方
(28:04) 你其实都还碰不到这么复杂的问题 对不对 然后呢其实但是我觉得还有一点 你之前里面提到的 还有一个叫停止决策 什么时候是落 落子是吧 这个这个是怎么看呢这个啊 对对对其实我倒把刚才那个话题 刚才你那个话题我觉得挺好的 我再把它稍微说一下 然后我们再说一下这个停止的话题 好就刚才你说到一个 的确就是是在一个复杂环境里面 就是嗯一个 刚才说到了概率是不确定性的 所以我们要用概率这个思考工具 去面对它 再一个他是一个复杂系统 我觉得这里面 其实可能还有一个特别特别重要的 就是必须要把达尔文给引入进来 嗯就是嗯就是 包括嗯就是 的确啊真的有点像 当年19世纪末的时候 波尔兹曼就预测说 将来是达尔文的世纪 我自己也觉得就是 如果从最近一二十年来说 我觉得达尔文是被最被我们低估的 一个科学家 因为我们以前学达尔文的东西 都还得背 是吧 一般这个成绩好的学生都不太喜欢 是吧就是都是背的东西 现在发现其实并不是 因为我们知道 就说在一个复杂的一个环境里面
(29:15) 不仅你需要用概率的工具去运用 而且连概率本身也是不确定性的 所以你要通过某种递进的方式 通过这种贝叶斯的方式 不断的去跟进更新 在环境里面 在不断的去所谓的进行迭代 然后在这种情况下 能够得出一些比较好的一些结果 我相信其实这一波的人工智能啊 大模型等等 很多也是基于这种思路 他有相当多的 不管是这个 强化学习 还是这个神经网络 都有点 这个对于生物学的一个模仿 嗯那当然 再回到我们 回到刚才所说的 就什么时候开始停止 其实这里面就是可能就涉及到一个 特别就是重要的一个 就是所谓的一个有限 有限性就是资源是有限的 计算的资源是有限的 时间是有限的 生命是有限的 能量也是有限的 所以在这种有限的情况的约束下了 我们的 做选择做决定 做决定其实就是在有限的情况下 去寻求一个相对于最优的解 寻求一个相对满意的解 也去分配有限的资源
(30:23) 所以我自己呢 对决策啊 对选择有一个定义 就是叫 以有限为前提 然后呢 面向未来的你 你的一个目标 你的这个目标只能是基于你满意的 然后呢在当下 沿着时间轴去分配你的资源 为什么是沿着时间 但这个沿着时间轴分配资源 这里面还有一个好玩的地方呢 时间既是分配资源的标识 时间也是资源本身 之所以是要沿着时间轴 去分配资源的话呢 就说明了 就是因为我们一直处在模糊中 当我们在模糊的时候 我们没有办法一下子all in 我们可能要分布下注 比如从投资的角度 可能你要依照凯利公式去下注 或者在这个过程中 你在不断的更新你的胜率 你越来越逼近 或者比对手获得某些概率上的优势 这样的你的下注可能会更有优势 但回过头来说 从什么时候开始停机 嗯的确就是 比如说举个例子 就像我们俩是吧 就是什么时候到温哥华 因为到温哥华 就是一个人生停机的一个选择
(31:37) 哈哈哈对 算是算是 培训下 培训下对对 你是硅谷的对啊 哈哈哈 对对当然你是那个 你是局部停机 因为你也经常去硅谷嘛 像我当时就真的唉 就打算停一下 就是为啥呢 因为啊我觉得觉得够了 就觉得满足了 我觉得我这样就可以了 但是我又不够坚定了 就是回头来看 其实是不够坚定 如果当时很坚定的我是停机了 那我所有的决策 应该围绕这个决定来展开 但是其实并没有 所以呢人生的很多乐趣 或者人生的很多苦恼也是来自于这种 游离 不甘心或者巴拉巴拉 哈哈或者悬而未决 或者说是有事情这决策了 那没这样做 是吧对 我觉得这个挺有趣的 所以你刚刚这个解释挺好的 我们刚刚提了两个 一个是 面对选择 其实面对决策 实际上我们现在是面对 面对的不是一个线性的逻辑系统决策 而是一个复杂系统的概率决策 对吧而且概率可能会分成哎 10%是这样子 30%是那样子的 其实概率会做的很复杂 回头 这个大家可以去听老喻的课就好了 我们这个上面也讲不了太多
(32:46) 然后另外一个呢 就是停止角色很重要 就是你的满足点在哪 就是说大家是吧 对谈女朋友 或者说是找 找什么那个相亲对象是吧 你10个范围里面 你前4个选中5个就好了 对不然你一辈子找不到对 哈哈开个玩笑 对就是剩女了 哈哈哈 嗯然后呢 这个我觉得算是一些方法吧 然后我们在AI 其实AI我觉得挺大程度上 去给了我们做概率决策 一个好的方法 因为 AI可以按照我们想要的这个概率分布 它可以给我们很多选择出来啊 而且它本身就是一个复杂系统的涌现 嘛AI 它挺适合在我们这个时候 来做出选择范围的 就是至少是推理模型 像O3这样 推理模型 我觉得这是一个增强 那么另外一个 我正好就是看到你之前那个 你整个 我记得你在那个人生决策里面呢 有个九段心法嘛 就第一个 就是有一个有个闭环啊 我们怎么建立感知 然后就我们刚已经聊到了决策吧 从感知到认知到决策到行动是 实际上我觉得我们刚才聊的这个 从概率选择 一直到这个到停止决策
(33:58) 应该就是这样的一个闭环吧 是的确如此 OK OK 了解嗯 那当然这里面呢 其实可以稍微多说一下 其实在某种意义上来说呢 比如说我们又要说回达尔文 其实每当我们做选择的时候呢 很多时候 大多数人呢 容易犯的一个错误会是说哎 我做了一个选择 所以你要给我一个结果 这个世界要给我一个结果 是吧是这样的 但事实上来说呢 当我们做出了一个选择之后呢 其实就像种子一样撒到了天空 然后完了之后 你其实对应的是我们 选择是一个基于概率的 然后接下来我们会面对一个被选择 被环境选择 被外部选择就极有可能了 比如说我做了一个对的决定 比如说我去 我去玩一个这个游戏 这个这个游戏的那个正面的 正面能够赚两块钱 反面能够 反面赚一块钱 那我肯定是选择正面是吧 然后概率都是50% 我肯定选正面 但是一个结果呢 嗯除了反面 就我没赚到这个钱 但是我这个决定依然是正确的 因为你这 这其实是一个很简单的例子 但他其实告诉我们一个真相
(35:08) 就是我们做出选择 对应的不是一个对的选择 对应的未必是一个对的结果 对的选择 只不过是让我们获得一个对的结果 这件事情的概率变得更大了 但并不确保它的发生 嗯那么但是呢 在某种意义上呢 我们也要意识到 我们做出一个选择之后 我们等于把我们的命运又抛出去 有另外一个外部环境 再做出一个再选择 所以从这个角度来说 嗯 所谓的一个认知闭环就可以这样理解 我做了一个选择 我把它抛出去 然后呢他回应给我一个 一个我的被选择 我的被选择呢 他其实有两重含义 他一方面给我带来一个或者好 或者坏的一个结果 他带给我的另外一个呢 就是带给我更新的信息 从贝叶斯的角度来说 我的先验概率 根据我这一次的结果 我把它更新了 是吧我 然后完了之后 下一次我再去掏出这个硬币的时候 我有一个更加接近真相的 一个我对于它的胜率和赔率的理解
(36:18) 所以呢嗯 这样的一个一个的闭环 在某种意义上来说 就就有一点点像所谓的认知飞轮 或者叫知行合一的飞轮 嗯你完成这个决策 我们可以把它理解为是行的一部分 但事实上呢 我们相当重要的一个结果是拿到啊 他这个闭环给到我们的一个反馈 就有点像一开场你所说的 这个反馈 其实对个人来说 是一个特别特别重要的事情 然后然后你再可以进行下一次决策 嗯了解 这个feedback 其实有点像对 大家我觉得我个人在用AI的过程中 跟他多轮讨论里面 他可以给我很多这样的一个闭环 我来做一个下一个判断 我觉得这一点 是AI对我帮助挺大的 然后还有一个模式啊 我们再说一个 也是你之前那个心法里面的 这个角色 有一个切换 模式切换 其实我们自己在用的时候 嗯怎么说呢 我形象比方一下 就是什么情况下是用自动驾驶 什么时候用主动控制 在做这个 在做这个 或者判断或者决策的时候
(37:28) 做事情的时候 这一点嗯 你之前因为我想想看 嗯自动驾驶有哪些东西 按照我的和AI类比来说 就什么时候我应该全部交给AI做 什么时候我应该拿回来全部控制 对对然后这种情况 嗯在你的这个决策 或者在这个算法的过程中 你是怎么来哪些事情 或者是对我提这样一个问题 哈哈哈对对对 嗯就我们还是把它拆开 就一个就说 你刚才提到了一个是AI的应用场景 然后再回到就是我们自身啊 就我们自身做选择做决策 或者我们在平时思考的时候 有这种自动驾驶 和这种主动的一个控制 嗯嗯 就有点像开始我问你的那个问题 就说 很多人会迅速的陷入到短视频里面 你说你有一个好的一个习惯 就我相信你从小从学习啊 或者你做事情 你是希望嗯 你经受过了这个训练 嗯在某种意义上来说呢 你其实从小不管是家庭环境 还是在学校 还是你自己在慢慢培养起来的 你已经形成了某些
(38:38) 特别好的 这样的一个自动驾驶的一个系统 所以这样的一个自动驾驶的一个系统呢 他能够我觉得对一个人 他可能会帮助特别大 不管是他的成长啊 不管是他在做一些事情啊 或者包括是在做投资方面啊 嗯他可能都会很好 都会有特别大的帮助 比如说举个例子 就说你会发现 其实 我自己也越来越意识到 假如一个人 真的想过上一种所谓美好的生活 你会发现呢 他一定是相当多的部分 我觉得至少要80%到90%以上的部分 嗯是由自动驾驶来控制的 也就是说他应该是基于某个系统 嗯嗯 如果总是在不断的动脑筋 不断的出妙招 不断的有很多灵光乍现 那往往这个人呢 他可能有精彩的一生 他未必有幸福的一生 就有点像价值投资者
(39:45) 一定都是无聊的 价值投资者在某种意义上来说 他是基于他的某个系统 我们一旦说到系统 我们更能更多的说到 他是具有某些自动化的属性的 所以呢从这个角度来说呢 当然这种自动化呢 又不是那种 流水线上的自动化 而是一个人 通过这种自己的主动的塑造 主动的良好习惯的养成 或者他的一些行事原则的形成 他形成了自己的这样的一个系统 我可以把这个理解为 一个人的自动驾驶的这部分 那我补充一下 我补充一下 我感觉Elon Musk的人生这是手动挡啊 天天给自己制造麻烦 对他是一个这样的 对他是一个这样的 但是让他自动化的那块 他也很聪明 而且我们知道从用人这块你就知道 就是他这个 作为一个 一个疯子啊 是吧一个这么在某种意义上来说 就是挺混蛋的一个人啊 是吧就传统意义上 是吧打个双引号 我有一篇文章也写 他说他是一个混球 传统意义上他是个混球 但他用的人都很靠谱 是吧 他能够把那种愿意用牺牲自己
(40:56) 不是牺牲, 是奉献自己的全身 为了某项事业 为了火箭 为了什么样的这帮最优秀的人 他能够团结在一起 我印象特别深刻的时候 在那个 space x的 在招聘员工前 1,000个员工 他每个人他都见 甚至连那个厨房 连那个餐厅的工作人员 他都是从 去星巴克挖他们的明星员工 所以他是一个 他不靠谱的那一面 就手动挡的那一面 就非常非常任性 但他为什么要去招这些最最优秀的人呢 就是作为他的员工 连厨房 因为这些人会有非常强的自驱力 对自己有很高的要求 所以呢他有一个特别特别好的 自动驾驶的一个基本盘 嗯所以他就可以很任性 这二者倒是相辅相成 OK这部分我们说完了 然后再跳到AI的这部分 嗯的确就是AI的这部分 这个问题一下子又特别复杂了 如果哈哈哈 对对对 如果是仅仅从使用的角度来说 真的完全自动化的一个AI 假如你是可以
(42:03) 那他一定又我们回到那句话 我们正好 伊隆马斯克这个话题给了我们一个话题 你得是一个像伊隆马斯克这样的人 嗯那么 AI的自动化对你可能会非常非常巨大 对对 巨大是吧 因为我记得好像那个那个谁啊 那个哈萨比斯 是吧那个Deepmind的那个创始人 他说他 他说 我现在就特别想AI给我干一件事 排在第一位的 就是帮他回邮件 就看起来特别小的事 是吧就是 对对对特别特别自动化的事 对但是 因为他是 他本身就从事着最前沿的探索 所以这个时候 一个高度自动化的 对他来说可能会非常非常有帮助 但是如果 如果你是一个 根本没有一个 很自由探索的 这种手动挡的自动 就是叫主动驾驶的事 你要去做 这种自动化对你来说有啥意义呢 没有因为 干脆AI彻底就自动化了 你这个人就可以被拿掉了 没有意义的 哈哈哈可以被完全拿掉了 嗯嗯 是的对 那这个我补充一下 我觉得 手动挡像伊隆马斯克这样的
(43:11) 他的手动挡 他的人生过得很奇特 就他的手动挡 给世界留下了好多很好的遗产 对很好的东西 对这个就是 我记得以前有个meme 就是有一个 有一个画 就是 Don't do that 伊隆马斯克说 伊隆马斯克画这 我就要做这个 你别做这个 我就要做这个 他这个 挺有趣的 他有个画嘛 就是聊这个 水里面的鸭子 是吧这样子一段话 唉你说的这个其实很有趣 就是又回到了 就是所谓的 我觉得我们刚才聊这个话题 涉及到一个就是 深水问题啊 就是碳基和硅基之间的人的问题 你刚才说伊隆马斯克 他的一个就是做这个事情 嗯嗯 就那个就 我这段时间又重新又翻了一下 那个那个彼得蒂尔的《从0到1》 就是他 其中呢他开篇的时候 他就提出了一个问题 其实作为整本书的一个基调 他说他面试人的时候呢 特别喜欢问别人一个问题 就是说 在哪些大家都赞成的某个话题上 你有不一样的见解 嗯就是 其实就说 你有什么与众不同的
(44:18) 对这个世界的理解 嗯 而且这个句式应该是大多数人都认为 这个电动车这件事是毫不靠谱的 但是我认为他会怎么怎么样 他还给出了一个句式 所以呢他其实就有点像 就也许 作为人类 从生物学的这种 它的一个演化的一个历程来说 它基本上有一个所谓的一个演化算法 演化算法是三个步骤 就是第一是变异 第二是选择 第三是复制 变异了 我们也知道它是随机的 并不是像我们小时候学的那样 它越变越好 因为它变异是一个中性的词 嗯它随机的 只有随机 才有可能有更多的这种可能性 嗯然后完了之后呢 是选择了 当然我们知道是一个交互式的选择 这个变异的这个主体 嗯他被环境选择 然后他再去选择环境 在这个交互的过程中 然后呢 一旦他达到某个临界点的时候 嗯他就有机会被复制
(45:27) 嗯所以从这个角度来说呢 其实我自己会在想呢 其实在某种意义上来说 假如是在这个演化算法的这个过程中 假如硅基生物和碳基生物共同 进行未来的这些演化 那么 假如继续在演化算法的这个框架下呢 其实人类越来越多的 扮演的是这个变异的这个角色 那么在选择的这个环境呢 因为我们知道选择它是需要时间的 但是在AI的辅助下 这个过程可以加速 是吧 就有点像蛋白质折叠这件事是吧 它其实是可以通过一种模拟的选择 和被选择 然后它可以加快这个速度 然后第三步就是大规模复制 这部分 我相信AI也能够扮演特别大的作用 所以呢回到这儿来说了 其实对于个体来说 嗯 也许最最重要的就是变异的这部分 就是你的 与众不同 你个体的与众不同 或者 你对这个世界有一点不一样的认识 你觉得有一些东西 嗯你有些有点奇怪的想法 你觉得要把它创造出来 或者怎么样 所以我觉得可能在未来
(46:36) 不管从教育 对于个体来说 可能这种与众不同 或者他的这种变异性 会变得 至少对AI来说 会是人类仅有的价值之一吧 对这个我想起一个广告 苹果在97年的the crazy one 是吧对 这个世界是由这些很疯狂的人推动的进步 对吧不管它是好的还是坏的 对对 都是由他们推动 我觉得这个当时 当时这个很深刻 这个广告 这个非常非常传神 传达了这个 乔布斯回去苹果 他要干的事情 对对是这个 但是 这个其实对所有人提的要求也太高了 哈哈我们的教育我们 我们回归均值之后 其实大部分人就 大自然就是这样的嘛 大自然就不会让所有人都去变异嘛 那可能只是少那么一点点啊 1/1,000,000啊 有可能或者更多 1/10,000,000 唉但是呢 我有一个另外的看法 如果说AI让把很多人事情都做了 把很多决策都做了 那么它会启动一轮淘汰程序 嗯嗯就是真的 这些刚刚说的就是全部都自动驾驶 它没有手动驾驶意义的人 那这些人
(47:42) 他们可能真的就它的下一代 或就没有 就不存在了 有可能就是这 这也是一种自然选择 我类比一下 我并没有说是什么 就我类比一下 就有可能大自然它会启动 因为我们有了人工智能 或者有了一种更先进的方法 那这也是涌现出来的嘛 人工智能 然后大自然又启动了一轮 新的物种筛选程序 然后剩下的人类 可能就会变得不一样了 变得不一样就是因为这个 因为AI的产生导致人类变异了 啊对 这个 我觉得可能有这样一种可能啊 对我们聊的话题远了一点 OK OK 这挺有趣的 刚才回答的这个问题挺好的 我们还举了一些例子 伊隆马斯克的例子 对然后呢 然后还有一个呢 我其实我在你在这个内容里面 我们 我们刚才不是聊到了这个变异嘛 个体的变异 然后呢 其实你的整个这个心法里面 还有最后一个心法 就是涌现 嗯啊 有这个印象吧 涌现嗯 涌现这个心法 就是说 由众多的小的改进叠加而成的质变 然后呢就是实现了群体智慧 或者是就是类似的智慧 因为我刚才讲的 可能是因为人类会被AI影响
(48:55) 大自然逼迫我们重新重启选择 但是呢这个里面 我们把涌现这个这样一个思维啊 它算是一种思维 放到这个现实世界中的一些决策 或者是一些 看待事情的这些角度上来看 你可以跟我们分享一下 你当时写这些心法的时候的这个 因为我记得涌现是最后一个 这个应该是比较高级的心法 现在是最后一个 是是 其实涌现如果在某种意义上来说 它有有一点点像所谓的 整体大于部分之和 他创造了一些 比原来更多的东西 这但这个多是更广义的 更多的东西 就很像我们往往 而且在某种意义上来说 如果稍微那个一点的 就是 往往暂时我们不能够特别解释的好的 大家就会用涌现来去描述 大家想 而且他看起来似乎也有点奇怪 因为如果按照熵增理论的话 就是涌现这件事 他看起来是一个特别特别小概率的事 而且它到底是一个发生在什么方向上的事情 他是因何而成
(50:07) 其实我觉得他还有很多未解之谜 但我觉得他倒是能够 能够给出一个在复杂环境 因为我们知道 它本身也是一个复杂科学的一个概念 是在一个复杂的环境里面 的我们能够给到我们很多启发 嗯但是首先呢 就像 我们知道 这个大模型的智能也是涌现出来的 是吧 他也相当多的受到了这种人类的启发 叫所谓的大力出奇迹 然后 当神经元的数量达到一定量的时候 他就涌现出来了某些智能 而且这些智能看起来因为它的这种 因为它的这种分布式 它看起来是甚至是不可解释的 并不是一个特别特别因果分明的 但它就是可以 就是挺好的 就是能用 嗯所以呢 我会觉得呢 就是他 它是一个挺好的 对于智能的一个描述的方式 那我当初在人生算法里面 就把它作为最后一个了
(51:18) 其实也会一直在想 就是作为一个人 嗯大家如果从世俗的角度来说 从世俗的成功学的角度 我们都希望能够创造出某些价值 那如果小一点的一个 就可以这么去说吧 比如说举个例子 假如我开一家咖啡馆 然后我是做一个小本生意 我开10家咖啡馆 基本上它对应的是十家咖啡 我的利润原来成了十倍 然后我的成本 我的成本可能因为规模效应 我就降低了一点 然后我可能有十二倍的利润 然后这个时候有可能你再到了 嗯开了1,000家咖啡馆 你突然会发现 你的这个咖啡馆变成了一种生活方式 或变成了人们不可或缺的一些东西 然后你变成了星巴克 你就会发现 你获得了另外一个价值 因为我们 知道假如星巴克的 公司的市值拆到咖啡馆的话 对应每一家咖啡馆的价值 是一个很吓人的 值这么多钱吗 其实好像不值那么多钱 嗯当然我们可以用这个来解释 现在特别火的这个泡泡玛特是吧 嗯就是他 为什么比如说举个例子 这个
(52:31) 我大概这个 去年二三月份的时候 我们有一个投资的一个小圈子 然后 是去年还是前年 如果没记错是去年3月份的时候 那个时候大概是差不多十七八块 然后我们当时说 哎呀如果到了十块 我们就all in要买 我们有一个大神就说要all in要买 然后现在就十倍都不止了 但在这种他在 当然我们这是一方面 他的股价他这么厉害 但他突然又能够大家觉得为什么他 他会有一个某一款产品 一下子全世界都特别火爆 而且可以冲破这种文化的 这种之间的区隔 能够世界联动 他那个labubu是吧 对能够突然这么火爆 他到底是如何发生的 其实像这一类的词语 你如果单纯的去用因果的方式 用商业化的方式 用艺术或怎么 其实都不够 可能多多少少 要加入一些涌现的概念在里面 是吧然后当然我们再回到 回到个体来说也是一样 那么我们总是希望呢 我们能够去做一些什么样的事情 这些事情呢 他有这种某种涌现的可能性
(53:41) 涌现有点像生命的中大奖啊 就是我觉得大模型就有点像AI从业者 突然中了 一个大奖 而且这个大奖是大户 他们超乎他们想象的 比如像辛顿 他之前其实肯定还从来没有想到 在他的有生之年能够见到这些东西 他都自己都没有这么乐观 所以涌现就有点像突如其来的礼物 但他极有可能也不来 就有点像AI寒冬的时候 很多年是吧 当年是吧 说马上过10年 我记得香农当时说过10年就可以 至少下棋可以下过 结果过了好几十年 才在国际象棋上能够AI赢过人类 嗯所以呢 再回过头来说 我想呢对于个人来说呢 其实无非是这样 就说我自己就会这么去想 还是回到现实的一个角度 就是 我们做选择做决策 可能分作两种 一种呢叫做 叫做保本式的 保本的部分就是我们得守住他的底线 这一部分 更多的是用这种所谓的底线思维 比如从理财的角度 是吧你得首先你的本金别损失了 比如说你和人际的交往的过程中 你得与人友善 但是你得有自己的底线
(54:51) 你得有自己的边界 否则的话 假如你没有底线的话呃 生活永远会把你锤到你能够接受的 最低的底线的那一块 对 生活是找软柿子捏的 是吧嗯 所以这是叫底线思维 你得守卫啊这些底线 但另外一方面呢 其实就是一个 另外一头呢 其实大概可能在我们的生活中间 占个10% 或者5% 或者有1%也挺好的 当然像Elon Musk这种疯子 他是99%是这种 放到这个 这一部分就是 去做一些 赔率特别特别大 但是胜率是未知的 我觉得涌现这种事就特别像 赔率极其极其大 但是呢胜率 不可知 嗯所以这类事情 我们生命当中也有一部分 可以投入到这些事情上 嗯就有点像 说到这儿 一个小小的例子就让我想起了 是在纽约吗 是有一个摄影师是吧 他一直给别人做保姆 然后呢他的业余时间 他就去拍照 然后一直到他离世之后 然后 大概是这个意思吧 然后他的作品出来 然后震惊了整个世界 所以我就觉得
(56:03) 这个过程也特别像他平时的这一张 积累啊 对他就他 可能就是说有一个核心的点 就是整体大于个体嘛 涌现有个特性就是这样子 整体大于个体 而且如果说对于复杂系统来说 就是它更高层次 上面的是下面这一层次 可能不理解上面这层次是什么 就是说 它的价值已经超出个体的所有价值了 没错你看人工智能 或者我们大脑 一个脑细胞 是吧 他也不知道自己该 他的作用就是说 和周围所有脑细胞产生连接 这是他这是他生存的意义 他的全身就他的 他的他的一切 就是这个 但是呢 在整体的大脑这个整体组织上 它竟然涌现出来了智能 对就像现在我们大语言模型也是一样 涌现出来的智能 而且它的什么推理啊 翻译啊总结摘要都很厉害 所以说我觉得这个 这特特别好玩的一种自然现象 这也是一种我们 其实我们前面最早讲到的就是复杂系统嘛 我们一开始讲的第一个话题 就是关于这个 我们现在这个 我们这个时代的决策不是线性决策 不是逻辑因果决策 而是一个概率决策 是复杂系统内的导航和决策 所以说 涌现永远是跟复杂系统关联在一块
(57:11) 是吧然后一个复杂系统呢 一定是网络化的 对他有非常多的这个触角 和周围产生连接嘛 啊对 一定是网络化的 那么你刚才总结的就是 我们人生其实要看待这个 这个怎么投资 或者做人生的选择的时候 要放一部分小概率的这个事情 放在复杂系统里面去 就是搏他的这个涌现出来的这一次机会 哈哈这我们做投资都知道 就是我以前 我以前做社交网络 也算是参与了 就是我们一下就涌现出来了一个微博 是吧 啊是 然后现在呢 嗯做投资 比如说你像Nvidia也是的 大家长期投他的人 他每次都能够在这么复杂的 这个环境里面 找到自己的生存机会 然后现在他又成了算力和人工智能的 之前是跑挖矿啊 然后是显卡呀 对对对是是是 对这个 其实他之前算不到 每张卡 其实差不多干的事情是一样的计算吧 但是他没错 因为网络方式不一样 组合方式用途不一样 他产生了完全不一样的价值 导致他这个公司价值越来越高 对所以我觉得嗯 这个挺好玩的 这个而且我们
(58:18) 我这是对 这个我 我把前面顺道给总结了一下 顺道给总结了一下 对OK OK 那么 这里就我想想看我们还有多长时间 我们聊了 唉快一个小时了 对 充其量我们一般都会聊70分钟之内 OK 那最后还有两个小问题啊 我们可以比较快一点 就是我给你想了一个问题 因为如果说 把你之前的这个人生算法压缩 压缩成了3行代码啊 压缩一下 对会写什么 最简单的 就大家怎么执行我的人生算法 就是我要写一个最简单的人生 人生引导程序吧 是吧 有一个比较优质的一个人生引导程序 你觉得应该 用什么样的代码来执行它 这个你怎么说都可以 无所谓对 其实那个人生算法 它本身这个标题啊 就是这个名字啊 他其实是有一点点怪的 叫所谓有一点点怪是啥呢 因为人生是一个充满不确定性的算法 在某种意义上来说 就是我们是希望他是可重复的 是吧 他可以有一些自动化的成分在里面
(59:28) 嗯所以这两个东西在一起 它本身就有点矛盾 是吧它它它本身就有点矛盾 所以当然有点矛盾 所以它凑在一起就会有点张力 就是所以呢 就是嗯 如果与他最近的这个描述了 就我们刚才也说到 就所谓的演化算法是变异选择 然后去放大 其实他的基本逻辑 好多事啊 就不管是商业上的事 比如像经济创意 比如像生物的演化 像好多东西 或者像小孩子的教育 很多时候基本上都是 都是底层这个东西都是差不多的 都是这三步 变异,选择和大规模的复制是吧 在复制过程中 有的有机会嗯 有的有机会嗯 去创造出来一些不一样的东西 涌现出来东西 有些人可能就就很平平常常一生 或者有些人嗯 去比别人多挣点钱啊等等 就复制的比较好 或者是运气比较好 嗯但是但是呢 就我自己呢 就是在嗯那个在写这个呃 说人生算法的时候呢 其实是讲的一个个体
(1:00:37) 做一个每一个个体的生命 嗯 其实它的一个核心词大概是啥意思呢 其实就是说人和人之间的本质而言 差别并不是那么大 嗯嗯 比如说举个例子 那就是最高的人 也不会比我们达不到我们身高的两倍 是吧最聪明的人 可能智商也达不到我们的两倍 跑得最快的人呢 也比我们跑不了我们的两倍那么快 对吧 对所以其实人很接近 嗯但是呢 从结果来说 差别很大 人和人对差别特别大 那到底是为什么 所以我就把它用了点一半成功学 一半这种 各种各种学科交织在一起的一个解释 其实在某种意义上来说 单纯的个体 在单个时间 单个片段 单个区间之内 它的差别其实挺小的 那么有些人能够有比较大的成就 或者有一个比较好的一个人生的实现 但并不是每个人都要如此啊 就是有些人之所以能够这样的话 就是因为我觉得差别最大的一点
(1:01:45) 有些人是有自己的人生算法的 有些人呢 是没有的 所以呢嗯 那么到底是包括哪些了 就比如说 举个例就像你刚才说到你 你有一些很好的一些习惯 你的好奇心 你的学习 然后你的对自己的输出价值的这种 这种好的习惯 我觉得这个这些 可能也是在某种意义上 也是算法的一部分 但是可能每个人不一样 比如有些人他可能就是比较懒散 有些人可能就是比较自由 嗯但我觉得 其实人生算法在本质上意义所说的是 嗯每个人呢 能够去找到自己最独特的 最独一无二的内核到底是什么 其实我也在找 我也在想 到目前为止也没有想特别明白 嗯就像你说的 你想留下一些有价值的东西 有痕迹的东西 那么我到底是什么样的东西是有价值 有独一无二 这是一个持续的探寻的过程 那么那这是一个 就是找到自己的这个内核的这部分 嗯而且内核的这部分一定嗯
(1:02:53) 你的答案一定是交织着理性和感性 从理性的角度 你知道这件事是可为的 有价值的 从感性的角度来说 你知道这件事你愿意 你可能折腾一辈子 你都会觉得挺有乐趣的 哪怕没有任何成果 你也会乐在其中 嗯这是一个 说白了就有点像 又回到了哲学上的第一个问题 就是发现你自己啊 其实对于我们找到自己的内核 嗯然后呢 我们把这个内核呢 假如是一个数值的话 我在写那个人生算法的时候 我就篡改了那个 那个 爱因斯坦的相对论的e等于MC的平方 那么内核的这部分其实对应的就是 m就是就正好又匹配上了 嗯当然了 就是后面的时候 你即使找到这个内核了 你其实是需要通过大规模的复制 让它去创造价值 它有很多种复制的方式 可能你创造了一个企业 可能你用你的钱去生钱 可能你用通过劳动时间的重复 可能你通过成立公司雇佣了很多人 然后放大这个效应 但总之它的一个核心词就是复制
(1:03:59) 然后 我们继续回到爱因斯坦的这个方程 就e等于mc的平方 然后其实我把那个平方呢 就变成了c的n次方 那就是你要重复很多很多次 所以呢 其实到最后人生算法的一个本质就是 我想了也许是两个 就一个呢 是找到你真正的内核 你的有优势有价值 你愿意干很久 而且这个事情怎么折腾呢 你也不疲惫的事 第二呢 你如何想办法去有效的高效的 大规模的去复制它 这种复制 你还可以借助于某些杠杆 借助于技术的杠杆 借助于资金的杠杆 等等等等 所以到最后 整个人称算法的一个核心就是这样的 一个e等于m c的n次方 其实在某种意义上 它也就是一个复利公式 也是构成了一个指数的 一个这样的东西 就是这样的 这个 对我补充一下我自己的想法 对这个的看法 因为我觉得呢 就回到最开始你刚刚说我的一个特点 我自己 刚才我脑子里面也回想了一下 我觉得我还是我 做事情还是蛮蛮任性的 就是我还是比较凭自己的价值观 来判断东西的 对我并没有做这个事情 有很多事情哎
(1:05:10) 这个事情你 你能赚很多钱去做 或者这个事情你能够怎么怎么样 你能够升职去做 但是我脑子里面可能会有一种价值观 可能这种价值观呢 也是我自己学习 或者是一种 或者是一种 就像一种审美一样 就是我觉得有 有时审美更广 更广阔的来定义它 可能会和价值判断有关系 然后呢我觉得这个事情它有价值 而且 我就会顺着这个价值 我会发现 怎么说呢 我身上有什么特点 我一直去追求这个价值 哪怕我去分享 我去做一些事情 嗯我在我平时是在工作中 在做产品的时候 会往这个方向去靠近 对如果我在分享内容的时候 我也会往这个方向去靠近 然后另外一个 你去做创业或者做投资的时候 但是我后面我离开微博 我就没怎么创业了 我就做投资 孵化项目为主 我孵化的项目也在 我找的人挺奇怪的 就是他挺执着的这个人 他挺执着的 他有一个理想在里面 就是这个 这个是个内核 就是你说的这个内核 这个内核有的有的人可能很不靠谱 或者说但是看上去挺不靠谱的 但是呢你投资吧 你总是小概率事情 有时候这个小概率 碰到了这个不靠谱的内核 但这个内核又很特别
(1:06:22) 它能够让一些事情得到放大 对啊 对就是我 以前我 我找这个钱百万的那个 就是李佳琦的老板 这个创始人他很特别 他有一种就是一种执着 不知道为什么他有一种执着在里面 就是这个事情 就能让他渡过后面很多难关 然后一直能够走到后面 这个事情 就是这是个小概率事情 这是非常稀有的 包括李佳琪本身 也是一个很稀有的一个事情 所以说我觉得呢 大家就是 我觉得这把放到你的算法里面 这个就是第一个就是变异 变异的那么一点点 就是值得守护的东西 如果说我能够顺着这个东西 这个变异的东西 我去往前走 你坚持它就好了 当然很多人可能是没有出头之路 或者一般他一碰到这个环境啊 产生变化的时候 你这个变异就有特点了 嗯有特点 然后你就加紧时间上杠杆 嗯 对吧对 就把这个东西放大啊 就是我们现在也是 如果说我 我觉得这个价值 我们现在做的内容 我觉得现在AI这个时代 就是自己的这个审美啊
(1:07:29) 执着啊或者说你自己认为 在这个内容挖掘上面有价值 那我们就做呗 就用AI放大它 让AI在你的指挥下来生产 你觉得就是你 你想去像日更你的内容一样 或者我经常在录节目 或者我想做点什么产品一样 我就用AI AI来放大杠杆 AI放大好了之后呢 我效率高了之后 我再用资本放大杠杆 你再融资嘛 其实我觉得对 这是我的一个总结啊 对把你的这个话题延伸一下 对非常好 OK OK OK啊就是比较实际一点 一个总结 真实case 那么当我们最后一个话题 一般我们节目最后都有一个话题 就是很快AGI了 我前面说了嘛 可能要不了多少年的 大家都预测2027年 2028年或者2030年 对吧 各个大佬们的预测都不一样 但是差不多也就四五年 然后呢我觉得在都很乐观 对对对 而且都说了用强化学习就能实现AGI RL(Reinforcement learning) 这个已经在业界得了共识了 嗯这是时间问题了 然后那我们如果AGI到来的时候 我们应该如何设计自己新的算法 就是我们如果说在那个时代 有一个AGI到来的时候
(1:08:41) 我们有新的人生 或者说我们应该启动 你可以最简单的方式来 来阐述一下我们AGI到来的时候 对我觉得就是 我觉得就是两件事 主要是两个事 其实基本的架构还是没变 基本架构是两头 就一头是关于生存的话题 一头是关于人生价值和意义的话题 生存的话题来说 AGI来了之后呢 它一定是对人类的价值一个 巨大的摧毁 对于个人的财富的再次分配啊等等 我觉得它一定是一个非常 非常巨大的一个摧毁 嗯所以呢 可能作为个体来说呢 而且 我觉得 他会面对的一个特别要命的一件事呢 就是会消灭中间商 就是过往的各种技术的进步 它会形成一个产业链是吧 就是会形成一个漫长的产业链 中间大家都一起来分这个蛋糕 那现在这个这一波的 你会发现它是消灭中间商的 其实这个大者通吃 吃的特别狠 嗯所以呢 对于个体来说 就是面对这件事 可能就比如说你必须要参与 嗯你得在这个公司里面 你得有点他们的股票啊 或者有点相关的透支啊 否则的话我觉得会很麻烦 他的掠夺性会更厉害
(1:09:54) 就是我觉得这个技术的进步的这一波 可能会然后呢 他的 然后呢再一个呢 就是可能好的公司是吧 然后还有一些什么样的东西 到底会是稀缺的 是吧比如说你买比特币是吧 你说我投资了点黄金 嗯嗯 但比特币也挺好啊 然后嗯 就是它怎么会变得是什么样的 你或者说比如说举个例子 像这个Indigo是吧 这个搞投资挣了钱 他可能就想去度假是吧 那就像我在一些稀有的 一些地方 就是一些 我认为是人类的终极类旅游目的地 地方我可能会投资一些旅游型的 租金收益特别好的这种物业 就你得做一些这种 假如会来临的时候 他到底会发生什么 我就 我觉得做一些生存方面的一些应对 否则的话 可能会我对他的这个 对于工作岗位的摧毁这件事 我其实是挺悲观的 就是我之前的 就我这段时间还稍微适应了一下 就是那个ChatGPT的那个Pro版的 o1Pro
(1:11:02) 是吧那个 那个嗯 那个出来的时候 我当时的一个强烈的感受 因为我原来在嗯 我在退休之前 我在广州那家公司 差不多几百号人 公司有五六百号人 我当时一个感慨 天呐我们这么多人加起来 也许我们这个 真的这一个大模型 能够顶一个部门干的事 顶几十号人啊 就是真的 其实是一个无法想象的 所以我觉得他对这种岗位的一个摧毁 是非常非常要命的 所以这是从这一头啊 就是我觉得 就是必须还要有一个防守的心态 就是假如这个事发生了之后 我不觉得AGI来了之后 大家都可以去躺平 即使AI想干这件事 但AI的老板也不同意干这件事 所以我觉得这个是属于痴心妄想了 就全人类共同富裕这件事 我觉得对 就想的太多了 是吧想的太画饼太多了 对对对实实在在画饼 是吧谁去那个 那首先你们说这些 说这些话的人 你们这些鼓吹这些的公司的老板 你应该第一步做的事 先把股票分给大家 是吧分给我们普通老百姓 就这个挺难的 但另外一方面来说了 的确了就是他也会推动着我们向着
(1:12:13) 就追寻更大的意义 来去探寻 那也许他暗示的一个小小的一个机遇 就在于与众不同的个体 他可能他的一个价值会呈现出来 就有点像可能未来从机会上来说 要么特别大的公司 要么特别特别小的个体 独一无二的个体 所谓的一人企业 我觉得可能会有所机遇 再往上一点的可能就是 哲学层面的 艺术层面的 或者是甚至是 宗教层面的探索了就是 对OK 这个你刚刚说了 对职业我说我非常认同 前上一周吧 那个AXIOS那个AXIOS那个 一个政治媒体啊对 他以前是political的人出来做的 他写了篇文章 挺轰动的 当时在Twitter上面 是在英文圈里面 他就写这个叫做什么血洗白领 这个接下来这个标题叫做血洗白领 这是他说现在 我觉得他现在 他说现在 现在的政策 现在就够了 不用AGI 其实根本就不用AGI 现在(血)洗(白领)都可以 是吧真的不用AGI 现在就够了 真的现在就够了哈哈哈
(1:13:24) 对他说现在的 现在的模型公司 科技公司和政府都在隐瞒这个事情 或者 他们没有意识到这个事情的严重性 但是这个肯定有人标题党 但是我觉得呢 一定会的 我觉得我感觉 我说一下 我的感觉是我肯定没有你那么悲观啊 对就是说我说他肯定是要血洗的 一定要血洗的 所有的变革嘛 你想想看 电力出来的时候 发明的时候 美国经济大萧条 工业革命 那都和这个科技革命有关嘛 但是AI可能这一次会超过电力对吧 这个他是对智力的替代 不是体力替代 然后这一次他会大量的岗位也会失业 然后社会会有巨变 然后呢人类 但是一个适应力很强的一个种族 只要地球没完 我们肯定可以玩 对只要地球还在哈 然后呢所以说 我们可能会经历过非常大的动乱 然后国家解体啊 什么都会有可能产生 企业然后呢 但是呢我们一定能走过去 我们一定能够等到ASI ASI诞生的时候 我们一定会有一个光明的一个未来 但是那个时候 人类可能会减少好多人口了 可能会 可能会减少一些人口 没事啊这个我只是猜想 然后呢你刚刚说那个 在这个时候应该做的事情 其实第一个就是 你知道这个科技巨头和这个科技进步
(1:14:38) 会有多大的破坏力 那你肯定是 如果说口袋还有钱 你肯定是 是吧这个请收下我的钱吧 对哈哈哈 对吧这个是投资 这是第一个 这是作为smart决策 对吧这个是挺重要的 我之前写过一篇文章叫数字化转型 我就是说投两种 是吧要投 投比特是吧 可以被无限复制的东西啊 那个时候 那个时候还没有AI 我是2021年还是2022年写的 AI没那么发达 就是我投比特 可以无限复制的东西 就比特币啊 类似的数字化的东西 或者软件公司科技 然后呢第二个呢 嗯现在有了智能之后了呢 智能也是因为比特产生的嘛 是吧这个运行的 所以说你肯定要投基于比特的智能啊 这个东西 然后呢 如果是大家一切都自动化了之后 那么其实任何东西都被软件化了 任何公司都可以叫做即插即用的 提供智能 你干什么都可以啊 你去买就好了 所以说治理已经没什么意义了 在那个时候 对对 就是就是 就是这样一个未来 那么 那么其实过度过了这个期限之后 如果(AGI)真的能够产生 很大的这个 这个社会进步啊 或者效率提升 那确实是能够产生很多财富 或者说是
(1:15:49) 他之前可能你要雇很多人 才能产生这么同样的财富 但是现在不用雇那么多人了 你也能产生这么多财富 那么剩下的这个钱 就应该分给你之前要雇的那些人 至少政府应该有这样的杠杆机制 那么大部分人 可能每天只用工作两个小时 大部分时间我可以创造 我可以发挥想象力啊 我可以做一点 是吧这个代表我自己的事情 那么大家都在做 这个代表我自己的事情的时候 那人类的这个社会的创意程度 会进到下一个台阶 我们可能又能涌现出 意想不到的东西出来 意想不到的需求 意想不到的想法 我们现在根本想不到的 那可能就是我们度过这个困难期之后 一个光明的未来啊 想象的啊 哈哈一个光明的未来 到时候人类可能会重新去定义闲暇 重新去定义 对对对 去定义一些什么样的东西 对就是以前那个刘慈欣写三体嘛 不是说之前抵抗啊 大家都变成一个客空的社会 然后没办法 反正我就放弃抵抗了 我们就一块happy吧 然后世界其实发展得更好 对不对嗯嗯对 大概是这样的一个逻辑 我是这样想的 所以说在这人生目标啊 这个在AGI时代设计人生目标 我觉得还是就是 分成两个 一个是投资目标 我刚才已经讲了
(1:16:59) 第二个人生自己的目标 就是寻找自己的意义 最重要的 嗯是 就是创造 做你自己认为坚持要做的事情 有理想就是 不是要理想 就是你认为对的事情 你感兴趣的事情 坚持下去 然后 能够形成一个很好的一个共同圈层 到时候我觉得是这样子的一个 所以我现在我自己来尝试 我就干这个 给大家科普嘛 是吧给大家科普 然后呢形成一个网络 大家一块 我们来共同 当时我觉得共同迎接这个AGI 迎接这个奇点的到来 干这个领航员的这个工作 好吧那我们今天就非常感谢老喻 参加我这次对谈 其实老喻分享了很多这个有趣的 这个想法 之前的好吧 欢迎我们下一次能够再聊 好吧谢谢大家 谢谢indigo 再见嗯拜拜