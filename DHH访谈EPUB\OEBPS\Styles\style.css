/* EPUB样式表 */

body {
    font-family: "Times New Roman", "SimSun", serif;
    font-size: 1.1em;
    line-height: 1.6;
    margin: 0;
    padding: 1em;
    color: #333;
    background-color: #fff;
}

h1 {
    font-size: 2.2em;
    color: #2c3e50;
    text-align: center;
    margin: 1.5em 0;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5em;
}

h2 {
    font-size: 1.8em;
    color: #34495e;
    margin: 1.2em 0 0.8em 0;
    border-left: 4px solid #3498db;
    padding-left: 1em;
}

h3 {
    font-size: 1.4em;
    color: #2c3e50;
    margin: 1em 0 0.6em 0;
}

p {
    margin: 0.8em 0;
    text-align: justify;
    text-indent: 2em;
}

.speaker {
    font-weight: bold;
    color: #e74c3c;
    margin-top: 1em;
}

.dialogue {
    margin-left: 1em;
    margin-bottom: 1em;
}

.chapter-intro {
    font-style: italic;
    color: #7f8c8d;
    background-color: #ecf0f1;
    padding: 1em;
    margin: 1em 0;
    border-left: 4px solid #95a5a6;
}

.metadata {
    font-size: 0.9em;
    color: #7f8c8d;
    text-align: center;
    margin: 2em 0;
}

.page-break {
    page-break-before: always;
}

/* 对话格式 */
.conversation {
    margin: 1em 0;
}

.conversation p {
    text-indent: 0;
}

/* 代码块样式 */
code {
    font-family: "Courier New", monospace;
    background-color: #f8f9fa;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.9em;
}

pre {
    background-color: #f8f9fa;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    margin: 1em 0;
}

/* 引用样式 */
blockquote {
    margin: 1em 2em;
    padding: 0.5em 1em;
    border-left: 4px solid #bdc3c7;
    background-color: #f9f9f9;
    font-style: italic;
}

/* 目录样式 */
.toc {
    list-style: none;
    padding: 0;
}

.toc li {
    margin: 0.5em 0;
    padding: 0.3em 0;
    border-bottom: 1px dotted #bdc3c7;
}

.toc a {
    text-decoration: none;
    color: #2c3e50;
    display: block;
}

.toc a:hover {
    color: #3498db;
}

/* 章节标题页 */
.chapter-title-page {
    text-align: center;
    margin: 3em 0;
}

.chapter-number {
    font-size: 1.2em;
    color: #7f8c8d;
    margin-bottom: 0.5em;
}

.chapter-title {
    font-size: 2em;
    color: #2c3e50;
    margin-bottom: 1em;
}
