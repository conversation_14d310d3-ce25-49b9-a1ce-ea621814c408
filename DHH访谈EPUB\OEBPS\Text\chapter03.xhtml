<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第三章：AI与编程的未来</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第03章</div>
        <h1 class="chapter-title">第三章：AI与编程的未来</h1>
    </div>
    
    <div class="chapter-intro">
        <p>人工智能正在改变编程的面貌，但DHH对此有着独特而深刻的见解。他既拥抱AI作为编程助手，又坚持认为程序员必须保持对代码的直接控制。本章探讨了AI如何影响编程实践，以及程序员如何在AI时代保持技能和创造力。</p>
    </div>

    <div class="conversation">
        <p><strong>Lex：</strong> 我喜欢我们如此多地谈论Ruby以及什么是美丽的代码和什么是美丽的编程语言。所以我认为在你那里的描述中暗示的也许你明确表达的事情之一是Ruby是动态类型与严格类型。你不仅说这是一件好事，而且你会为动态类型辩护到死。</p>

        <p>就像那种自由是一种强大的自由来保护。</p>

        <p><strong>DHH：</strong> 这是使Ruby成为Ruby的本质。这就是为什么我不完全理解当人们呼吁Ruby添加静态类型时。因为对我来说，这是这是什么的基石。为什么你想把最美丽的语言之一变成更丑陋的东西？这是我对静态类型的主要反对意见之一。</p>

        <p>这不仅仅是它在某些方面限制了你。它使元编程更难。我写了很多元编程。我见过在TypeScript中做元编程需要什么。这实际上是真正让我开始撕裂或让TypeScript退出我参与的一些项目的事情之一。我们从turbo中拉出了TypeScript。</p>

        <p>我们拥有的前端框架之一，因为我试图在TypeScript中编写元编程，我只是被激怒了。我不想要那种体验，但我也不想从美学的角度。我讨厌重复。我们刚刚谈论了我多么喜欢Ruby将所有这些表达式煮沸到其本质。你不能删除一个点。</p>

        <p>你不能删除一个字符而不失去一些东西。这一刻你去静态类型，你声明，至少我知道有方法做隐含类型等等。但让我们只是采用一个例子的刻板案例，例如。大写U，用户，我声明变量的类型；小写用户，我现在命名我的变量；等于大写用户或新大写用户。我重复了用户三次。</p>

        <p>我没有时间做这个。我没有这个的敏感性。我不想让我的Ruby被这个污染。现在我理解人们喜欢静态类型的所有论点，当主要论点是它使工具更容易。例如，它使在编辑器中做自动完成更容易。</p>

        <p>它使找到某些类型的错误更容易，因为也许你调用对象上不存在的方法，编辑器实际上可以在你甚至运行它之前捕获那个错误。我不在乎。首先，我不用工具写代码。我用文本编辑器写它们。我用我的赤手从屏幕上凿出它们。我不自动完成。这就是为什么我如此喜欢Ruby，这就是为什么我继续爱上文本编辑器而不是IDE。</p>

        <p>我不想要IDE。我希望我的手指必须单独输入它的每个元素，因为它会强迫我留在Ruby美丽的世界中。因为一旦输入大量样板变得容易，好吧，猜猜什么？你可以有很多样板。基本上每种具有出色工具支持的语言都对样板有更高的容忍度，因为思维是，好吧，你无论如何都不会输入它，你只是自动完成它。</p>

        <p>我根本不想要那个。我想要我正在工作的织物，它只是一个文本文件。没有其他的。所以这些东西一起发挥作用。有美学部分，有工具部分，有元编程部分。有Ruby的鸭子类型精神的事实，我不知道你以前是否听过那个术语。</p>

        <p>它本质上不是关于，我可以调用这个方法，如果对象是某个类的。它是，如果方法响应，我可以调用这个方法吗？在这方面，它非常来自Smalltalk。你实际上不检查那个类是否有方法，这允许你在运行时动态添加方法并做各种真正有趣的事情，这些事情支撑了我们在Ruby中做的所有美丽的元编程。我不想失去任何那个。</p>

        <p>我不在乎好处。我一遍又一遍看到吹捧的好处之一是编写正确的软件要容易得多。你可以有更少的错误。你会有更少的空指针异常。你会有更少的所有这些东西。是的，我没有任何那个。这只是在我的标准操作模式中不会发生的事情。</p>

        <p>我不是说我没有错误，当然我有，但我用单元测试，用集成测试捕获那些错误。这些是会捕获逻辑错误的预防措施。编译但错误的东西以及不可编译的东西。所以我从来没有被吸引到这个世界，部分原因是我在某类系统上工作。</p>

        <p>我完全接受那个。如果你正在编写有500万、1000万、5000万行代码的系统，有数百、数千或数万程序员，我完全接受你需要不同的方法。我反对的是为1000万行代码的代码库和10万程序员工作的正确的想法也是我应该在我的卧室中使用来创建Basecamp的同样的东西，因为我只是一个个人。那完全是胡说八道。</p>

        <p>在现实世界中，我们会知道那根本没有意义。你不会，我不知道，用你的Pagani去Costco买杂货。这是一个糟糕的车辆。它只是没有空间。你不想弄脏美丽的座位。你不想做任何那些事情。</p>

        <p>我们知道在某些领域非常好的某些东西不适用于所有。在编程语言中，似乎我们忘记了那个。现在，公平地说，我也可能有一点忘记那个的声誉。当我第一次学习Ruby时，我如此疯狂地爱上了这种编程语言，以至于我几乎发现任何人会选择任何其他编程语言来编写Web应用程序是不可想象的。</p>

        <p>我有点以那种精神参与Ruby on Rails的传道，作为一个十字军，我只需要教你福音。我只需要向你展示我们刚才谈论的这个条件代码，你会在尖锐论点的点上转换。现在，我了解到那不是方式。它不是方式的部分原因是程序员思考不同。</p>

        <p>我们的大脑配置不同。我的大脑完美地配置为Ruby。完美地为我可以用文本编辑器凿出代码的动态鸭子类型语言。其他人需要IDE的安全性。他们想要除非你调用它上面的方法否则不会编译的类的安全性。</p>

        <p>我已经接受了那个，但大多数程序员没有。他们仍然陷入，本质上，我喜欢静态类型。因此，静态类型是创建可靠正确系统的唯一方法，这只是如此令人震惊，坦率地说，面对证据，相反的证据山，说这是愚蠢的事情。</p>

        <p>这是我如此爱上Shopify作为Ruby on Rails的旗舰应用程序的原因之一。Shopify存在于大多数程序员永远不会触及的规模。在黑色星期五，我认为Shopify做了每秒100万个请求。那不是100万个图像请求。那是通过商业管道漏斗的动态请求。我的意思是，Shopify运行大约30%的所有电子商务商店在该死的互联网上。</p>

        <p>所有商业的巨大部分通过Shopify运行，那运行在Ruby on Rails上。所以Ruby on Rails能够扩展到那个水平，而不在它所做的所有事情中使用静态类型。现在我知道他们在某些方面做了某些实验，因为他们正在达到你会用动态类型达到的一些限制。</p>

        <p>你用动态类型达到的一些限制实际上，顺便说一下，只是当你写500万行代码时你达到的限制。我认为Shopify单体是大约500万行代码。在那个规模上，一切都破坏，因为你在人类能够用编程语言做什么的前沿。</p>

        <p>部分差异是Ruby是如此简洁的语言，那些500万，如果它们已经用，让我们说Go或Java写，会是5000万或2500万。现在那可能已经缓解了当你在有许多程序员的巨大系统上工作时你有的一些问题。但它肯定也会复合它们试图理解2500万行代码。</p>

        <p><strong>Lex：</strong> 所以这个东西确实扩展。这是一个持续的神话，它不扩展Shopify和其他人。但Shopify认为一个很好的例子。顺便说一下，我喜欢Shopify，我喜欢Tobi，太棒了。</p>

        <p><strong>DHH：</strong> 你必须让Tobi上。</p>

        <p><strong>Lex：</strong> 是的，当然。</p>

        <p><strong>DHH：</strong> 今天早上刚和他说话。</p>

        <p><strong>Lex：</strong> 当然，他很聪明。我有机会在沙漠的某个地方和他一起出去玩，我忘记了在犹他州。</p>

        <p>他只是一个聪明的人。Shopify，shopify.com/lex一直在支持这个播客最长时间。我不认为实际上Tobi知道他们赞助这个播客。我的意思是这是一个大公司，对吧？</p>

        <p><strong>DHH：</strong> 这是一个巨大的公司。我认为不到10,000名员工，市值1200亿，每季度GMV四分之一万亿。</p>

        <p><strong>Lex：</strong> 他参与细节。</p>

        <p><strong>DHH：</strong> 他是，非常如此。关于Tobi的有趣故事。Tobi在2000年代中期在Rails核心团队。Tobi自己写了Active Merchant，这是创建商店的框架之一。他写了Shopify今天仍在使用的Liquid模板语言。</p>

        <p>他对Rails生态系统有巨大的贡献列表，他是公司的CEO。是的，我认为这只是...这对我来说非常鼓舞人心，因为它与我喜欢做的事情完全相反。我喜欢一天中大部分时间用我自己的手凿代码。他经营一家几乎10,000人的公司，字面上像世界商业依赖于它。我甚至无法开始理解的关键性水平。</p>

        <p>然而我们可以在计算机科学和程序开发中的许多这些基本问题上看到眼对眼。那是一个动态范围，能够包含Rails成为刚刚开始有想法的一个开发者的伟大工具，他们甚至不完全知道一切。</p>

        <p>谁正好在PHP在那些90年代末会是一个好选择的水平，因为是的，我可能可以上传一些东西到FTP服务器，等等。Rails确实比那有更多复杂性，但它也有如此长的跑道。跑道一直到该死的Shopify。那是我可以为我们可以做很多的动态范围做的最有说服力的论点。</p>

        <p>即使说了那个，Shopify当然是异常值。当我写Rails时，我不把Shopify作为主要目标。我想到单个开发者。实际上，我确实想到Shopify。但我不想到现在的Shopify。我想到当Tobi写Snow Devil时的Shopify，那是第一个销售滑雪板的电子商务商店，他创建的，那是前Shopify的Shopify。</p>

        <p>他完全自己创建的。那是可能的，因为Ruby on Rails不仅仅是关于美丽的代码。它同样是关于生产力。它同样是关于个人程序员能够产生的影响。他们可以构建他们可以在头脑中保持整个事情并能够推进它的系统，这样你可以从一个开发者坐着工作某些东西，那个东西是Shopify，它变成今天的样子。</p>

        <p>当我们谈论编程语言并比较它们时，我们经常在非常晚的阶段比较它们。比如什么是更好的编程语言，让我们说2009年的Twitter，当它已经是一个巨大的成功。Twitter开始于Ruby on Rails。然后他们遇到了一些扩展问题。当时这是一个大争议。</p>

        <p>然后他们最终我认为用其他语言重写它，顺便说一下，我认为这是Ruby on Rails有史以来最好的广告，因为在他们切换后的10年里什么他妈的都没有发生，对吧？本质上零创新。其中一些是因为他们在做长时间的转换，所有早期成功部分来自于他们有敏捷性快速改变和采用等等。</p>

        <p>这就是初创公司需要的。这就是Shopify需要的。这就是Twitter需要的。这就是每个人都需要的。这是Ruby on Rails的第一优先级。确保我们不失去那个。因为当开发工具和编程语言由大公司驱动时经常发生的是它们反映他们的组织结构图。</p>

        <p>React和使用它需要的其他一切在某些方面是Meta如何构建Facebook的反映。当然是这样。因为当然这是那个的抽象。我不是说React不是一个伟大的工具，不能被较小的团队使用。当然可以。但它诞生在与Ruby on Rails这样的东西非常不同的背景中。</p>

        <p><strong>Lex：</strong> 让我说这个小旁白，因为我认为我们可能会回到Shopify并经常庆祝它。只是一种个人笔记。这个特定的播客有比我可能拥有的更多的赞助商和想成为赞助商的赞助商。对我来说真的，真的很重要的是不在乎，并能够庆祝人们。就像我庆祝人们。</p>

        <p>我庆祝公司。我不在乎他们在赞助。我真的不在乎。我只是想让那个非常明确，因为我们将继续说关于Shopify的积极事情。我不在乎。停止赞助。这对我来说真的不重要。但是，是的，我只是想让那个明确。</p>

        <p>所以，但要在Twitter和Shopify的扩展事情上停留，你能向我解释Shopify用YJIT做什么吗？他们必须尝试做什么来扩展这个东西？因为那是一个令人难以置信的故事，对吧？</p>

        <p><strong>DHH：</strong> 是的，所以Shopify对整个Ruby生态系统，不仅仅是Rails，特别是Rails做出的伟大贡献之一是YJIT。</p>

        <p>所以YJIT是他们的Ruby编译器。这只是让一切更有效率，在Shopify规模上，即使在Ruby的开销和执行时间上挤出5%、10%的改进也是一个巨大的交易。现在，Shopify不需要YJIT。Shopify已经在Ruby的初始版本上运行，我认为比我们今天拥有的慢10倍。</p>

        <p>如果你回顾Ruby 186，Tobi可能开始的，就像我开始的，那足以推动Shopify到今天的规模。扩展对话中的很多在区分两件事的失败中丢失了。规模是我们谈论的一种包装，当里面真的有多个包装时。</p>

        <p>一个是运行时性能，延迟。你能多快执行单个请求？它能发生得足够快，用户不会注意到吗？如果你的Rails请求需要一秒半执行，用户会注意到。你的应用会感觉缓慢和迟钝。</p>

        <p>你必须将响应时间降低到，让我们说至少300毫秒以下。我喜欢以100毫秒作为我的延迟目标。那是一种性能。你能从单个CPU核心中挤出多少那种延迟的性能，这告诉你单个请求的价格是什么。</p>

        <p>但然后你是否能处理像Shopify现在正在做的每秒100万个请求。如果你有一个可以每秒做一千个请求的盒子，你只需要X个盒子来达到一百万。你实际上会发现的是，当涉及编程语言时，它们在这方面都是一样的。它们都在很大程度上水平扩展得很好。你只是添加更多盒子。</p>

        <p>扩展Shopify的困难部分通常不是编程语言。是数据库。这实际上是Shopify现在面临的挑战之一，你如何在他们运营的规模上处理MySQL？你什么时候需要转移到其他数据库来获得全球性能？所有这些事情。关于扩展Ruby的问题是经济问题。</p>

        <p>如果我们在应用服务器上花费这么多，如果我们能从Ruby中获得5%更多的性能，好吧，我们可以节省5%的那些服务器，这可以过滤到预算中。现在那个分析基本上得出一件事。Ruby是一种奢侈语言。这是一种奢侈，在我看来是最高的奢侈。</p>

        <p>它是编程语言的Coco Chanel。不是每个人都能负担得起的东西。我以最好的可能方式意味着这个。互联网上有一些应用程序，每个请求的价值如此之少，你负担不起使用像Ruby这样的奢侈语言来编程。</p>

        <p>你只是必须用C或Go或其他一些低级语言或Rust将就，谈论那里的线噪声...</p>

        <p><strong>Lex：</strong> 这就像语言的旧货店。</p>

        <p><strong>DHH：</strong> 确切地，你需要一种...你需要一个非常低的级别来做它。你负担不起使用奢侈语言来构建它。这对Shopify不是真的。即使在2004年，这对Basecamp也不是真的。</p>

        <p>这对99%的所有曾经创建的Web应用程序都不是真的，因为99%的Web应用程序的主要成本组件，不是CPU课程。是湿课程。是人类课程。是人类理解和涉及系统的能力。是他们的个人生产力。</p>

        <p>我曾经做过一次计算，当有人第400次说，"哦，如果你从Ruby切换到一些更快的语言，你可以节省一堆钱。"我计算出在那个时候，我认为我最后一次做这个计算是将近十年前。我们在Ruby应用服务上花费了大约15%的运营预算。</p>

        <p>所以为了将我的业务成本概况改善七个百分点，我必须选择快两倍的东西。这相当困难。相比之下，如果Ruby和Ruby on Rails比其他东西甚至高10%的生产力，我会移动针头得多，因为让个人程序员更有生产力实际上重要得多。这就是为什么人们对AI如此兴奋。</p>

        <p>这就是为什么他们对硅谷一个年薪30万美元的程序员现在可以做三个或五个人的工作这一事实感到恐慌，至少在理论上。我实际上还没有在实践中完全看到那个，但让我们假设理论是正确的，如果不是现在，那么在六个月内。那是一个巨大的交易。当涉及这些类型的业务应用程序时，这比你是否能从CPU中挤出更多周期重要得多。</p>

        <p>如果你正在制作像你有的Tim Sweeney这样的虚幻引擎渲染东西。是的，他需要真正出汗所有那些细节。Nanite引擎不能在Ruby上运行。它永远不会。它不是为那个而意味着，好的。这些类型的业务应用程序绝对可以，人们现在对AI兴奋的一切，那个额外的能力只是做更多。</p>

        <p>这就是为什么我们在2000年代初对Ruby感到兴奋。那是因为我看到如果我们甚至能从人类程序员中挤出10%的改进，我们就能够用更少的钱做更多的事情。</p>

        <p><strong>Lex：</strong> 可能会争论这个，但我真的喜欢与AI一起工作，与AI合作。我会争论你想要AI生成的代码类型是人类可读的，人类可解释的。</p>

        <p>如果它生成Perl高尔夫代码，那不是合作。所以它必须说人类。这不仅仅是你用英语写提示。你也想用像Ruby这样的人类可解释语言阅读响应，对吧？所以这实际上对AI也是有益的。因为你有点说对你这个雕塑家，那种精英主义的Coco Chanel雕塑家，你想在你的花哨键盘上用你自己的手指输入每一个字母。</p>

        <p>但也是Ruby的好处也适用于一旦其中一些由AI编写，你实际上用你自己的手指做编辑。因为你可以与它互动，因为它是人类可解释的。</p>

        <p><strong>DHH：</strong> 我真正喜欢的范式是埃隆实际上在你们谈论Neuralink时在你的一个节目中说的，Neuralink允许你和机器之间的带宽增加。语言，无论是口语还是书面语，都是非常低带宽的。如果你要计算我们坐在这里时可以交换多少位，它非常慢。Ruby有更高的通信带宽，揭示了每个字符传达比大多数其他编程语言更多的概念。</p>

        <p>所以当你与AI合作时，你想要真正高的带宽。你希望它能够与你一起产生程序，无论你是否让它写代码，你们两个都能真正快速理解，你可以将一个宏大的概念，一个宏大的系统压缩成你们两个都能理解的更少的部分。现在，我实际上也喜欢与AI合作。</p>

        <p>我喜欢凿我的代码，我使用AI的方式是在一个单独的窗口中。我不让它驱动我的代码。我试过那个。我试过光标和风面，我不喜欢那种写作方式。我不喜欢那种写作方式的原因之一是我可以字面上感觉到能力从我的手指中流失。与材料的那种即时性水平消失了。</p>

        <p>我感受到这个最多的地方是我做了这个叫做Omakub的Ubuntu混音，当我切换到Linux时，它全部用Bash写的。我以前从来没有用Bash写过任何严重数量的代码。所以我使用AI合作，与我一起写一堆Bash，因为我需要所有这些。我知道我想要什么。</p>

        <p>我可以用Ruby表达它。但我认为通过Bash过滤是一个有趣的挑战，因为我正在做的是设置Linux机器。这基本上是Bash设计的目的。这是一个很好的约束。但我发现自己做的是一遍又一遍地要求AI以同样的方式表达条件，例如，在Bash中。通过不输入它，我没有学习它。</p>

        <p>我在使用它，我得到了我想要的表达，但我没有学习它。我有点害怕。我有点害怕，这是学习的终结吗？如果我不输入，我不再学习吗？我为我重新塑造的方式是，我不想放弃AI。作为程序员查找API，对某些东西获得第二意见，做草稿，这是如此更好的体验。但我必须自己做输入，因为你用手指学习。</p>

        <p>如果你正在学习如何弹吉他，你可以看尽可能多的YouTube视频。你不会学会吉他。你必须把手指放在弦上才能真正学习动作。我认为这里有一个与编程的平行，编程必须部分通过实际输入来学习。</p>

        <p><strong>Lex：</strong> 我只是真的，这很迷人。听着，我大脑的一部分100%同意你。部分不同意。我认为AI应该在学习的循环中。现在当前系统不这样做，但我认为光标说，基本上强迫你输入某些东西是非常可能的。所以如果你设置学习模式，我不想放弃AI。</p>

        <p>我认为氛围编码是一种技能。所以对于有经验的程序员来说，将氛围编码作为一种东西来解雇太容易了。</p>

        <p><strong>DHH：</strong> 我同意，我不会解雇它。</p>

        <p><strong>Lex：</strong> 但我认为你需要开始建立那种技能，开始弄清楚如何防止能力从你的手指和大脑中滑走。</p>

        <p>就像你如何与其他技能并行发展那种技能？我不知道。我认为这是一个迷人的谜题。我知道太多真正强大的程序员只是有点避免AI，因为它目前有点太愚蠢。</p>

        <p><strong>DHH：</strong> 是的，它有点太慢。这实际上是我的主要问题。它在某些方面有点太愚蠢，但在其他方面有点太慢。</p>

        <p>当我使用Claude的代码，Claude的终端版本，这实际上是我使用它的首选方式时，我变得太不耐烦。感觉就像我回到了代码必须编译的时代，我必须去做其他事情。在代码编译时煮一些茶。好吧，我在Ruby中工作了20年。我不再有编译等待。所以有那个方面。</p>

        <p>但我认为对我来说更关键的方面是我真的关心能力。我见过即使是伟大的程序员一旦他们放下键盘会发生什么。因为即使在AI之前，这会在人们得到提升时立即发生。大多数在大企业工作的伟大程序员停止每天写代码，因为他们只是有太多会议要参加。他们有太多其他事情要做。他们不可避免地失去与编程的联系。</p>

        <p>这并不意味着他们忘记一切。但如果你没有把手指放在酱汁，源代码中，你会失去与它的联系。没有其他方法。我不想要那个，因为我太享受它了。这不仅仅是关于结果。</p>

        <p>这是理解编程对喜欢编码的程序员来说至关重要的，不仅仅是关于他们从中得到的程序。那可能是经济价值。这不是唯一的人类价值。人类价值在表达中同样多。当有人坐下来弹吉他并演奏"天堂阶梯"时，有一个完美的录音将永远持续。你可以只是把它放在Spotify上。你实际上不需要做它。快乐是自己指挥吉他。</p>

        <p>程序员的快乐，我作为程序员的快乐，是自己输入代码。如果我提升自己，如果我将自己提升出编程，我将自己变成项目经理。如我前几天写的，AI乌鸦谋杀的项目经理。我可能在我的整个职业生涯中成为项目经理。如果我不关心自己写代码，我可能在20年前成为项目经理。我只是想要结果。</p>

        <p>这就是我如何开始编程的。我只是想要结果。然后我爱上了编程，现在我宁愿退休也不愿放弃它。现在这并不意味着你不能拥有你的蛋糕并需要它。我做了一些氛围编码，我不在乎我没有自己演奏。我只是想看到一些东西。我脑海中有一个想法。我想看到一些东西。那很好。</p>

        <p>我也整天使用AI。事实上，我已经到了如果你把它从我这里拿走，我会想，哦，我的上帝，我们甚至如何在互联网上查找东西了？Stack Overflow还在吗？或者我仍然是一个东西？就像我如何甚至找到我整天都有的一些这些问题的答案。</p>

        <p>我不想放弃AI。事实上，我会说我喜欢使用AI的方式，我因为AI变得更聪明。因为我使用AI让它向我解释事情。甚至愚蠢的问题。我会有点尴尬甚至输入到谷歌。AI完全愿意给我一些Unix命令的ELI5解释。我应该已经知道，但我不知道，对不起，你能向我解释一下吗，现在我知道这个东西。</p>

        <p>所以在我整天与AI工作的一天结束时，我聪明了一点。像5%，对不起，不是5%，也许半个百分点。这随着时间的推移而复合。但我也看到，当我在Omakub项目上工作并试图让AI为我驱动时，我感觉在一天结束时我可能愚蠢了半个百分点。</p>

        <p><strong>Lex：</strong> 好吧，你说了很多有趣的事情。</p>

        <p>首先，让我们从你问愚蠢问题的事实开始。如果你去Stack Overflow问一个愚蠢的问题或阅读别人的愚蠢问题和答案，那里有很多判断。AI有时过度地没有判断。它通常说，哦，那是一个很好的问题。</p>

        <p><strong>DHH：</strong> 过度了。</p>

        <p><strong>Lex：</strong> 是的，哦，那很棒。是的，我的意思是，它如此有利于学习。它是如此美妙的学习工具，我也会想念它。它是一个很好的基本搜索引擎，进入特定编程语言的各种细微差别，特别是如果你不太了解它或像你可以加载文档的API。它对学习如此伟大。对我个人来说...</p>

        <p>我的意思是，在快乐量表上，它让我更兴奋地编程。我不知道那确切是什么。部分是，我真的很抱歉。Stack Overflow是一个令人难以置信的网站，但那里有负面性。那里有判断。与我旁边的炒作人一起出去很兴奋，只是说，是的，那是一个很好的想法。我会说，不，那是错误的。</p>

        <p>我会纠正AI，AI会说，"你绝对正确。我怎么没想到那个？"你知道，重写代码。我想，神圣的狗屎，我正在有，那就像一个伙伴。那就像真的积极，非常聪明，挑战我思考。即使我从不使用它生成的代码，我已经是一个更好的程序员。</p>

        <p>但实际上更深的事情是出于某种原因我有更多乐趣。那是一个真的，真的重要的事情。</p>

        <p><strong>DHH：</strong> 我喜欢把它想象成一个配对程序员，正是因为那个原因。配对编程在2000年代变得时髦。你会有两个程序员在一台机器前，你会在你们之间推键盘。一个程序员会驾驶，他们会输入。</p>

        <p>另一个程序员基本上会坐着看代码，建议改进，查找一些东西。那是一个真的有趣的动态。现在不幸的是，我是一个内向的人，所以我可以做大约五分钟，然后我想从桥上跳下去。所以它不适合我作为全职职业。</p>

        <p>但AI允许我一直拥有那种体验的所有最好的。现在我认为真的有趣的是，你说的关于它让它更有趣。我实际上没有想过那个，但它对我更有趣的是再次成为初学者。它让第一次成功学习Bash更有趣。</p>

        <p>现在我必须做绕道，我让它为我写所有代码，我意识到我没有学习近我希望的那么多，我开始做一旦我自己输入它。但它给了我信心，你知道什么？如果我需要自己做一些iOS编程，我可能六年没有做过，那是我最后一次涉足它。我从来没有真正为真实构建任何东西。</p>

        <p>我现在非常有信心，我可以与AI坐下来，我可以在本周末在应用商店中有一些东西。如果我没有像AI这样的配对编程伙伴，我不会有那种信心。我实际上不经常为Ruby代码使用它。每当我尝试它时，我偶尔会印象深刻，他们就像，哦，它得到了这一件事。这真的很了不起，它实际上相当不错。</p>

        <p>然后我会再问你两个问题，我会想，哦，是的，好吧。如果你是我的初级程序员，我会开始敲我的手指并说，你必须振作起来。现在当然伟大的事情是我们可以只是等五分钟。Anthropic CEO似乎认为到年底90%的所有代码将由AI编写。</p>

        <p>我对此有点怀疑，但我对编程可能变成手动完成时的马的前景持开放态度。我们娱乐性地做的事情不再是在洛杉矶四处走动的交通方式。你不会鞍马并去杂货店从Whole Foods的鞍袋中拿东西。那只是不再是一个东西。</p>

        <p>那可能是编程的未来，完全可能的手动编程。我也不在乎。就像即使我们有所有最好歌曲的伟大演绎，正如我所说，有数百万人喜欢弹吉他。它可能不再具有曾经的经济价值。我认为我相当确信这是真的，我们也许已经看到了顶峰。</p>

        <p>现在我理解悖论，当某些东西的价格下降时，实际上总体使用量上升，对那个活动的总支出上升。那也可能发生。但我们现在看到的是很多大商店，很多大公司不像五年前那样招聘。</p>

        <p>他们不预期他们会需要更多的程序员。有争议的是，Tobi实际上在Shopify内部发出了一份备忘录，要求每个考虑雇佣某人的人问这个问题，这能由AI完成吗？现在，他在这个问题上比我更超前。</p>

        <p>我看一些编码战壕，我想，我很想更多地使用AI，我看到它如何让我们更有生产力，但它还没有达到我只是说，哦，我们有这个项目的水平。让我只是把它给AI代理，它会去做它。</p>

        <p><strong>Lex：</strong> 但让我们诚实一点。</p>

        <p>你就像一个克林特·伊斯特伍德类型的角色牛仔，骑着马看到汽车四处行驶，你就像，好吧。</p>

        <p><strong>DHH：</strong> 那是其中的一部分。我认为那是，拥有那种谦逊是重要的，你擅长的可能不再是社会重视的。这在历史上发生了一百万次，你可能在制作马鞍方面异常出色，例如。那是很多人过去关心的事情，因为每个人都骑马。</p>

        <p>然后突然骑马变成了这个小众爱好，有些人关心它，但远没有那么多。那没关系。现在这个的另一件事是我有幸...我已经是程序员将近30年了。那是一个很好的运行。我试图以这种方式看待生活，我已经被祝福了几十年的经济上可行的，高价值的方式，将我在工作世界中最喜欢的东西翻译成写Ruby代码。那是如此有价值，我可以做数百万美元。</p>

        <p>如果那明天结束，我不应该带着遗憾看待那个。我应该带着感激看待它。</p>

        <p><strong>Lex：</strong> 但你也是一个高度有经验，聪明和有主见的人，所以得到你对马的未来的意见真的很有趣，因为它，你知道，有很多年轻人听这个，他们喜欢编程或对用软件，用Ruby on Rails，那种语言构建东西的可能性感到兴奋。这和现在的可能性。</p>

        <p><strong>DHH：</strong> 但这是一个职业吗？</p>

        <p><strong>Lex：</strong> 这是一个职业吗？</p>

        <p>如果确实一个人可以在AI的帮助下构建越来越多的东西，就像他们如何学习那种技能？这是一个好技能学习吗？我的意思是，对我来说，这是真正的谜团，因为我认为你仍然绝对必须从头学习如何编程是真的。但你如何平衡那两种技能？因为我也，当我现在思考时，有一种可怕的技能滑走发生。</p>

        <p>在特定代码片段上真的几分钟的事情。这很可怕。不是驾驶的方式，你知道，当你有一辆车为你驾驶时不会那么快滑走。所以那真的让我害怕。所以当有人来找我问我如何学习编程时，我不知道建议是什么，因为我认为仅仅使用光标或copilot生成代码是不够的。</p>

        <p><strong>DHH：</strong> 绝对不够。如果你想学习，你们都不想变得更好。如果你只是成为一个敲击猴子，也许你在第二个是有生产力的。但然后你必须意识到，好吧，任何人都可以敲击吗？如果我们所做的只是整天坐着敲击？是的，是的，是的，是的，是的。那不是一个可销售的技能。</p>

        <p>现在，我总是为自己和当我对其他人说话时加上这个前缀，这是规则第一号，没有人他妈的知道任何事情。没有人可以预测甚至六个月前。现在，我们可能处于AI未来炒作的顶峰，因为我们看到所有的承诺，因为其中很多是真实的，很多人自己经历过它。</p>

        <p>这个令人震惊的事情，硅以某种方式思考，感觉eerily让人想起人类。我实际上会说对我来说大事甚至不是ChatGPT。甚至不是Claude。是DeepSeek。在本地运行DeepSeek并看到思考框，它与自己对话关于如何制定响应。</p>

        <p>我几乎想想，这是一个噱头吗？它是为了我的利益而做这个表演吗？但那实际上不是它如何思考的。如果这是它实际如何思考的，好吧，我有点害怕。这是令人难以置信的人类，它以这种方式思考。但那去哪里？所以在95年，我最喜欢的电影之一，我最喜欢的B级电影之一出来了，"割草机人"。</p>

        <p><strong>Lex：</strong> 伟大的电影。</p>

        <p><strong>DHH：</strong> 关于虚拟现实的令人难以置信的电影。成为化身并生活在VR中，就像故事是一团糟。但美学世界建立起来是令人难以置信的。我想，我们五年后。我现在要生活在VR中。我只是要漂浮。我要成为化身。这是大多数人类可以度过一天中大部分时间的地方，那没有发生。我们30年后，VR仍然不在这里。</p>

        <p>它在这里用于游戏。它在这里用于一些专门的应用程序。我最大的孩子喜欢玩Gorilla Tag。我不知道你是否试过那个。那基本上是最热门的VR游戏。美妙，它很棒。预测未来真的很难，因为我们只是不知道。然后当你考虑AI时，你甚至有最聪明的人说，我不认为我们完全理解这如何工作。</p>

        <p><strong>Lex：</strong> 但然后另一方面，你有摩尔定律，似乎已经工作了很多，很多，很多年，减少晶体管的大小，例如。所以就像你知道，Flash没有接管互联网，但摩尔定律工作了。所以我们不知道AI是哪一个。</p>

        <p><strong>DHH：</strong> 哪一个。这就是我也发现如此迷人的。</p>

        <p>我忘记了谁做了这个演示，但网络社区中的某人，关于飞机历史的这个伟大演示。所以你从莱特兄弟飞行，什么？1903年或类似的东西。40年后你有喷气式飞行。在四十年中令人难以置信的进步量。然后在56年，我认为是，波音747本质上前身的停止标志被设计，基本上从那时起什么都没有发生。只是自50年代以来飞行体验的小调整和改进。</p>

        <p>不知何故，如果你要预测飞行会去哪里，你坐在42年，你会看到，你会记得莱特兄弟在03年飞行，你看到喷气发动机来了，你就像，我们要在另外两十年内飞到星星。</p>

        <p>我们要发明超级巨型超音速飞行，将在两小时内穿越地球。然后那没有发生。它耗尽了。这就是预测未来如此困难的原因。我们可以在那一刻如此兴奋，因为我们在图表上通过早期点画一条线，它看起来像那些早期点只是向右上升，有时它只是平坦化。</p>

        <p>这也是我们有如此多关键基础设施的事情之一，例如，仍然在COBOL上运行。世界上大约五个人真正理解，真正深入地，有很多，社会可能失去能力。它仍然需要，因为它在追逐未来。COBOL仍然与我们在一起。这是我想到编程的事情之一。</p>

        <p>Ruby on Rails现在处于这样的水平，50年后，极有可能仍然有大量Ruby on Rail系统在运行。很难预测那个确切的世界会是什么样子。但昨天的天气告诉我们，如果仍然有来自70年代的COBOL代码今天运行社会保障，我们还没有找到一个干净的方法来转换那个，更不用说理解它，我们当然应该对预测未来谦逊。</p>

        <p>我不认为在70年代写那个COBOL代码的任何程序员有任何该死的想法，在2025年，支票仍然从他们当时编码的业务逻辑中切出。但那只是让我得出关于年轻程序员应该做什么的问题的结论？你不会能够预测未来。没有人会能够预测未来。</p>

        <p>如果你喜欢编程，你应该学习编程。现在，那会永远是一个职业吗？我不知道，但什么会永远是一个职业？谁知道？就像一秒钟前，我们认为是蓝领劳动将首先被提取。是机器人将接管。</p>

        <p>然后GenAI出来，然后所有艺术家突然看起来像，"神圣的狗屎，这现在要做所有动画吗？现在要做所有音乐吗？"他们变得真的害怕。现在我看到最新的特斯拉机器人说，哦，也许我们现在回到蓝领有麻烦。因为如果它可以那样跳舞，它可能可以修理厕所。</p>

        <p>所以没有人知道任何事情，你必须然后为未来定位自己，以这样的方式，你选择一个职业或道路，如果结果是你必须重新工具和重新技能，你不会后悔你采取的道路，这不重要。那是一个一般的生活原则。</p>

        <p>对我来说，我如何看待我涉及自己的所有努力是我想对所有结果感到满意。当我们开始在37signals上工作一个新产品时，我为其成功设置我的心理模型。我说，你知道什么？如果没有人想要这个，我将有另一个机会写美丽的Ruby代码，探索Greenfield域，学习新的东西，构建我想要的系统，即使没有其他人想要它。多么祝福。</p>

        <p>多么特权。如果一堆人想要它，那很棒。我们可以支付一些薪水，我们可以保持业务运行，如果这是一个巨大的成功，美妙。我可以影响一堆人。</p>

        <p><strong>Lex：</strong> 我认为对我来说一个大的开放问题是你可以用氛围编码走多远，是否年轻开发者投资大部分时间到氛围编码或从头写代码的方法。</p>

        <p>所以氛围编码，意思是，所以我有点倾向于模因，但氛围编码，意思是你生成代码，你有这个你想创建的东西的想法，你生成代码，然后你用自然语言到提示和手动修复它。你学习足够手动修复它。所以那是学习过程。</p>

        <p>你如何修复生成的代码，或者你从头写代码并让LMS有点标签，标签，标签，标签，添加额外的代码。就像你倾向于哪一部分？我认为为了安全，你应该在两者中找到美丽和艺术和技能，从头开始。所以应该有你的时间的某个百分比只是从头写，某个百分比氛围编码。</p>

        <p><strong>DHH：</strong> 应该有更多时间从头写。</p>

        <p>如果你有兴趣学习如何编程，不幸的是，你不会通过看健身视频变得健康。你不会通过看YouTube吉他视频学会弹吉他。你必须实际自己演奏。你必须做仰卧起坐。编程，理解，学习几乎任何东西都需要你做。</p>

        <p>人类不是为了通过只是从远处观看其他人来吸收信息，以转化为技能的方式而建造的。现在讽刺的是，似乎AI实际上相当擅长那个，但人类不是。如果你想学习如何成为一个有能力的程序员，你必须编程。理解这真的不那么困难。现在我理解诱惑。</p>

        <p>诱惑在那里，因为氛围编码可以产生东西。也许在这一刻，特别是在你不熟悉的新领域，用你不完全了解的工具，那比你能做的更好。或者你会花更长时间到达，但你不会学到任何东西。</p>

        <p>你会以这种肤浅的方式学习，感觉像学习，但完全是空卡路里。其次，如果你可以只是氛围编码它，你不是程序员。然后任何人都可以做它，这可能是美妙的。那本质上是Access数据库发生的事情。那是Excel发生的事情。它采取了会计师成为软件开发者的能力，因为工具变得如此可访问，他们可以为业务下周如何做构建模型。那在Excel之前需要程序员。现在它没有，因为他们可以自己做它</p>

        <p>通过编码使非程序员能够以我发现绝对美妙的方式探索他们的想法。但它不会让你成为程序员。</p>

        <p><strong>Lex：</strong> 我同意你，但我想为我们两个都错了留出空间。例如，可能有，氛围编码实际上可能是一种技能。</p>

        <p>如果你训练它，通过氛围编码，让我们包括纠正步骤，迭代纠正。如果你真的擅长那个，你可能超越从头开始运行的人，你可以想出真正创新的东西，特别是在历史的这一刻。虽然LLM有点太愚蠢，无法创建超级新颖的东西和完整的产品，但它们开始接近那个。</p>

        <p>所以如果你现在投资时间成为一个真正好的氛围编码器，也许这是正确的事情，如果它确实是一种技能，我们有点模因关于氛围编码，就像坐回去，它在名字中。但如果你认真对待它，一个竞争性的氛围编码器并擅长骑AI的浪潮并擅长编辑代码与从头写代码的技能，你可能实际上可以在长期走得更远。</p>

        <p>也许编辑是与从头写作根本不同的任务，如果你认真对待那作为你发展的技能。对我来说那是一个开放的问题。我只是想，我个人，现在你在另一个水平，但只是我，只是个人，我不如编辑我没有写的代码。</p>

        <p><strong>DHH：</strong> 没有人是。</p>

        <p><strong>Lex：</strong> 那是一个不同。</p>

        <p><strong>DHH：</strong> 这一代没有人是，但也许那是一种技能。</p>

        <p><strong>Lex：</strong> 也许如果你与AI在同一页面上，因为AI有一致性。它真的像一个有一致风格和结构等等的配对程序员。加上你自己的提示，你可以控制你写的代码类型。我的意思是，它通常可能是一种技能。</p>

        <p><strong>DHH：</strong> 那是提示工程师的梦想。我认为这是完全的白日梦。</p>

        <p>我不认为存在不擅长写作的编辑。我写了很多书。我有很多专业编辑。不是所有人都写了自己的伟大书籍，但所有人在某种方面都是伟大的作家。如果你不知道如何做，你不能给某人指针。如果编辑不能自己制作解决方案，编辑很难能够发现问题的错误。</p>

        <p>在我看来，编辑是奖励，成为好编辑的能力是你从成为好实干家得到的奖励。你必须首先是实干家。现在那与说氛围编码，提示工程不会能够很快产生完全形成的令人惊叹的系统不同。我认为那完全可能，但然后没有技能留下，这也许是最大的回报。</p>

        <p>那不是AI的整个承诺吗，它只是所有自然语言，甚至我笨拙的制定问题的方式可能导致美丽，简洁的答案。那对我来说实际上是一个更吸引人的愿景，将有这些特殊的提示工程巫师，他们知道如何恰好挠AI来产生他们想要的。</p>

        <p>AI的美丽是认为不知道AI实际如何工作的第一件事的人能够制定他们的想法和他们想要的愿望，AI可以以某种方式采取那个混乱的想法团块并产生某人想要的东西。那实际上是编程一直是什么。</p>

        <p>经常有不知道如何编程的人，想要程序，然后雇佣程序员，给他们他们想要的混乱描述，然后当程序员交付那个回来时说，"哦，不，实际上那不是我的意思。我想要别的东西。"AI可能能够提供那个循环。</p>

        <p>如果那发生到最大程度，是的，不会有那么多程序员，对吧？但希望大概某人仍然，至少在可预见的未来，必须理解AI产生的是否实际工作。</p>

        <p><strong>Lex：</strong> 作为一个有趣的案例研究，也许一个思想实验。</p>

        <p>如果我想氛围编码Basecamp或HEY，以及你建造的一些产品，就像瓶颈是什么？我会在路上哪里失败？</p>

        <p><strong>DHH：</strong> 我在尝试这样做时看到的，尝试使用氛围编码构建真实的东西是你实际上很早就失败了。氛围编码能够在当前时刻构建看起来像它工作的东西的表面，对吧？但它在各种方式上有缺陷。</p>

        <p>有明显的方式，模因方式，它泄漏你所有的API密钥，它以纯文本存储你的密码。我认为那最终是可解决的。就像它会弄清楚那个，或者至少它会在那个上变得更好，但它在自己的迷宫中迷失的能力现在非常大。</p>

        <p>它编码某些东西，然后你想改变某些东西，它变成打地鼠游戏。真的很快，Pieter Levels一直在做这个美妙的飞行模拟器正在谈论那个，在某个规模上，东西只是继续咬自己的尾巴。你想修复某些东西，它破坏五个其他东西，我认为实际上是独特的人类，因为那是大多数糟糕程序员的方式。</p>

        <p>在某个复杂性水平的领域，他们不能修复一件事而不破坏三个其他事情。所以在那种方式中，我实际上在某种程度上，这几乎是AI将弄清楚这个的积极信号，因为它现在已经完成了一个极其人类的轨迹。它正在犯的错误类型是初级程序员一直犯的错误类型。</p>
    </div>
</body>
</html>