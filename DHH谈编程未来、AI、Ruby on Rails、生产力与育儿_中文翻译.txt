# DHH谈编程未来、AI、Ruby on Rails、生产力与育儿

**原文来源：** DHH: Future of Programming, AI, Ruby on Rails, Productivity & Parenting | Lex Fridman Podcast #474
**原文链接：** https://www.youtube.com/watch?v=vagyIcmIGOQ

---

## 引言

没有任何一个认真对待这个问题的人相信Cookie横幅对任何人有任何好处，但我们却无法摆脱它。这就是Cookie横幅真正让我恼火的地方。这不仅仅是欧盟的问题，而是整个世界的问题。你在这个星球上的任何地方都无法躲避Cookie横幅。

如果你乘坐埃隆的火箭去到该死的火星，试图访问一个网页，你仍然会看到Cookie横幅。宇宙中没有人能逃脱这种荒谬的东西。有时感觉我们的处境并没有好多少。网页与90年代末、2000年代初的网页并没有太大不同。它们仍然只是表单，仍然只是写入数据库。

我认为很多人对这样一个事实感到非常不舒服：他们本质上就是CRUD（增删改查）猴子。他们只是制作创建、读取、更新或删除数据库行的系统，他们必须通过过度复杂化来补偿这种存在主义的恐惧。驾驶赛车的巨大满足感很大一部分来自于在附着力边缘驾驶，正如我们所说的。

在那里，你基本上只需要一个微小的动作就会失控。不需要太多，然后汽车就开始旋转。一旦它开始旋转，你就失去了抓地力，你就会撞墙。这种危险与技巧的平衡是如此令人陶醉。

以下是与大卫·海涅迈尔·汉森（David Heinemeier Hansson，也被称为DHH）的对话。他是编程和技术世界的传奇人物，才华横溢且富有洞察力，有时颇具争议，但总是很有趣。他是Ruby on Rails的创造者，这是一个影响深远的Web开发框架，支撑着数百万人使用的许多网站，包括Shopify、GitHub和Airbnb。

他是37signals的联合所有者和首席技术官，该公司创建了Basecamp、HEY和ONCE。他与合著者杰森·弗里德（Jason Fried）一起是《纽约时报》畅销书作者，著有四本书：《重新工作》、《远程工作》、《真实起步》和《工作不必疯狂》。除此之外，他还是一名赛车手，包括在传奇的勒芒24小时耐力赛中获得组别冠军。

这是Lex Fridman播客。为了支持它，请查看描述中的赞助商，并考虑订阅这个频道。现在，亲爱的朋友们，这里是DHH。

## DHH的编程学习之路

**Lex：** 对于一个成为传奇程序员的人来说，你正式开始编程的时间相对较晚，我想这是因为你曾经几次尝试学习编程但都失败了。你能告诉我你学习编程失败的完整故事吗？Commodore 64有参与其中吗？

**DHH：** Commodore 64是我的灵感来源。我真的很想要一台Commodore 64。那是我第一次坐在电脑前。我坐在它前面的方式是，我五岁的时候，我们街上有一个孩子有一台Commodore 64。

没有其他人有电脑，所以我们所有的孩子都会去那里，我们都在玩"Yie Ar Kung-Fu"。我不知道你是否见过那个游戏。它是最早的格斗游戏之一，真的是一个很棒的游戏。我五岁时第一次玩那个游戏。

我们大约有七个孩子坐在这个孩子的卧室里，轮流玩游戏。我觉得这非常有趣。我求了又求，求我爸爸，我能得到一台电脑吗？他终于回家了，说："我给你买了电脑。"我想，太好了，我自己的Commodore 64。然后他拿出这个黑绿蓝色的键盘。

那是一台Amstrad 464。我说，爸爸，这是什么？

**Lex：** 失望啊。

**DHH：** 这不是Commodore 64。但它是一台电脑。所以我在六岁时得到了我的第一台电脑，那台Amstrad 464。当然，我想做的第一件事就是玩电子游戏。

我想这台电脑，顺便说一下，他用电视和立体声录音机之类的东西换来的，带有大约两个游戏。一个是这个"Frogger"游戏，你必须从地下逃脱。实际上有点黑暗。就像这只青蛙，你试图让它从地下出来。我玩得很糟糕。我只有那两个游戏。

然后我想要更多游戏。当你是一个没有很多钱的孩子时，获得更多游戏的一种方法，我不能只是买一堆游戏，就是自己输入它们。在84年、85年，杂志会在杂志的后面印刷源代码，你可以坐下来输入它。所以我试图这样做，将这个游戏输入到Amstrad中需要大约两个小时。

当然，我会在途中犯一些拼写错误，有些东西不会工作。我的英语不是很好。我出生在丹麦。所以我真的很努力地想进入其中，因为我想要所有这些游戏。我没有钱买它们。

我努力了很长时间，但它从来没有点击。然后我发现了盗版的魔力。在那之后，我基本上暂停了学习编程，因为现在我突然可以访问各种游戏。所以那是第一次尝试，大约在六、七岁的时候。有趣的是，我记得这些片段。

我记得不理解变量的目的。如果有一个东西，你给它分配了什么，为什么你要给它分配另一个东西？出于某种原因，我理解常量。常量对我来说是有意义的，但变量不是。然后也许我11到12岁，我已经进入了Amiga。

顺便说一下，Amiga仍然可能是我有史以来最喜欢的电脑。我的意思是，这是那些你变老了就会说"哦，80年代的音乐太棒了"的事情之一。对我来说，即使作为一个热爱电脑、热爱新电脑的人，Amiga是这台由生产Commodore 64的同一家公司制造的神奇机器。我在87年得到了Amiga 500。

**Lex：** 看看这个性感的东西，那是一台性感的机器。

**DHH：** 顺便说一下，这来自一个计算机不像现在这样全球化的时代。不同的地区有不同的流行电脑。Amiga在欧洲真的很受欢迎，但据我了解，它在美国根本不受欢迎。它在日本也不受欢迎。

只是有不同的机器。Apple II在美国是一个大事件。我在80年代的哥本哈根甚至从未听说过苹果。但Amiga 500是让我想再次尝试的机器。你知道什么有趣吗？我想再次尝试的原因是我记得第一次学习。

然后有这种编程语言，字面上叫做EasyAMOS，就像AMOS的简易版本。我想，如果它是EasyAMOS，能有多难？我应该能够弄清楚这一点。这次我更努力地尝试了。我进入了条件语句，我进入了循环，我进入了所有这些东西，但我仍然做不到。

在第二次尝试中，我真的到了这样的地步，也许我不够聪明，也许编程对我来说不合适，也许它太多数学了。我喜欢数学，但只是表面上的。我不像我的一些可能稍微更书呆子的朋友那样深入地喜欢它，我对他们有极大的尊重。我不是那个会弄清楚一切的数学极客。

所以在用EasyAMOS尝试失败后，甚至没有完成一个非常基本的游戏，我想编程对我来说不合适。我必须做别的事情。我仍然热爱电脑。我仍然热爱电子游戏。实际上在那个时候，我已经开始与知道如何编程的人交朋友，他们甚至不是在编程EasyAMOS。他们在编程该死的汇编语言。

我会坐下来说，你怎么做，移动和内存和复制，你甚至怎么做这个？我甚至不明白你如何从这个到Amiga演示，例如。那是Amiga的大事。它在欧洲有这个精彩的演示场景。

这是Amiga历史上一个非常有趣的时期，你有所有这些程序员主要分布在欧洲各地，他们会在图形竞赛中竞争，你可能会带来其中一个不同的...

**Lex：** 在那个东西上？

**DHH：** 在这个东西上，他们会制作这些小的几乎像音乐视频的东西，结合一些midi音乐，结合一些酷炫的图形。他们会在4K中完成所有这些，四千字节。

那不是4K分辨率，而是四千字节的内存。我只是觉得那是一个如此酷的场景。这显然是互联网之前的时代。它甚至在某种程度上是BBS（电子公告板系统）之前的时代。你通过邮寄磁盘与其他人交换你的演示软件，就像3.5英寸软盘。我被整个场景迷住了。

我被他们能够创造的东西迷住了，我只是想成为其中的一部分，即使我没有任何技能可以贡献。这就是我如何进入运行BBS的。我那时没有学习编程，直到很久以后，直到我快20岁时才学会编程。

电子公告板系统存在于这个有趣的空间中，它们部分是为演示场景提供服务，允许所有这些演示组织分发他们令人惊叹的演示。然后它也是一个交易盗版软件的地方。我在14岁时在哥本哈根我的小卧室里开始了其中一个。我有我的，在那时，Amiga 4000。我有三条电话线进入我的小房间。

**Lex：** 不错。

**DHH：** 这很有趣，因为我只有14岁。当我安装第三条线路时，你必须让电话公司的人来做。我得到了这个人，他只是四处看看，"这是什么？为什么一个14岁的孩子在他们的小卧室里有三条电话线？这里发生了什么？为什么所有这些调制解调器都在闪烁红色和黑色并发出有趣的声音？"

**Lex：** 你的父母知道吗？

**DHH：** 他们知道也不知道。他们知道我有电话线。他们知道我有电脑。我不认为他们真正理解我在交易盗版软件，这既是非法的，也是其他正在进行的事情。

**Lex：** 哦，我们应该说在欧洲，也许你可以评论一下，特别是在东欧，但总的来说在欧洲，我认为盗版比在美国更容易被接受。我不知道，也许这只是我的成长经历。

**DHH：** 甚至那种对话都不存在。我在丹麦长大时从未与任何人交谈过。

**Lex：** 盗版是错误的。

**DHH：** 对盗版有任何道德疑虑。完全接受你是一个孩子，你想要很多游戏。你没有很多钱，你怎么办？你交易。

**Lex：** 是的。

**DHH：** 有些人偶尔会买游戏。我的意思是，我曾经买了一个世嘉Master System，我买了一个游戏。因为那是我能负担得起的。我得到了"After Burner II"。我不知道你是否玩过那个游戏。在世嘉Master System上的实现相当糟糕，但它花费了大约600克朗。我当时通过送报纸赚钱。我必须做一个月才能负担得起一个游戏。

我太喜欢电子游戏了，不能等一个月才得到一个游戏。所以盗版就是你做的方式。这就是我如何进入运行这个电子公告板系统，成为演示场景的一部分，在某种程度上成为盗版场景的一部分。然后在某个时候意识到，哦，你实际上也可以在这上面赚钱，这可以资助购买更多电话线和购买更多调制解调器和购买更多Amiga。

哦，是的，那是演示聚会之一。这些是令人惊叹的事情。

**Lex：** 我在看什么？

**DHH：** 这不是很棒吗？

**Lex：** 看看所有那些CRT显示器。

**DHH：** 所有这些CRT显示器。再次，当我14岁时，我不完全理解为什么我的父母允许这样做。但我从丹麦首都哥本哈根乘火车到日德兰半岛的这个小镇奥胡斯，和一群大约十几岁末和二十多岁的家伙一起。

我14岁。我拖着我的14英寸CRT显示器和我的电脑背包去参加聚会。那就是它被称为的。那是当时最大的演示场景聚会。正如你在那张照片中看到的，成千上万的人只是排队带着他们的电脑，整天编程演示，并来回交易这些东西。

**Lex：** 这有点棒。不会撒谎，这有点荒谬。

**DHH：** 这完全棒。我以某种方式怀念它，互联网在某些方面连接了人们，但你从坐在另一个人旁边得到的连接，他有自己的CRT显示器，他拖着它穿越半个国家到达那里，这真的很特别。因为这也只是创造力的爆发。

你不断地四处奔跑。你不断地被真正擅长他们所做的事情的人包围。他们真的很擅长编程计算机。这是有感染性的。这是我当时感到的痛苦的一部分，哦，天哪，为什么我不能弄清楚这一点？我的意思是，为什么我甚至不能弄清楚EasyAMOS？这有点令人沮丧。

**Lex：** 但在你的第三次尝试中，你更成功了吗？

**DHH：** 所以第三次尝试是我开始理解的时候，这是我开始帮助构建互联网的东西的时候。所以大约在95年，我想是，或者96年，我发现了互联网。实际上，在九年级。那是我的第一次经历。我去了丹麦的某个大学。

在九年级，我们有这次远足，他们让我们坐在电脑前，电脑有Netscape Navigator，第一个版本，或者也许它甚至是那个的前身。他们有一个文本编辑器。我们这些孩子就得到了，"嘿，在互联网上建造一些东西。"

它只是HTML，你做的第一件事就是，哦，我可以通过放入这个标签并保存它来让文本闪烁。那一刻，那实际上是我重新唤醒想要学习编程的冲动的时候，因为我得到了一个积极的体验。我与编程的所有其他经历是我会花几个小时输入一些东西，我会点击运行，它不会工作。

我会得到一个对我作为一个孩子没有意义的错误消息，无论是在六岁或七岁还是在12岁。在这里，我坐在连接到互联网的电脑前，我让文本闪烁。我让它变大。我把它变成h1或h2。这些家伙在这里，"我们只是做了大约一个半小时。"

突然我想，哦，我可以为互联网制作德国的某个人能够访问和看到的东西，我不必向任何人请求许可。这太酷了。我必须做更多这样的事情。所以我进入了互联网。我开始使用HTML，我仍然有所有这些来自这些演示聚会的朋友。我开始与他们一起创建游戏网站。

我宁愿买电子游戏，我会评论它们。这是获得新电子游戏的另一种好方法，就是走到某个商店说，嘿，我是记者。我是这个15岁的孩子。他们看着你，"你是记者？"是的，我可以借一些游戏吗？因为这是游戏转移到PlayStation和其他这些东西的时候。

你不能像以前那样容易地盗版，至少一开始不能。所以我去那里，做了所有这些，这开始了我的互联网之旅。它开始在这些游戏网站上工作，与程序员一起工作，弄清楚我可以做一些事情，我可以在HTML部分工作。

这不是真正的编程，但它有点像。你在与电脑交谈。你让它在屏幕上放文本，你与世界另一端的人交流。所以这成为了我回到编程的途径。然后慢慢地我学会了越来越多。我与某人做的第一个动态网站，来自演示场景的这些程序之一是ASP.NET。

我甚至实际上没有被称为.NET。那是我们开始的。然后我们转向PHP，PHP是我终于理解的时候。当它终于点击的时候。当条件语句和循环和变量以及所有这些东西开始对我有足够的意义，我想我可以做到这一点。

## 发现Ruby的魔力

**Lex：** 所以可以公平地说，没有PHP就不会有DHH，因此你把你所有的成功都归功于PHP吗？

**DHH：** 100%这是真的。甚至比这更好，因为PHP对我来说不仅仅是在制作我自己的Web应用程序方面给了我一个开始，它实际上给了我一个标准。在许多方面，我认为开发者、Web开发者人体工程学的顶峰是90年代末的PHP，你写这个脚本，你FTP它到服务器，立即部署。立即可用。

你改变那个文件中的任何东西，你重新加载。砰，它就在那里。没有Web服务器，没有设置。只有一个运行mod PHP的Apache。它本质上是让动态网页启动和运行的最简单方法。这是我基本上在我职业生涯的其余部分一直在追求的高度之一。

在90年代中后期为互联网制作东西是如此容易。我们如何失去了允许我们不仅以这种方式工作，而且让新人进入行业给他们成功体验的敏感性，就像我有的那样。在HTML页面上添加一个该死的闪烁标签，将PHP页面FTP到Apache Web服务器，而不真正了解任何关于任何东西的知识，不了解框架，不了解设置。

所有这些东西真的把我们带到了一个地方，有时感觉我们的处境并没有好多少。网页与90年代末、2000年代初的网页并没有太大不同。它们仍然只是表单。它们仍然只是写入数据库。我认为很多人对这样一个事实感到非常不舒服：他们本质上是CRUD猴子。

他们只是制作创建、读取、更新或删除数据库行的系统，他们必须通过过度复杂化来补偿这种存在主义的恐惧。现在这有点夸张。还有更多。你可以从更复杂的思考方式中学到东西。但这里仍然有一个理想，这就是为什么我很高兴你有Pieter Levels，因为他仍然基本上是这样工作的。

我看着那个说，天哪，这太棒了。

**Lex：** 是的，你在追求那种高度。他一直都很高，使用PHP、jQuery和SQLite。

**DHH：** 我认为这很棒，因为他证明了这不仅仅是一个怀旧的梦想。他实际上在做这件事。他在经营所有这些业务。现在其中一些是，正如他会首先承认的，他只是一个人。

当你只是一个人时，你可以做不同的事情。当你在团队中工作时，当我开始与杰森·弗里德在Basecamp上工作时，我们起初没有一起使用版本控制。我为自己使用版本控制。

然后我想，你知道吗？设计师，啊，他们可能不够聪明来弄清楚CVS。因此我就像，不，不，不，你只是FTP它。你只是FTP它。我知道他们知道如何做FTP。然后在我第三次覆盖他们的更改后。我想，该死的。我必须教杰森CVS不要再这样做。但我认为我们仍然可以以我们在90年代的方式工作，以Pieter今天的工作方式，甚至在团队环境中，有更多的真理。

我们太愿意将我们的开发者人体工程学交给复杂性商人。

**Lex：** 你一直在Rails 8中追求这一点。那么你如何带来现代框架的所有酷炫功能，并使其无需构建，使创建和发布东西像90年代的PHP一样容易？对我来说，很难击败Pieter Levels的方法。发布一些PHP是如此容易。

**DHH：** 应该是这样。为什么应该比那更难？我们今天的计算机几乎比90年代的计算机快无限倍。所以我们不应该能够以更简单的方式工作吗？我们应该回顾90年代说，哦，那太复杂了。

现在我们有更复杂的技术，速度更快，它允许我们以这些更容易使用的方式工作。但这不是真的。但现在你可以看到我在Ruby on Rails工作中画的线，特别是Rails 8。对我来说，无构建是回到那种90年代的感觉，现在我们可以做一些这些事情而不放弃所有的进步。因为我确实认为你可能过于怀旧。

我确实认为你可以开始幻想90年代的一切都更好。不是的。我的意思是，我在那里。有很多糟糕的事情。如果我们能以某种方式找到一种方法来结合我们在过去20年中取得的优势和进步与开发者人体工程学的便利性，我们就能赢。

无构建是对我在过去10、15年中最讨厌的Web开发部分的拒绝，那就是JavaScript场景。我不是说我讨厌JavaScript的人。我的意思是，我经常开玩笑说JavaScript。这是我第二喜欢的编程语言。这是一个非常遥远的第二名。Ruby是迄今为止第一名，但我实际上喜欢JavaScript。我不认为这是一种糟糕的语言。

它受到很多抨击。人们添加一个字符串的二加一，它给出一些无意义的东西。我只是想，是的，但你为什么要这样做？只是不要这样做。这种语言实际上相当可爱，特别是现代版本。ES6真正引入了适当的类语法。所以我可以以许多与我喜欢使用Ruby工作的相同方式使用JavaScript，使事情变得更好。

但在2010年代初直到最近，所有这些进步都发生在预处理中，发生在构建管道中。浏览器无法说出令人愉快的JavaScript方言。所以每个人都开始预编译他们的JavaScript，以便能够使用更现代的编程方式与被视为陷入古老JavaScript版本的浏览器，没有人真正想要使用。

这对我来说是有意义的，但它也是深深不愉快的。我记得在那个时候，我称之为黑暗时代，与JavaScript，这不可能是最终目的地。我们不可能将互联网变成如此不愉快的工作场所。我会开始在JavaScript中使用webpack和所有这些依赖项处理项目。

我会把它放下五分钟，这个东西就不会再编译了。JavaScript社区，特别是其框架和工具，在2010年到2020年的十年中经历的流失量是荒谬的。你必须被困在那个疯人院里才能不意识到我们让自己陷入了多么完全变态的情况。

为什么一切都一直在破坏？我的意思是，笑话不仅仅是软件会破坏，这会让我个人恼火。但然后我会去Hacker News，我会看到一些关于某个框架的最新JavaScript发布的线程。线程会是这样的，有人会问，"好吧，我们不是在使用三个月前刚刚使用的东西吗？"人们会说，"那个东西太过时了。那是三个月前的事。

你必须跟上新程序。我们正在完全重写第无数次的一切。"你在过去一段时间在框架上学到的任何东西，都是无用的。你必须扔掉一切，你必须重新开始。

你为什么不这样做，愚蠢的白痴？

**Lex：** 这是一种接管开发者社区的大规模歇斯底里吗？你认为你必须不断创建新框架和新框架。我们过了那个黑暗时代吗？

**DHH：** 我认为我们正在摆脱它，我们正在摆脱它，因为浏览器变得更好了。浏览器技术有停滞。其中一些是一直追溯到IE5的遗留问题。所以IE5基本上将整个互联网开发体验置于深度冻结中，因为微软在2000年代中期赢得了浏览器战争，然后他们基本上解散了他们的浏览器开发团队。因为他们想，"好吧，工作完成了。我们不需要在互联网上进行更多创新。

我们现在可以回到编写Windows表单或其他什么，现在我们控制一切？"直到显然Firefox点燃了一点东西。然后Chrome进入了场景，谷歌认真地推动网络前进。你有一个点燃，也许浏览器可以更好。也许浏览器没有在2005年及时冻结。

也许浏览器实际上可以像它所是的开发平台一样进化。但然后发生的是你有很多聪明的人涌入网络，因为网络结果是有史以来最伟大的应用程序开发平台。这是所有钱被赚的地方。这是所有亿万富翁被铸造的地方。

这是Facebook和世界上的其他人出现的地方。所以你有所有这些脑力应用于如何与网络合作的问题。有一些非常聪明的人有一些，我确信非常好的想法，他们没有程序员快乐作为他们的第一动机。他们有其他优先事项，这些优先事项允许他们贴现甚至合理化他们在各处注入的复杂性。其中一些复杂性来自组织结构。

当你有一个像Facebook这样的公司，例如，它确实依赖于网络并想要推动它前进，但已经将开发角色工作切成这些微小的小生态位。我是前端glob管道配置器。哦，是的，我是前端工程师。突然网络开发者不再是一个人。

它是15个不同的角色。这本身就注入了大量的复杂性。但我也想在这里给出大胆的案例，这是一些复杂性是必要的，以达到我们今天的位置。复杂性是一座桥梁。它不是目的地，但我们必须穿过那座桥梁才能到达我们今天的位置，浏览器坦率地说是令人难以置信的。

你可以在文本文件中编写的JavaScript，然后在Web服务器上为浏览器摄取提供服务，这是惊人的。这实际上是一个非常好的体验。你不需要任何预处理。你可以只写文本文件，将它们发送到浏览器，你有一个令人难以置信的开发。

**Lex：** 我们也应该说它可能有点破碎，至少HTML，但即使JavaScript也可能有点破碎，它仍然有点工作，也许是半屁股工作。

但就像，浏览器必须处理的混乱代码的数量是疯狂的。

**DHH：** 这是当今计算中最困难的问题之一，就是解析整个互联网。因为对我们作为Web开发者来说幸运的是，但对浏览器开发者来说可能不是那么多，除了Flash的短暂时期，每个曾经创建的网页今天仍然运行。

我在九年级做的网页今天会在现代浏览器上渲染，30年后。

**Lex：** 这太疯狂了。

**DHH：** 这完全疯狂。当你想到我们与网络的进化量，我们如何使它变得更好，浏览器采用了多少更多标准。今天创建一个新浏览器本质上是一个阿波罗项目，这就是为什么它不经常发生，这就是为什么即使像微软这样的公司也必须扔毛巾说，"我们做不到。"现在我实际上不认为这对网络有好处。

如果我们只是得到一个运行一切的单一浏览器引擎，就有单一文化的危险，我们处于这种危险中。我喜欢Ladybird项目，例如，正在尝试从头开始制作一个新的浏览器引擎。我支持了那个项目。我会鼓励人们研究那个。

这真的是一个美妙的事情。它由一群过去在其他浏览器项目上工作的人组成。

**Lex：** 真正独立的Web浏览器。

**DHH：** 我们真的需要那个。但我可以在我的脑海中保持那个想法。同时，我在我的脑海中保持这样的想法，谷歌的Chrome对网络作为首要Web开发平台的生存至关重要。

如果不是因为谷歌和他们的整个业务依赖于蓬勃发展的开放网络，苹果、微软，我认为会很乐意看到网络消失，消失成为只是服务于他们可以完全控制的本机Web应用程序或本机移动应用程序和本机桌面应用程序的东西。所以我对谷歌有各种各样的问题，但不是Chrome。

Chrome是对世界各地Web开发者的完全礼物，对网络作为开发平台。他们应该得到巨大的信贷，我认为为此。即使它与他们的商业模式纠缠在一起，Chrome的一半是监视你或通知有针对性广告的代码。还有一堆事情，我不是很喜欢。我可以将其与我们需要网络角落的冠军这一事实分开，他们有数万亿美元的市值价值骑在开放网络上。

## 关于Chrome和反垄断的讨论

**Lex：** 我们要进行切线上的切线上的切线。所以让我们去Chrome。我认为Chrome对人类的积极影响是不可估量的，原因就是你刚才描述的。在技术方面，它们呈现的功能，它们创造的竞争，它刺激了Web技术的美妙繁荣。但无论如何，我必须问你关于最近司法部试图分拆Chrome和谷歌的事情。你认为这是一个好主意吗？你认为这会造成伤害吗？

**DHH：** 这是一场灾难。我这样说是作为一个对反垄断斗争非常同情的人，因为我确实认为我们在技术中有反垄断问题。但我们没有这些问题的一个地方，总的来说，是浏览器，是我们用来访问开放网络的工具。

首先，我们有Firefox。现在Firefox做得不是很好。Firefox多年来一直由谷歌支撑，以阻止正在与司法部发生的事情，即他们是镇上唯一的游戏。苹果有Safari。我对苹果也有很多问题，但我喜欢Safari。我喜欢我们有一个在首要操作系统上运行的首要浏览器，人们不能将网络变成只是Chrome体验的事实。

但我也认为开放网络需要这个万亿美元的冠军，或者至少从中受益。也许它不需要它，但它肯定从中受益。在技术中垄断形成的所有错误事情中，Chrome是最后一个。

这就是为什么我有时对反垄断斗争感到如此沮丧，有真正的问题。我们应该首先关注首要问题。比如我们手机上的收费站。它们是更大的问题。不是开放网络。不是我们用来访问开放网络的工具。如果我不想使用Chrome，如果我的在互联网上运行的业务的客户不想使用Chrome，他们不必这样做。

我们从来没有被迫通过它。开放互联网仍然是开放的。所以我认为司法部选择以这种方式追求谷歌真的很遗憾。我确实认为有其他事情你可以为谷歌钉住，他们的广告垄断也许，或者在控制广告分类账的两边所做的恶作剧，他们既控制供应又控制需求。

有问题。Chrome不是吗？你最终会让网络变得更糟。这是我们在考虑立法时，当我们考虑垄断斗争时，我们总是必须记住的事情，你可能不喜欢今天的事情看起来如何。你可能想对此做些什么，但你也可能让它变得更糟。

欧洲GDPR背后的良好意图目前已经达到了什么？每个人在互联网上讨厌的Cookie横幅。这不能帮助任何人做任何更好的事情，任何更有效的事情，以任何方式、形状或形式保存任何隐私，这是一个完全的失败，只丰富了律师和会计师和官僚。

**Lex：** 是的，你说Cookie横幅是欧洲在技术方面做得最差的所有地区的纪念碑。

**DHH：** 这是良好意图直接通向地狱的纪念碑。欧洲实际上在良好意图直接通向地狱方面是世界级的。

**Lex：** 所以地狱看起来像Cookie接受按钮，你必须接受所有Cookie。那就是地狱的样子，一遍又一遍。你实际上永远不会到达网页。

**DHH：** 只是在人类规模上，试着想象每天有多少小时浪费在点击那个上面。我们对网络作为人们享受的平台造成了多少伤害，因为它们。互联网部分是丑陋的，因为Cookie横幅。

Cookie横幅应该拯救我们免受广告，广告可以让网络变得丑陋。有很多这样的例子，但Cookie横幅在一次感觉中让整个互联网变得丑陋。这是一个完全的悲剧。但更糟糕的是，这就是为什么我称之为欧盟搞错的一切的纪念碑，是我们已经知道这一点十年了。

没有任何地方认真的人相信Cookie横幅对任何人做任何好事。然而我们一直无法摆脱它。有这一个我认为现在10或12年的立法。在每个可以想象的指标上都是完全失败的。每个人都普遍讨厌它，但我们似乎无法对此做任何事情。

这是任何假装或假装为不仅仅是公民，而是世界各地的人们让事情变得更好的官僚机构的破产声明。这就是Cookie横幅真正让我恼火的地方。这不仅仅是欧盟。这是整个世界。你在这个星球上的任何地方都无法躲避Cookie横幅。

如果你去到该死的火星乘坐埃隆的火箭之一，试图访问网页，你仍然会看到Cookie横幅。宇宙中没有人能逃脱这种荒谬。

**Lex：** 可能是火箭上的界面。

**DHH：** 它会更慢。你基本上会有150秒的ping时间。所以从火星通过Cookie横幅需要45秒。

## Ruby的美学哲学

**Lex：** 好吧，让我们回到我们一直在进行的这些递归切线的堆栈。所以Chrome，我们应该说，至少在我看来，不是不公平地获胜。它通过公平的方式获胜，只是更好。

**DHH：** 是的，如果我要为另一边偷人一半秒，人们会说，好吧，也许是的，大多数人确实有点勉强同意这是一个相当不错的浏览器。但然后他们会说它获得主导地位的原因是分发。它获得分发的原因是因为谷歌也控制Android，因此可以使Chrome成为所有这些手机上的默认浏览器。现在我不买那个。

我不买那个的原因是因为在Android上，你实际上被允许发布一个具有与Chrome不同的浏览器引擎的不同浏览器。与iOS不同，如果你想发布浏览器，Chrome，例如，为iOS发布，但它不是Chrome，它是穿着裙子的Safari。iOS上的每个替代浏览器都必须使用Safari Web引擎。那不是竞争。那不是Android上发生的事情。

再次，我认为有一些细微差别，但如果你缩小并查看我们与大技术的所有问题，Chrome不是它。Chrome凭借优点获胜。我勉强地基于那个认识单独切换到Chrome。作为Web开发者，我只是更喜欢它。我在许多方面喜欢Firefox。我喜欢它的精神，但Chrome是比Firefox更好的浏览器，完全停止。

**Lex：** 顺便说一下，我们从来没有提到Edge。Edge也是一个好浏览器。

**DHH：** 因为它也是穿着裙子的Chrome。

**Lex：** 但它从来没有得到爱。我不认为我曾经使用过Bing，我确信Bing真的很好。

**DHH：** 也许你有，因为你知道什么？Bing穿着裙子吗？

**Lex：** 什么？

**DHH：** DuckDuckGo。这实际上是我使用的搜索引擎。DuckDuckGo从Bing获得其搜索结果，或者至少它曾经这样做。如果他们改变了那个，那对我来说是新闻。

**Lex：** 好吧，也许一切都只是一个包装或裙子。一切都在下面穿着裙子。有一些其他的。

**DHH：** 有一些那个。

**Lex：** 乌龟，所有的裙子一直向下。

好吧，我们在谈论什么？他们，我们从JavaScript和你学习如何编程到达那里。所以最终，大成功故事是当你用PHP构建了一堆东西，你实际上在发布东西。那就是Ruby故事出现的时候。所以你与编程的大爱情故事从那里开始了吗？你能带我去那里吗？什么是Ruby？告诉我Ruby的故事。解释Ruby给我。

**DHH：** PHP是将我从只能摆弄HTML并制作一些网页转换为实际能够自己制作Web应用程序的东西。所以我对PHP在这方面欠下巨大的感激。但我从来没有把PHP看作是一个召唤。我从来没有想过，我是一个写PHP的专业程序员，那就是我是谁，那就是我做的。

我把PHP看作是我需要用来敲击计算机直到它产生我想要的Web应用程序的工具。这非常是达到目的的手段。我没有爱上PHP。我非常感激它教会了我编程的基础知识。我非常感激它为经济学设定了标准。但直到Ruby我才开始把自己想象成程序员。

这发生的方式是我第一次被雇佣为专业程序员写代码实际上是由杰森·弗里德，我的商业伙伴。一直回到2001年，我在那时已经在PHP上工作这些游戏网站基本上18个月。没有人为我做代码付钱。我通过从哥本哈根，丹麦发送到芝加哥，伊利诺伊州的电子邮件与杰森·弗里德联系，发送给一个不知道我是谁的人。

我只是提供主动建议。杰森在互联网上问了一个问题，我发送了答案，他在PHP中问。我发送了那个问题的答案。我们开始交谈，然后我们开始工作。顺便说一下，这是互联网可以允许的奇迹。

哥本哈根的一个孩子从未见过芝加哥的这个人如何能够通过电子邮件连接并开始一起工作？顺便说一下，我们现在仍然在24年后一起工作。这太不可思议了。但我们开始一起工作，我们开始在一些客户项目上一起工作。杰森会做设计，37signals会做设计，我会带来编程PHP。

在我们一起在PHP中工作了我认为两个或三个客户项目后，我们一直遇到同样的问题。每当你与客户合作时，你从电子邮件开始那个项目。哦，是的，让我们一起工作。这是我们正在建设的。你开始交易越来越多的电子邮件。在几周过去之前，你必须向项目添加某人。

他们没有电子邮件。他们没有上下文。你发送它，最新文件在哪里？哦，我已经在FTP上上传了。它就像finalfinal_v06_2.0，对吧？那是要得到的。这只是一团糟。在某些方面是美丽的混乱，在某种程度上仍然运行今天绝大多数项目的混乱。电子邮件是最低公分母。这很棒。

但我们已经以严重的方式与客户失球几次，我们想我们可以做得更好。我们知道如何制作Web应用程序。我们不能只是制作一个比电子邮件更好的管理项目的系统吗？这不能那么难。我们一直在做博客。我们一直在做待办事项列表。

让我们把其中一些放在一起，只是制作一个系统，其中参与项目的任何人需要的一切都在一个页面上。它必须足够简单，我不会举办研讨会教你如何使用系统。我只是给你登录代码。你要跳进去。所以那是Basecamp。当我们开始在Basecamp上工作时，我第一次在与杰森的经历中有技术选择的自由。没有客户告诉我，"是的，PHP，听起来不错。

我们知道PHP。你能在PHP中构建它吗？"我有自由统治。在那个时候，我一直在阅读IEEE杂志和2000年代初的其他几本杂志，戴夫·托马斯和马丁·福勒一直在写关于编程模式和如何编写更好代码的文章。这两个人，特别是，都在使用Ruby来解释他们的概念，因为Ruby看起来像伪代码。

无论你是在C或Java或PHP中编程，所有三个选区都能理解Ruby，因为它基本上只是读链接英语。所以这些人使用Ruby来描述概念。首先，我会阅读这些文章只是为了他们解释的概念。我会想，我喜欢你解释的概念，但我也想看看编程语言。

我为什么没有听说过这个？所以我开始研究Ruby，我意识到在那个时候，Ruby可能不被任何人知道，但它实际上已经存在很长时间了。Matz，Ruby的日本创造者，早在93年就开始在Ruby上工作。在互联网甚至是一个东西之前。这里我在2003年，10年后，拿起似乎是这个隐藏的宝石，只是躺在默默无闻中，在众目睽睽之下。

但戴夫·托马斯和马丁·福勒，我认为成功地让我和其他一些人走上了一种编程语言的轨道，这种语言在西方没有被大量使用，但可能是。所以我拿起Ruby，我想，这非常不同。首先，所有分号在哪里？我一直在PHP、ASP中编程，我甚至做了一些Pascal，我看了一些C。到处都有分号。

这是第一个打击我的事情，该死的分号在哪里？我开始想，实际上，为什么我们在编程中有分号？它们是告诉解释器有新的指令行，但我作为人类不需要它们。怎么样？哦，有人在这里照顾人类，而不是机器。所以这真的让我感兴趣。

然后我对自己想，你知道吗？我很了解PHP。我不是一个了不起的程序员。我没有在编程中工作那么长时间，但也许我能弄清楚。我要给自己两周时间。我要写一个概念证明，我与数据库交谈，我拉一些记录，我格式化它们一点，我在HTML页面上显示它们。

我能在几周内弄清楚吗？大约花了一个周末，我完全着迷了。我完全被震撼了，因为Ruby是为我的大脑制作的，就像一个完美的定制手套，由我从未见过的人制作。这怎么可能？

**Lex：** 我们应该说也许像画Ruby具有的某些品质的图片，也许甚至与PHP相比。我们也应该说有一个荒谬的事情，我习惯于我忘记的，PHP到处都有美元符号。

**DHH：** 是的，是的，有线噪声。这就是我喜欢称呼的。

**Lex：** 有线噪声，这是一个如此美丽的短语。是的，所以有所有这些看起来像程序的东西。用Ruby，我的意思是Python有一些相似之处。它只是看起来像自然语言。你可以正常阅读它。

**DHH：** 这里有一个做五次迭代的野生循环。你可以字面上输入数字五，点，现在我在数字五下调用方法。顺便说一下，这是Ruby的美丽方面之一，像整数这样的原语也是对象。你可以调用5.times，开始括号。现在你在那个括号中的代码上迭代五次，就是这样。

**Lex：** 好吧，这很好。

**DHH：** 这不仅仅是好的，这是例外的。字面上没有其他我知道的编程语言能够将线噪声煮沸，几乎每个其他编程语言会注入到五次迭代在代码块上到那种程度。

**Lex：** 哇，这真的很好，好吧，谢谢你给那个例子。那是一个美丽的例子。哇，我不认为我知道什么编程语言做那个。这真的很好。

**DHH：** Ruby充满了那个。所以让我深入几个例子。因为我真的认为它有助于画图片。让我通过说我实际上，我喜欢Python的精神来为此做前缀。我认为Ruby和Python社区分享很多相似之处。它们都是动态解释语言。它们都专注于即时性和生产力以及在很多方面的易用性。但然后它们在许多其他方面也非常不同。它们非常不同的一种方式是美学上。Python，对我来说，我希望我不会太冒犯人们。我以前说过这个，它很丑。

它在其空间中很丑，因为它充满了多余的指令，这些指令对于Guido在87年制作Python时的遗留原因是必要的，这些原因仍然在2025年这里。我的大脑无法应对那个。让我给你一个基本例子。当你在Python中制作类时，初始化方法，起始方法是def。

好吧，公平。这实际上与Ruby相同，D-E-F，方法的定义。然后它是下划线，不是一个，下划线，两个，init，下划线，下划线，括号开始，self，逗号，然后第一个参数。

**Lex：** 是的，整个self事情。

**DHH：** 我看着那个说，对不起，我出去了。我做不到。关于它的一切都冒犯了我的核心敏感性。这里你有所有新对象或类必须实现的最重要方法。它是我在任何地方见过的最美学上冒犯的输入初始化方式之一。你们对此没问题吗？

**Lex：** 嘿，你让我，你知道，你就像在谈论我的婚姻或类似的东西，我没有意识到我一直在一个有毒的关系中。然而，我只是习惯了它。

**DHH：** 对我来说，顺便说一下，那是Ruby的魔力。它打开了我的眼睛，帮助美丽的程序可能是。我不知道我一直在ASP中工作。我一直在PHP中工作。我甚至没有美学美丽代码是我们可以优化的东西的概念。我们可以追求的东西。甚至超过那个，我们可以追求它超过其他目标。Ruby之所以如此美丽，这不是意外，也不容易。Ruby本身是在C中实现的。解析Ruby代码非常困难，因为Ruby是为人类编写的，人类是混乱的生物。他们喜欢以正确的方式的东西。我无法完全解释为什么__init__让我反感，但它确实如此。当我看Ruby替代品时，它真的很有指导意义。

所以它是def，同样的部分，D-E-F，空格，初始化，括号。甚至不是括号。如果你不需要在参数内调用它，甚至没有括号。这本身实际上也是一个主要部分。如果人类不需要额外的字符，我们不会只是把它们放进去，因为解析计算机会更好。

我们要摆脱分号。我们要摆脱括号。我们要摆脱下划线。我们要摆脱所有那些丑陋，所有线噪声，并将其煮沸到其纯粹的本质。同时，我们不会缩写。

这是Ruby和Python之间美学的关键差异。init，短类型，只有五个字符。初始化要长得多，但它看起来好得多，你不经常输入它。所以你应该看一些漂亮的东西。如果你不必一直这样做，它很长是可以的。那些美学评估在Ruby语言中到处都是。

但让我给你一个更好的例子。if条件。这是所有编程语言的基石。他们有if条件。如果你采用大多数编程语言，它们都有if。这在几乎每种语言中基本上是相同的。空格，开始括号，我们都这样做。

然后你有也许，让我们说你调用一个叫做user.isadmin的对象，关闭括号，关闭括号，开始括号，这是我们要做的，如果用户是管理员，对吧？那将是一个正常的编程语言。Ruby不这样做。Ruby几乎煮沸了所有这些。我们从if开始。好吧，那是一样的。

没有括号必要，因为人类没有歧义来区分下一部分只是一个单一语句。所以你做if，空格，用户，点，管理员，问号。没有开括号，没有括号，什么都没有。下一个，开行。这是你的条件。那个问号对计算机没有意义，但它对人类意味着什么。

Ruby纯粹作为人类之间的沟通工具放入谓词方法样式。解释器实际上更多的工作能够看到这个问号在这里。为什么这个问号在这里？因为它读得如此好。如果user.admin？

这就是Ruby的美学。这就是为什么我如此深深地爱上了这种编程语言。几乎有一种神圣灵感的感觉，无论Matz在编写Ruby的初始版本时在哪里，都超越了时间，以至于没有人甚至开始达到它。这是我总是觉得迷人的另一件事。我通常相信有效市场理论，如果有人想出更好的捕鼠器或更好的想法，其他人最终会复制他们，以至于也许原来的捕鼠器甚至不再被记住。没有人能够复制Ruby的那种本质。

他们借用了元素，这完全没问题，但Ruby在这些指标上，在这种对人类和程序员的信任上仍然比其他人都高。

**Lex：** 我们也应该说，你知道，也许在那个指标上的完美编程语言，然后有成功的语言，这些往往是不同的。关于Brendan Eich创建JavaScript的故事有一些美妙的东西。JavaScript接管世界的方式有一些真正美丽的东西。我最近有机会访问亚马逊丛林，我最喜欢做的事情之一就是看蚂蚁接管任何东西，一切。就像它是一个很好的分销系统。

这是一个混乱的东西，似乎没有秩序，但它只是工作和它的机制。

**DHH：** 更糟糕是更好的。更糟糕是更好的。我的意思是，这实际上是软件开发中的一个模式的名称，以及其他如何做的方式是Linux的模式。Linux在数量上比我认为当时是Minx更糟糕。

其他方式更像大教堂，不那么奇怪，它仍然是一个有一些东西的，不完美可以帮助某些东西前进。这实际上是我研究的一个技巧，以至于我现在几乎在我做的所有开源中都融入了。我确保当我发布我工作的任何新东西的第一个版本时，它有点破碎。

它在邀请人们进来帮助我的方式上有点破碎。因为没有比放出他们知道如何修复和改进的东西更容易获得其他程序员合作的方法了。

**Lex：** 是的，这太棒了。

**DHH：** 但Ruby在某种程度上，或者至少在这方面有点不同。不是在所有方面。Matz得到了语言的精神，语言的设计恰到好处。但Ruby的第一个版本非常慢。花了，我的意思是数百人年才让Ruby既美丽又高效且真正快速。

**Lex：** 我们应该说让你爱上这种特定编程语言的东西是元编程吗？

**DHH：** 是的，所以这采用了我们刚才谈论的所有这些元素，并将它们调到11。我会快速解释元编程。

**Lex：** 是的，请。

**DHH：** 元编程本质上是5.days的一个版本。你可以向语言添加关键字。Active Record是Rails中与数据库通信的部分。这是一个系统，其中数据库中的每个表都由一个类表示。

所以如果我们再次采用用户示例，你做类用户从active record base下降，然后你可以写的第一行是这个。我希望我的用户有很多帖子或有很多评论。让我们这样做。我们正在制作一些用户可以发表评论的系统。下一行是has_many，空格，冒号评论。现在你已经在用户和评论之间建立了依赖关系，这将为用户提供整个主机的访问和工厂方法，以便能够拥有评论，创建评论，在该行中更新评论，单独has many看起来像一个关键字。

它看起来像Ruby语言的一部分。那就是元编程。当Rails能够将这些元素添加到你如何定义类中，然后运行代码，向使用类添加一堆方法时，那就是元编程。当元编程以这种方式使用时，我们称之为领域特定语言。

你采用像Ruby这样的通用语言，并将其定制到某个领域，比如在对象级别描述数据库中的关系。这是你可以做的早期例子之一，用户有很多评论。Belongs_to :account。现在你已经建立了一对一关系，在我们有一对多关系之前。Rails充满了所有这些领域特定语言，有时它甚至不像Ruby。

你无法识别Ruby关键字。你只能识别在其自己的编程语言中看起来像关键字的东西。现在再次，我知道Lisp和其他人也做这些东西。他们只是用可以塞进编程语言的最大量的线噪声来做。

Ruby在一个你无法区分我的元编程和Matz的关键字的水平上做，并且零线噪声。

**Lex：** 是的，我应该说我的初恋是Lisp。所以有一个你看不到的慢眼泪。

**DHH：** 我实际上从来没有自己写过任何真正的Lisp。

**Lex：** 好吧，那你怎么能如此严厉地判断它呢？

**DHH：** 因为我有两只眼睛，我可以看代码，我的美学敏感性禁止我甚至走得更远。这是一个限制，我知道。

我实际上应该深入Lisp，因为我发现我学到了很多，只是深入，也许我在这里再次侮辱Lisp，但编程语言的过去，例如Smalltalk。我认为Smalltalk是一个令人难以置信的实验，也有效，但不适合今天的编程环境。

## 动态类型的哲学

**Lex：** 我喜欢我们如此多地谈论Ruby以及什么是美丽的代码和什么是美丽的编程语言。所以我认为在你那里的描述中暗示的也许你明确表达的事情之一是Ruby是动态类型与严格类型。你不仅说这是一件好事，而且你会为动态类型辩护到死。

就像那种自由是一种强大的自由来保护。

**DHH：** 这是使Ruby成为Ruby的本质。这就是为什么我不完全理解当人们呼吁Ruby添加静态类型时。因为对我来说，这是这是什么的基石。为什么你想把最美丽的语言之一变成更丑陋的东西？这是我对静态类型的主要反对意见之一。

这不仅仅是它在某些方面限制了你。它使元编程更难。我写了很多元编程。我见过在TypeScript中做元编程需要什么。这实际上是真正让我开始撕裂或让TypeScript退出我参与的一些项目的事情之一。我们从turbo中拉出了TypeScript。

我们拥有的前端框架之一，因为我试图在TypeScript中编写元编程，我只是被激怒了。我不想要那种体验，但我也不想从美学的角度。我讨厌重复。我们刚刚谈论了我多么喜欢Ruby将所有这些表达式煮沸到其本质。你不能删除一个点。

你不能删除一个字符而不失去一些东西。这一刻你去静态类型，你声明，至少我知道有方法做隐含类型等等。但让我们只是采用一个例子的刻板案例，例如。大写U，用户，我声明变量的类型；小写用户，我现在命名我的变量；等于大写用户或新大写用户。我重复了用户三次。

我没有时间做这个。我没有这个的敏感性。我不想让我的Ruby被这个污染。现在我理解人们喜欢静态类型的所有论点，当主要论点是它使工具更容易。例如，它使在编辑器中做自动完成更容易。

它使找到某些类型的错误更容易，因为也许你调用对象上不存在的方法，编辑器实际上可以在你甚至运行它之前捕获那个错误。我不在乎。首先，我不用工具写代码。我用文本编辑器写它们。我用我的赤手从屏幕上凿出它们。我不自动完成。这就是为什么我如此喜欢Ruby，这就是为什么我继续爱上文本编辑器而不是IDE。

我不想要IDE。我希望我的手指必须单独输入它的每个元素，因为它会强迫我留在Ruby美丽的世界中。因为一旦输入大量样板变得容易，好吧，猜猜什么？你可以有很多样板。基本上每种具有出色工具支持的语言都对样板有更高的容忍度，因为思维是，好吧，你无论如何都不会输入它，你只是自动完成它。

我根本不想要那个。我想要我正在工作的织物，它只是一个文本文件。没有其他的。所以这些东西一起发挥作用。有美学部分，有工具部分，有元编程部分。有Ruby的鸭子类型精神的事实，我不知道你以前是否听过那个术语。

它本质上不是关于，我可以调用这个方法，如果对象是某个类的。它是，如果方法响应，我可以调用这个方法吗？在这方面，它非常来自Smalltalk。你实际上不检查那个类是否有方法，这允许你在运行时动态添加方法并做各种真正有趣的事情，这些事情支撑了我们在Ruby中做的所有美丽的元编程。我不想失去任何那个。

我不在乎好处。我一遍又一遍看到吹捧的好处之一是编写正确的软件要容易得多。你可以有更少的错误。你会有更少的空指针异常。你会有更少的所有这些东西。是的，我没有任何那个。这只是在我的标准操作模式中不会发生的事情。

我不是说我没有错误，当然我有，但我用单元测试，用集成测试捕获那些错误。这些是会捕获逻辑错误的预防措施。编译但错误的东西以及不可编译的东西。所以我从来没有被吸引到这个世界，部分原因是我在某类系统上工作。

我完全接受那个。如果你正在编写有500万、1000万、5000万行代码的系统，有数百、数千或数万程序员，我完全接受你需要不同的方法。我反对的是为1000万行代码的代码库和10万程序员工作的正确的想法也是我应该在我的卧室中使用来创建Basecamp的同样的东西，因为我只是一个个人。那完全是胡说八道。

在现实世界中，我们会知道那根本没有意义。你不会，我不知道，用你的Pagani去Costco买杂货。这是一个糟糕的车辆。它只是没有空间。你不想弄脏美丽的座位。你不想做任何那些事情。

我们知道在某些领域非常好的某些东西不适用于所有。在编程语言中，似乎我们忘记了那个。现在，公平地说，我也可能有一点忘记那个的声誉。当我第一次学习Ruby时，我如此疯狂地爱上了这种编程语言，以至于我几乎发现任何人会选择任何其他编程语言来编写Web应用程序是不可想象的。

我有点以那种精神参与Ruby on Rails的传道，作为一个十字军，我只需要教你福音。我只需要向你展示我们刚才谈论的这个条件代码，你会在尖锐论点的点上转换。现在，我了解到那不是方式。它不是方式的部分原因是程序员思考不同。

我们的大脑配置不同。我的大脑完美地配置为Ruby。完美地为我可以用文本编辑器凿出代码的动态鸭子类型语言。其他人需要IDE的安全性。他们想要除非你调用它上面的方法否则不会编译的类的安全性。

我已经接受了那个，但大多数程序员没有。他们仍然陷入，本质上，我喜欢静态类型。因此，静态类型是创建可靠正确系统的唯一方法，这只是如此令人震惊，坦率地说，面对证据，相反的证据山，说这是愚蠢的事情。

这是我如此爱上Shopify作为Ruby on Rails的旗舰应用程序的原因之一。Shopify存在于大多数程序员永远不会触及的规模。在黑色星期五，我认为Shopify做了每秒100万个请求。那不是100万个图像请求。那是通过商业管道漏斗的动态请求。我的意思是，Shopify运行大约30%的所有电子商务商店在该死的互联网上。

所有商业的巨大部分通过Shopify运行，那运行在Ruby on Rails上。所以Ruby on Rails能够扩展到那个水平，而不在它所做的所有事情中使用静态类型。现在我知道他们在某些方面做了某些实验，因为他们正在达到你会用动态类型达到的一些限制。

你用动态类型达到的一些限制实际上，顺便说一下，只是当你写500万行代码时你达到的限制。我认为Shopify单体是大约500万行代码。在那个规模上，一切都破坏，因为你在人类能够用编程语言做什么的前沿。

部分差异是Ruby是如此简洁的语言，那些500万，如果它们已经用，让我们说Go或Java写，会是5000万或2500万。现在那可能已经缓解了当你在有许多程序员的巨大系统上工作时你有的一些问题。但它肯定也会复合它们试图理解2500万行代码。

**Lex：** 所以这个东西确实扩展。这是一个持续的神话，它不扩展Shopify和其他人。但Shopify认为一个很好的例子。顺便说一下，我喜欢Shopify，我喜欢Tobi，太棒了。

**DHH：** 你必须让Tobi上。

**Lex：** 是的，当然。

**DHH：** 今天早上刚和他说话。

**Lex：** 当然，他很聪明。我有机会在沙漠的某个地方和他一起出去玩，我忘记了在犹他州。

他只是一个聪明的人。Shopify，shopify.com/lex一直在支持这个播客最长时间。我不认为实际上Tobi知道他们赞助这个播客。我的意思是这是一个大公司，对吧？

**DHH：** 这是一个巨大的公司。我认为不到10,000名员工，市值1200亿，每季度GMV四分之一万亿。

**Lex：** 他参与细节。

**DHH：** 他是，非常如此。关于Tobi的有趣故事。Tobi在2000年代中期在Rails核心团队。Tobi自己写了Active Merchant，这是创建商店的框架之一。他写了Shopify今天仍在使用的Liquid模板语言。

他对Rails生态系统有巨大的贡献列表，他是公司的CEO。是的，我认为这只是...这对我来说非常鼓舞人心，因为它与我喜欢做的事情完全相反。我喜欢一天中大部分时间用我自己的手凿代码。他经营一家几乎10,000人的公司，字面上像世界商业依赖于它。我甚至无法开始理解的关键性水平。

然而我们可以在计算机科学和程序开发中的许多这些基本问题上看到眼对眼。那是一个动态范围，能够包含Rails成为刚刚开始有想法的一个开发者的伟大工具，他们甚至不完全知道一切。

谁正好在PHP在那些90年代末会是一个好选择的水平，因为是的，我可能可以上传一些东西到FTP服务器，等等。Rails确实比那有更多复杂性，但它也有如此长的跑道。跑道一直到该死的Shopify。那是我可以为我们可以做很多的动态范围做的最有说服力的论点。

即使说了那个，Shopify当然是异常值。当我写Rails时，我不把Shopify作为主要目标。我想到单个开发者。实际上，我确实想到Shopify。但我不想到现在的Shopify。我想到当Tobi写Snow Devil时的Shopify，那是第一个销售滑雪板的电子商务商店，他创建的，那是前Shopify的Shopify。

他完全自己创建的。那是可能的，因为Ruby on Rails不仅仅是关于美丽的代码。它同样是关于生产力。它同样是关于个人程序员能够产生的影响。他们可以构建他们可以在头脑中保持整个事情并能够推进它的系统，这样你可以从一个开发者坐着工作某些东西，那个东西是Shopify，它变成今天的样子。

当我们谈论编程语言并比较它们时，我们经常在非常晚的阶段比较它们。比如什么是更好的编程语言，让我们说2009年的Twitter，当它已经是一个巨大的成功。Twitter开始于Ruby on Rails。然后他们遇到了一些扩展问题。当时这是一个大争议。

然后他们最终我认为用其他语言重写它，顺便说一下，我认为这是Ruby on Rails有史以来最好的广告，因为在他们切换后的10年里什么他妈的都没有发生，对吧？本质上零创新。其中一些是因为他们在做长时间的转换，所有早期成功部分来自于他们有敏捷性快速改变和采用等等。

这就是初创公司需要的。这就是Shopify需要的。这就是Twitter需要的。这就是每个人都需要的。这是Ruby on Rails的第一优先级。确保我们不失去那个。因为当开发工具和编程语言由大公司驱动时经常发生的是它们反映他们的组织结构图。

React和使用它需要的其他一切在某些方面是Meta如何构建Facebook的反映。当然是这样。因为当然这是那个的抽象。我不是说React不是一个伟大的工具，不能被较小的团队使用。当然可以。但它诞生在与Ruby on Rails这样的东西非常不同的背景中。

**Lex：** 让我说这个小旁白，因为我认为我们可能会回到Shopify并经常庆祝它。只是一种个人笔记。这个特定的播客有比我可能拥有的更多的赞助商和想成为赞助商的赞助商。对我来说真的，真的很重要的是不在乎，并能够庆祝人们。就像我庆祝人们。

我庆祝公司。我不在乎他们在赞助。我真的不在乎。我只是想让那个非常明确，因为我们将继续说关于Shopify的积极事情。我不在乎。停止赞助。这对我来说真的不重要。但是，是的，我只是想让那个明确。

所以，但要在Twitter和Shopify的扩展事情上停留，你能向我解释Shopify用YJIT做什么吗？他们必须尝试做什么来扩展这个东西？因为那是一个令人难以置信的故事，对吧？

**DHH：** 是的，所以Shopify对整个Ruby生态系统，不仅仅是Rails，特别是Rails做出的伟大贡献之一是YJIT。

所以YJIT是他们的Ruby编译器。这只是让一切更有效率，在Shopify规模上，即使在Ruby的开销和执行时间上挤出5%、10%的改进也是一个巨大的交易。现在，Shopify不需要YJIT。Shopify已经在Ruby的初始版本上运行，我认为比我们今天拥有的慢10倍。

如果你回顾Ruby 186，Tobi可能开始的，就像我开始的，那足以推动Shopify到今天的规模。扩展对话中的很多在区分两件事的失败中丢失了。规模是我们谈论的一种包装，当里面真的有多个包装时。

一个是运行时性能，延迟。你能多快执行单个请求？它能发生得足够快，用户不会注意到吗？如果你的Rails请求需要一秒半执行，用户会注意到。你的应用会感觉缓慢和迟钝。

你必须将响应时间降低到，让我们说至少300毫秒以下。我喜欢以100毫秒作为我的延迟目标。那是一种性能。你能从单个CPU核心中挤出多少那种延迟的性能，这告诉你单个请求的价格是什么。

但然后你是否能处理像Shopify现在正在做的每秒100万个请求。如果你有一个可以每秒做一千个请求的盒子，你只需要X个盒子来达到一百万。你实际上会发现的是，当涉及编程语言时，它们在这方面都是一样的。它们都在很大程度上水平扩展得很好。你只是添加更多盒子。

扩展Shopify的困难部分通常不是编程语言。是数据库。这实际上是Shopify现在面临的挑战之一，你如何在他们运营的规模上处理MySQL？你什么时候需要转移到其他数据库来获得全球性能？所有这些事情。关于扩展Ruby的问题是经济问题。

如果我们在应用服务器上花费这么多，如果我们能从Ruby中获得5%更多的性能，好吧，我们可以节省5%的那些服务器，这可以过滤到预算中。现在那个分析基本上得出一件事。Ruby是一种奢侈语言。这是一种奢侈，在我看来是最高的奢侈。

它是编程语言的Coco Chanel。不是每个人都能负担得起的东西。我以最好的可能方式意味着这个。互联网上有一些应用程序，每个请求的价值如此之少，你负担不起使用像Ruby这样的奢侈语言来编程。

你只是必须用C或Go或其他一些低级语言或Rust将就，谈论那里的线噪声...

**Lex：** 这就像语言的旧货店。

**DHH：** 确切地，你需要一种...你需要一个非常低的级别来做它。你负担不起使用奢侈语言来构建它。这对Shopify不是真的。即使在2004年，这对Basecamp也不是真的。

这对99%的所有曾经创建的Web应用程序都不是真的，因为99%的Web应用程序的主要成本组件，不是CPU课程。是湿课程。是人类课程。是人类理解和涉及系统的能力。是他们的个人生产力。

我曾经做过一次计算，当有人第400次说，"哦，如果你从Ruby切换到一些更快的语言，你可以节省一堆钱。"我计算出在那个时候，我认为我最后一次做这个计算是将近十年前。我们在Ruby应用服务上花费了大约15%的运营预算。

所以为了将我的业务成本概况改善七个百分点，我必须选择快两倍的东西。这相当困难。相比之下，如果Ruby和Ruby on Rails比其他东西甚至高10%的生产力，我会移动针头得多，因为让个人程序员更有生产力实际上重要得多。这就是为什么人们对AI如此兴奋。

这就是为什么他们对硅谷一个年薪30万美元的程序员现在可以做三个或五个人的工作这一事实感到恐慌，至少在理论上。我实际上还没有在实践中完全看到那个，但让我们假设理论是正确的，如果不是现在，那么在六个月内。那是一个巨大的交易。当涉及这些类型的业务应用程序时，这比你是否能从CPU中挤出更多周期重要得多。

如果你正在制作像你有的Tim Sweeney这样的虚幻引擎渲染东西。是的，他需要真正出汗所有那些细节。Nanite引擎不能在Ruby上运行。它永远不会。它不是为那个而意味着，好的。这些类型的业务应用程序绝对可以，人们现在对AI兴奋的一切，那个额外的能力只是做更多。

这就是为什么我们在2000年代初对Ruby感到兴奋。那是因为我看到如果我们甚至能从人类程序员中挤出10%的改进，我们就能够用更少的钱做更多的事情。

**Lex：** 可能会争论这个，但我真的喜欢与AI一起工作，与AI合作。我会争论你想要AI生成的代码类型是人类可读的，人类可解释的。

如果它生成Perl高尔夫代码，那不是合作。所以它必须说人类。这不仅仅是你用英语写提示。你也想用像Ruby这样的人类可解释语言阅读响应，对吧？所以这实际上对AI也是有益的。因为你有点说对你这个雕塑家，那种精英主义的Coco Chanel雕塑家，你想在你的花哨键盘上用你自己的手指输入每一个字母。

但也是Ruby的好处也适用于一旦其中一些由AI编写，你实际上用你自己的手指做编辑。因为你可以与它互动，因为它是人类可解释的。

**DHH：** 我真正喜欢的范式是埃隆实际上在你们谈论Neuralink时在你的一个节目中说的，Neuralink允许你和机器之间的带宽增加。语言，无论是口语还是书面语，都是非常低带宽的。如果你要计算我们坐在这里时可以交换多少位，它非常慢。Ruby有更高的通信带宽，揭示了每个字符传达比大多数其他编程语言更多的概念。

所以当你与AI合作时，你想要真正高的带宽。你希望它能够与你一起产生程序，无论你是否让它写代码，你们两个都能真正快速理解，你可以将一个宏大的概念，一个宏大的系统压缩成你们两个都能理解的更少的部分。现在，我实际上也喜欢与AI合作。

我喜欢凿我的代码，我使用AI的方式是在一个单独的窗口中。我不让它驱动我的代码。我试过那个。我试过光标和风面，我不喜欢那种写作方式。我不喜欢那种写作方式的原因之一是我可以字面上感觉到能力从我的手指中流失。与材料的那种即时性水平消失了。

我感受到这个最多的地方是我做了这个叫做Omakub的Ubuntu混音，当我切换到Linux时，它全部用Bash写的。我以前从来没有用Bash写过任何严重数量的代码。所以我使用AI合作，与我一起写一堆Bash，因为我需要所有这些。我知道我想要什么。

我可以用Ruby表达它。但我认为通过Bash过滤是一个有趣的挑战，因为我正在做的是设置Linux机器。这基本上是Bash设计的目的。这是一个很好的约束。但我发现自己做的是一遍又一遍地要求AI以同样的方式表达条件，例如，在Bash中。通过不输入它，我没有学习它。

我在使用它，我得到了我想要的表达，但我没有学习它。我有点害怕。我有点害怕，这是学习的终结吗？如果我不输入，我不再学习吗？我为我重新塑造的方式是，我不想放弃AI。作为程序员查找API，对某些东西获得第二意见，做草稿，这是如此更好的体验。但我必须自己做输入，因为你用手指学习。

如果你正在学习如何弹吉他，你可以看尽可能多的YouTube视频。你不会学会吉他。你必须把手指放在弦上才能真正学习动作。我认为这里有一个与编程的平行，编程必须部分通过实际输入来学习。

**Lex：** 我只是真的，这很迷人。听着，我大脑的一部分100%同意你。部分不同意。我认为AI应该在学习的循环中。现在当前系统不这样做，但我认为光标说，基本上强迫你输入某些东西是非常可能的。所以如果你设置学习模式，我不想放弃AI。

我认为氛围编码是一种技能。所以对于有经验的程序员来说，将氛围编码作为一种东西来解雇太容易了。

**DHH：** 我同意，我不会解雇它。

**Lex：** 但我认为你需要开始建立那种技能，开始弄清楚如何防止能力从你的手指和大脑中滑走。

就像你如何与其他技能并行发展那种技能？我不知道。我认为这是一个迷人的谜题。我知道太多真正强大的程序员只是有点避免AI，因为它目前有点太愚蠢。

**DHH：** 是的，它有点太慢。这实际上是我的主要问题。它在某些方面有点太愚蠢，但在其他方面有点太慢。

当我使用Claude的代码，Claude的终端版本，这实际上是我使用它的首选方式时，我变得太不耐烦。感觉就像我回到了代码必须编译的时代，我必须去做其他事情。在代码编译时煮一些茶。好吧，我在Ruby中工作了20年。我不再有编译等待。所以有那个方面。

但我认为对我来说更关键的方面是我真的关心能力。我见过即使是伟大的程序员一旦他们放下键盘会发生什么。因为即使在AI之前，这会在人们得到提升时立即发生。大多数在大企业工作的伟大程序员停止每天写代码，因为他们只是有太多会议要参加。他们有太多其他事情要做。他们不可避免地失去与编程的联系。

这并不意味着他们忘记一切。但如果你没有把手指放在酱汁，源代码中，你会失去与它的联系。没有其他方法。我不想要那个，因为我太享受它了。这不仅仅是关于结果。

这是理解编程对喜欢编码的程序员来说至关重要的，不仅仅是关于他们从中得到的程序。那可能是经济价值。这不是唯一的人类价值。人类价值在表达中同样多。当有人坐下来弹吉他并演奏"天堂阶梯"时，有一个完美的录音将永远持续。你可以只是把它放在Spotify上。你实际上不需要做它。快乐是自己指挥吉他。

程序员的快乐，我作为程序员的快乐，是自己输入代码。如果我提升自己，如果我将自己提升出编程，我将自己变成项目经理。如我前几天写的，AI乌鸦谋杀的项目经理。我可能在我的整个职业生涯中成为项目经理。如果我不关心自己写代码，我可能在20年前成为项目经理。我只是想要结果。

这就是我如何开始编程的。我只是想要结果。然后我爱上了编程，现在我宁愿退休也不愿放弃它。现在这并不意味着你不能拥有你的蛋糕并需要它。我做了一些氛围编码，我不在乎我没有自己演奏。我只是想看到一些东西。我脑海中有一个想法。我想看到一些东西。那很好。

我也整天使用AI。事实上，我已经到了如果你把它从我这里拿走，我会想，哦，我的上帝，我们甚至如何在互联网上查找东西了？Stack Overflow还在吗？或者我仍然是一个东西？就像我如何甚至找到我整天都有的一些这些问题的答案。

我不想放弃AI。事实上，我会说我喜欢使用AI的方式，我因为AI变得更聪明。因为我使用AI让它向我解释事情。甚至愚蠢的问题。我会有点尴尬甚至输入到谷歌。AI完全愿意给我一些Unix命令的ELI5解释。我应该已经知道，但我不知道，对不起，你能向我解释一下吗，现在我知道这个东西。

所以在我整天与AI工作的一天结束时，我聪明了一点。像5%，对不起，不是5%，也许半个百分点。这随着时间的推移而复合。但我也看到，当我在Omakub项目上工作并试图让AI为我驱动时，我感觉在一天结束时我可能愚蠢了半个百分点。

**Lex：** 好吧，你说了很多有趣的事情。

首先，让我们从你问愚蠢问题的事实开始。如果你去Stack Overflow问一个愚蠢的问题或阅读别人的愚蠢问题和答案，那里有很多判断。AI有时过度地没有判断。它通常说，哦，那是一个很好的问题。

**DHH：** 过度了。

**Lex：** 是的，哦，那很棒。是的，我的意思是，它如此有利于学习。它是如此美妙的学习工具，我也会想念它。它是一个很好的基本搜索引擎，进入特定编程语言的各种细微差别，特别是如果你不太了解它或像你可以加载文档的API。它对学习如此伟大。对我个人来说...

我的意思是，在快乐量表上，它让我更兴奋地编程。我不知道那确切是什么。部分是，我真的很抱歉。Stack Overflow是一个令人难以置信的网站，但那里有负面性。那里有判断。与我旁边的炒作人一起出去很兴奋，只是说，是的，那是一个很好的想法。我会说，不，那是错误的。

我会纠正AI，AI会说，"你绝对正确。我怎么没想到那个？"你知道，重写代码。我想，神圣的狗屎，我正在有，那就像一个伙伴。那就像真的积极，非常聪明，挑战我思考。即使我从不使用它生成的代码，我已经是一个更好的程序员。

但实际上更深的事情是出于某种原因我有更多乐趣。那是一个真的，真的重要的事情。

**DHH：** 我喜欢把它想象成一个配对程序员，正是因为那个原因。配对编程在2000年代变得时髦。你会有两个程序员在一台机器前，你会在你们之间推键盘。一个程序员会驾驶，他们会输入。

另一个程序员基本上会坐着看代码，建议改进，查找一些东西。那是一个真的有趣的动态。现在不幸的是，我是一个内向的人，所以我可以做大约五分钟，然后我想从桥上跳下去。所以它不适合我作为全职职业。

但AI允许我一直拥有那种体验的所有最好的。现在我认为真的有趣的是，你说的关于它让它更有趣。我实际上没有想过那个，但它对我更有趣的是再次成为初学者。它让第一次成功学习Bash更有趣。

现在我必须做绕道，我让它为我写所有代码，我意识到我没有学习近我希望的那么多，我开始做一旦我自己输入它。但它给了我信心，你知道什么？如果我需要自己做一些iOS编程，我可能六年没有做过，那是我最后一次涉足它。我从来没有真正为真实构建任何东西。

我现在非常有信心，我可以与AI坐下来，我可以在本周末在应用商店中有一些东西。如果我没有像AI这样的配对编程伙伴，我不会有那种信心。我实际上不经常为Ruby代码使用它。每当我尝试它时，我偶尔会印象深刻，他们就像，哦，它得到了这一件事。这真的很了不起，它实际上相当不错。

然后我会再问你两个问题，我会想，哦，是的，好吧。如果你是我的初级程序员，我会开始敲我的手指并说，你必须振作起来。现在当然伟大的事情是我们可以只是等五分钟。Anthropic CEO似乎认为到年底90%的所有代码将由AI编写。

我对此有点怀疑，但我对编程可能变成手动完成时的马的前景持开放态度。我们娱乐性地做的事情不再是在洛杉矶四处走动的交通方式。你不会鞍马并去杂货店从Whole Foods的鞍袋中拿东西。那只是不再是一个东西。

那可能是编程的未来，完全可能的手动编程。我也不在乎。就像即使我们有所有最好歌曲的伟大演绎，正如我所说，有数百万人喜欢弹吉他。它可能不再具有曾经的经济价值。我认为我相当确信这是真的，我们也许已经看到了顶峰。

现在我理解悖论，当某些东西的价格下降时，实际上总体使用量上升，对那个活动的总支出上升。那也可能发生。但我们现在看到的是很多大商店，很多大公司不像五年前那样招聘。

他们不预期他们会需要更多的程序员。有争议的是，Tobi实际上在Shopify内部发出了一份备忘录，要求每个考虑雇佣某人的人问这个问题，这能由AI完成吗？现在，他在这个问题上比我更超前。

我看一些编码战壕，我想，我很想更多地使用AI，我看到它如何让我们更有生产力，但它还没有达到我只是说，哦，我们有这个项目的水平。让我只是把它给AI代理，它会去做它。

**Lex：** 但让我们诚实一点。

你就像一个克林特·伊斯特伍德类型的角色牛仔，骑着马看到汽车四处行驶，你就像，好吧。

**DHH：** 那是其中的一部分。我认为那是，拥有那种谦逊是重要的，你擅长的可能不再是社会重视的。这在历史上发生了一百万次，你可能在制作马鞍方面异常出色，例如。那是很多人过去关心的事情，因为每个人都骑马。

然后突然骑马变成了这个小众爱好，有些人关心它，但远没有那么多。那没关系。现在这个的另一件事是我有幸...我已经是程序员将近30年了。那是一个很好的运行。我试图以这种方式看待生活，我已经被祝福了几十年的经济上可行的，高价值的方式，将我在工作世界中最喜欢的东西翻译成写Ruby代码。那是如此有价值，我可以做数百万美元。

如果那明天结束，我不应该带着遗憾看待那个。我应该带着感激看待它。

**Lex：** 但你也是一个高度有经验，聪明和有主见的人，所以得到你对马的未来的意见真的很有趣，因为它，你知道，有很多年轻人听这个，他们喜欢编程或对用软件，用Ruby on Rails，那种语言构建东西的可能性感到兴奋。这和现在的可能性。

**DHH：** 但这是一个职业吗？

**Lex：** 这是一个职业吗？

如果确实一个人可以在AI的帮助下构建越来越多的东西，就像他们如何学习那种技能？这是一个好技能学习吗？我的意思是，对我来说，这是真正的谜团，因为我认为你仍然绝对必须从头学习如何编程是真的。但你如何平衡那两种技能？因为我也，当我现在思考时，有一种可怕的技能滑走发生。

在特定代码片段上真的几分钟的事情。这很可怕。不是驾驶的方式，你知道，当你有一辆车为你驾驶时不会那么快滑走。所以那真的让我害怕。所以当有人来找我问我如何学习编程时，我不知道建议是什么，因为我认为仅仅使用光标或copilot生成代码是不够的。

**DHH：** 绝对不够。如果你想学习，你们都不想变得更好。如果你只是成为一个敲击猴子，也许你在第二个是有生产力的。但然后你必须意识到，好吧，任何人都可以敲击吗？如果我们所做的只是整天坐着敲击？是的，是的，是的，是的，是的。那不是一个可销售的技能。

现在，我总是为自己和当我对其他人说话时加上这个前缀，这是规则第一号，没有人他妈的知道任何事情。没有人可以预测甚至六个月前。现在，我们可能处于AI未来炒作的顶峰，因为我们看到所有的承诺，因为其中很多是真实的，很多人自己经历过它。

这个令人震惊的事情，硅以某种方式思考，感觉eerily让人想起人类。我实际上会说对我来说大事甚至不是ChatGPT。甚至不是Claude。是DeepSeek。在本地运行DeepSeek并看到思考框，它与自己对话关于如何制定响应。

我几乎想想，这是一个噱头吗？它是为了我的利益而做这个表演吗？但那实际上不是它如何思考的。如果这是它实际如何思考的，好吧，我有点害怕。这是令人难以置信的人类，它以这种方式思考。但那去哪里？所以在95年，我最喜欢的电影之一，我最喜欢的B级电影之一出来了，"割草机人"。

**Lex：** 伟大的电影。

**DHH：** 关于虚拟现实的令人难以置信的电影。成为化身并生活在VR中，就像故事是一团糟。但美学世界建立起来是令人难以置信的。我想，我们五年后。我现在要生活在VR中。我只是要漂浮。我要成为化身。这是大多数人类可以度过一天中大部分时间的地方，那没有发生。我们30年后，VR仍然不在这里。

它在这里用于游戏。它在这里用于一些专门的应用程序。我最大的孩子喜欢玩Gorilla Tag。我不知道你是否试过那个。那基本上是最热门的VR游戏。美妙，它很棒。预测未来真的很难，因为我们只是不知道。然后当你考虑AI时，你甚至有最聪明的人说，我不认为我们完全理解这如何工作。

**Lex：** 但然后另一方面，你有摩尔定律，似乎已经工作了很多，很多，很多年，减少晶体管的大小，例如。所以就像你知道，Flash没有接管互联网，但摩尔定律工作了。所以我们不知道AI是哪一个。

**DHH：** 哪一个。这就是我也发现如此迷人的。

我忘记了谁做了这个演示，但网络社区中的某人，关于飞机历史的这个伟大演示。所以你从莱特兄弟飞行，什么？1903年或类似的东西。40年后你有喷气式飞行。在四十年中令人难以置信的进步量。然后在56年，我认为是，波音747本质上前身的停止标志被设计，基本上从那时起什么都没有发生。只是自50年代以来飞行体验的小调整和改进。

不知何故，如果你要预测飞行会去哪里，你坐在42年，你会看到，你会记得莱特兄弟在03年飞行，你看到喷气发动机来了，你就像，我们要在另外两十年内飞到星星。

我们要发明超级巨型超音速飞行，将在两小时内穿越地球。然后那没有发生。它耗尽了。这就是预测未来如此困难的原因。我们可以在那一刻如此兴奋，因为我们在图表上通过早期点画一条线，它看起来像那些早期点只是向右上升，有时它只是平坦化。

这也是我们有如此多关键基础设施的事情之一，例如，仍然在COBOL上运行。世界上大约五个人真正理解，真正深入地，有很多，社会可能失去能力。它仍然需要，因为它在追逐未来。COBOL仍然与我们在一起。这是我想到编程的事情之一。

Ruby on Rails现在处于这样的水平，50年后，极有可能仍然有大量Ruby on Rail系统在运行。很难预测那个确切的世界会是什么样子。但昨天的天气告诉我们，如果仍然有来自70年代的COBOL代码今天运行社会保障，我们还没有找到一个干净的方法来转换那个，更不用说理解它，我们当然应该对预测未来谦逊。

我不认为在70年代写那个COBOL代码的任何程序员有任何该死的想法，在2025年，支票仍然从他们当时编码的业务逻辑中切出。但那只是让我得出关于年轻程序员应该做什么的问题的结论？你不会能够预测未来。没有人会能够预测未来。

如果你喜欢编程，你应该学习编程。现在，那会永远是一个职业吗？我不知道，但什么会永远是一个职业？谁知道？就像一秒钟前，我们认为是蓝领劳动将首先被提取。是机器人将接管。

然后GenAI出来，然后所有艺术家突然看起来像，"神圣的狗屎，这现在要做所有动画吗？现在要做所有音乐吗？"他们变得真的害怕。现在我看到最新的特斯拉机器人说，哦，也许我们现在回到蓝领有麻烦。因为如果它可以那样跳舞，它可能可以修理厕所。

所以没有人知道任何事情，你必须然后为未来定位自己，以这样的方式，你选择一个职业或道路，如果结果是你必须重新工具和重新技能，你不会后悔你采取的道路，这不重要。那是一个一般的生活原则。

对我来说，我如何看待我涉及自己的所有努力是我想对所有结果感到满意。当我们开始在37signals上工作一个新产品时，我为其成功设置我的心理模型。我说，你知道什么？如果没有人想要这个，我将有另一个机会写美丽的Ruby代码，探索Greenfield域，学习新的东西，构建我想要的系统，即使没有其他人想要它。多么祝福。

多么特权。如果一堆人想要它，那很棒。我们可以支付一些薪水，我们可以保持业务运行，如果这是一个巨大的成功，美妙。我可以影响一堆人。

**Lex：** 我认为对我来说一个大的开放问题是你可以用氛围编码走多远，是否年轻开发者投资大部分时间到氛围编码或从头写代码的方法。

所以氛围编码，意思是，所以我有点倾向于模因，但氛围编码，意思是你生成代码，你有这个你想创建的东西的想法，你生成代码，然后你用自然语言到提示和手动修复它。你学习足够手动修复它。所以那是学习过程。

你如何修复生成的代码，或者你从头写代码并让LMS有点标签，标签，标签，标签，添加额外的代码。就像你倾向于哪一部分？我认为为了安全，你应该在两者中找到美丽和艺术和技能，从头开始。所以应该有你的时间的某个百分比只是从头写，某个百分比氛围编码。

**DHH：** 应该有更多时间从头写。

如果你有兴趣学习如何编程，不幸的是，你不会通过看健身视频变得健康。你不会通过看YouTube吉他视频学会弹吉他。你必须实际自己演奏。你必须做仰卧起坐。编程，理解，学习几乎任何东西都需要你做。

人类不是为了通过只是从远处观看其他人来吸收信息，以转化为技能的方式而建造的。现在讽刺的是，似乎AI实际上相当擅长那个，但人类不是。如果你想学习如何成为一个有能力的程序员，你必须编程。理解这真的不那么困难。现在我理解诱惑。

诱惑在那里，因为氛围编码可以产生东西。也许在这一刻，特别是在你不熟悉的新领域，用你不完全了解的工具，那比你能做的更好。或者你会花更长时间到达，但你不会学到任何东西。

你会以这种肤浅的方式学习，感觉像学习，但完全是空卡路里。其次，如果你可以只是氛围编码它，你不是程序员。然后任何人都可以做它，这可能是美妙的。那本质上是Access数据库发生的事情。那是Excel发生的事情。它采取了会计师成为软件开发者的能力，因为工具变得如此可访问，他们可以为业务下周如何做构建模型。那在Excel之前需要程序员。现在它没有，因为他们可以自己做它

通过编码使非程序员能够以我发现绝对美妙的方式探索他们的想法。但它不会让你成为程序员。

**Lex：** 我同意你，但我想为我们两个都错了留出空间。例如，可能有，氛围编码实际上可能是一种技能。

如果你训练它，通过氛围编码，让我们包括纠正步骤，迭代纠正。如果你真的擅长那个，你可能超越从头开始运行的人，你可以想出真正创新的东西，特别是在历史的这一刻。虽然LLM有点太愚蠢，无法创建超级新颖的东西和完整的产品，但它们开始接近那个。

所以如果你现在投资时间成为一个真正好的氛围编码器，也许这是正确的事情，如果它确实是一种技能，我们有点模因关于氛围编码，就像坐回去，它在名字中。但如果你认真对待它，一个竞争性的氛围编码器并擅长骑AI的浪潮并擅长编辑代码与从头写代码的技能，你可能实际上可以在长期走得更远。

也许编辑是与从头写作根本不同的任务，如果你认真对待那作为你发展的技能。对我来说那是一个开放的问题。我只是想，我个人，现在你在另一个水平，但只是我，只是个人，我不如编辑我没有写的代码。

**DHH：** 没有人是。

**Lex：** 那是一个不同。

**DHH：** 这一代没有人是，但也许那是一种技能。

**Lex：** 也许如果你与AI在同一页面上，因为AI有一致性。它真的像一个有一致风格和结构等等的配对程序员。加上你自己的提示，你可以控制你写的代码类型。我的意思是，它通常可能是一种技能。

**DHH：** 那是提示工程师的梦想。我认为这是完全的白日梦。

我不认为存在不擅长写作的编辑。我写了很多书。我有很多专业编辑。不是所有人都写了自己的伟大书籍，但所有人在某种方面都是伟大的作家。如果你不知道如何做，你不能给某人指针。如果编辑不能自己制作解决方案，编辑很难能够发现问题的错误。

在我看来，编辑是奖励，成为好编辑的能力是你从成为好实干家得到的奖励。你必须首先是实干家。现在那与说氛围编码，提示工程不会能够很快产生完全形成的令人惊叹的系统不同。我认为那完全可能，但然后没有技能留下，这也许是最大的回报。

那不是AI的整个承诺吗，它只是所有自然语言，甚至我笨拙的制定问题的方式可能导致美丽，简洁的答案。那对我来说实际上是一个更吸引人的愿景，将有这些特殊的提示工程巫师，他们知道如何恰好挠AI来产生他们想要的。

AI的美丽是认为不知道AI实际如何工作的第一件事的人能够制定他们的想法和他们想要的愿望，AI可以以某种方式采取那个混乱的想法团块并产生某人想要的东西。那实际上是编程一直是什么。

经常有不知道如何编程的人，想要程序，然后雇佣程序员，给他们他们想要的混乱描述，然后当程序员交付那个回来时说，"哦，不，实际上那不是我的意思。我想要别的东西。"AI可能能够提供那个循环。

如果那发生到最大程度，是的，不会有那么多程序员，对吧？但希望大概某人仍然，至少在可预见的未来，必须理解AI产生的是否实际工作。

**Lex：** 作为一个有趣的案例研究，也许一个思想实验。

如果我想氛围编码Basecamp或HEY，以及你建造的一些产品，就像瓶颈是什么？我会在路上哪里失败？

**DHH：** 我在尝试这样做时看到的，尝试使用氛围编码构建真实的东西是你实际上很早就失败了。氛围编码能够在当前时刻构建看起来像它工作的东西的表面，对吧？但它在各种方式上有缺陷。

有明显的方式，模因方式，它泄漏你所有的API密钥，它以纯文本存储你的密码。我认为那最终是可解决的。就像它会弄清楚那个，或者至少它会在那个上变得更好，但它在自己的迷宫中迷失的能力现在非常大。

它编码某些东西，然后你想改变某些东西，它变成打地鼠游戏。真的很快，Pieter Levels一直在做这个美妙的飞行模拟器正在谈论那个，在某个规模上，东西只是继续咬自己的尾巴。你想修复某些东西，它破坏五个其他东西，我认为实际上是独特的人类，因为那是大多数糟糕程序员的方式。

在某个复杂性水平的领域，他们不能修复一件事而不破坏三个其他事情。所以在那种方式中，我实际上在某种程度上，这几乎是AI将弄清楚这个的积极信号，因为它现在已经完成了一个极其人类的轨迹。它正在犯的错误类型是初级程序员一直犯的错误类型。

## Rails的愿景与教条

**Lex：** 是的，我们能缩小并看看Rails的愿景，宣言，教条吗？是什么让编程语言框架变得伟大？特别是对于Web开发。所以我们谈论了快乐。Ruby的潜在目标。还有什么？

**DHH：** 所以你在看我认为在2012年写出的九个要点。首先，在我们深入它们之前，我想说我写下它的原因是如果你想要一个社区持续，你必须记录它的价值观，你必须记录它的实践。如果你不这样做，最终你会得到足够多的

新人进来，他们对这个东西应该去哪里有自己的想法。如果我们没有指导灯帮助我们做决定，我们会开始挣扎。我们会开始实际分崩离析。我认为这是各种机构开始分崩离析的关键原因之一。我们忘记了为什么切斯特顿的围栏在那里。

我们只是说，为什么那个围栏在那里？让我们把它拉出来。哦，它是为了把狼挡在外面。现在我们都死了，哎呀。所以我想写下这些东西。如果我们只是快速一个一个地看它们，你谈到了为程序员快乐优化。我把它放在第一位，向Matz致敬，这很大程度上是关于接受在编写美丽代码和我们想要从系统中得到的其他东西之间偶尔有权衡。

可能有运行时权衡，可能有性能权衡，但我们无论如何都要这样做。我们也将以许多程序员默认不舒服的方式允许歧义。我实际上在这里给出的例子是在交互式Ruby shell中，你可以玩语言或甚至与你的领域模型交互。你可以以至少我发现的两种方式退出它。

你可以写exit。砰，你退出程序。你可以写quit。砰，你退出程序。它们做同样的事情。我们只是写了exit和quit，或者构建那个的人写了exit和quit，因为他们知道人类可能选择其中一个或另一个。Python是这个的完美对比。

在Python交互协议中，如果你写exit，它不会退出。它会给你一个教训。它基本上会告诉你阅读该死的手册。它说使用exit括号或Ctrl-D，即文件结束来退出。我想，一个非常人性化，另一个非常工程师。我以最好的可能方式意味着它们两个。Python是迂腐的。

Python从一开始声明的价值是应该最好有一个且只有一个方式来做某件事。Ruby是完全相反的。不，我们想要适合不同人类大脑的完整表达，这样看起来语言正在猜测他们想要什么。

**Lex：** 其中一部分也描述了最少惊讶原则，这是一个难以工程到语言中的东西，因为你必须有点，这是一个主观的东西。

**DHH：** 这就是为什么你不能用一种方式做它，这就是为什么我使用exit和quit的例子。对一些人来说，最少惊讶原则会是，"哦，exit。那是我如何退出提示的。"对其他人来说，它会是quit。

为什么我们不只是两个都做？

**Lex：** 好吧，那么约定优于配置是什么？那是一个大的。

**DHH：** 那是一个大的。那是一个巨大的。它诞生于我在早期对特别是Java框架的挫折，当你在当时为Java设置Web应用程序框架时，字面上写数百甚至数千行XML配置文件并不罕见。哦，我需要这个。

我希望数据库使用外键作为post_id。不，不，不，我希望它作为post大写ID。哦，不，不，不，你必须做大写PID。有所有这些方式，你可以配置外关系键应该如何在数据库中工作，它们都不重要。我们只需要选择一个，然后那就好了。如果我们选择一个并且我们可以依赖它，它就成为约定。如果它是约定，我们不必配置它。

如果我们不必配置它，你可以更快地开始你实际关心的事情。所以约定优于配置本质上是采取系统应该预组装的想法。我不只是给你一盒该死的乐高积木并要求你建造千年隼。我给你一个完成的玩具。

你可以编辑，你可以改变它，它仍然是用乐高积木建造的。你仍然可以取下一些片段并放入一些其他片段，但我给你最终产品。这与大多数程序员喜欢的相反。他们喜欢一盒乐高积木。他们喜欢从头开始把一切放在一起。他们喜欢做所有这些详细的小决定，根本不重要。

我想把那个提升起来，这样嘿，我不是试图从你那里拿走决定。我只是想让你专注于实际重要的决定，你真正关心的。没有人关心它是post_id还是post ID还是PID。

**Lex：** 是的，伟大的默认值。

**DHH：** 是的。

**Lex：** 这只是一个美妙的事情。你有所有这些愿望，他们要做某种定制的，最美丽的乐高城堡，没有人曾经从这些片段建造过。

但实际上在大多数情况下要有生产力，你只需要建造基本的东西。然后在那之上是你的创造力来的地方。

**DHH：** 绝对地，我认为这是那些，教条的一部分，很多使用Ruby on Rails的程序员会勉强承认这是一个好事，即使他们不真正喜欢它。

就像很难击败从头开始用乐高积木建造对程序员的吸引力。那只是我们喜欢的。这就是为什么我们首先是程序员，因为我们喜欢把这些小片段放在一起，但我们可以将那种本能导向堆栈的更有生产力的端。

**Lex：** 好吧，其他一些是什么？

**DHH：** 菜单是omakase。

它实际上来自伟大的默认值真正重要的同一原则。如果你看现在JavaScript生态系统的所有错误，例如，它是没有人负责菜单。有十亿种不同的菜肴，你可以配置你的定制特定配置，但没有人做工作确保它们都适合在一起。

所以你在JavaScript生态系统中有所有这些独特的问题，例如，可能有25种主要的只是做控制器层的方式。然后有多少种与数据库交谈的方式。所以你得到这个n乘n乘n的排列，没有人使用同样的东西。如果他们使用同样的东西，他们只使用同样的东西大约五分钟。所以我们没有保留的智慧。

我们建立不了持久的技能。Rails走完全相反的方式说，你知道什么？Rails不只是一个白色框架。它是解决Web问题的完整尝试。它是解决你需要构建伟大Web应用程序的一切的完整尝试。

那个拼图的每一片理想地应该在盒子里预配置，预组装。如果你想稍后改变其中一些片段，那很棒。但在第一天，你会得到一个由真正关心每一片成分的厨师设计的完整菜单，你会享受它。那又是那些许多程序员认为我知道更好的事情之一。

他们在某种超本地意义上确实如此。每个程序员都知道更好。这就是Ruby建立的基础。每个程序员在他们的特定情况下都知道更好。也许他们可以做一些危险的事情。也许他们认为他们知道更好，然后他们炸掉他们的脚，然后他们真正会知道更好，因为他们已经炸掉他们的脚一次，不会再这样做。但omakase的菜单就是那个。

**Lex：** 所以你总的来说看到单体的价值？

**DHH：** 是的，集成系统。

**Lex：** 集成。

**DHH：** 有人想到整个问题。这是我自从术语被创造以来一直在对微服务进行十字军东征的原因之一。微服务诞生于本质上一个好想法。

当你有数千名工程师在数百万行代码上工作时，你在Netflix规模上做什么？没有人可以一次在他们的头脑中保持整个系统。你必须把它分解。微服务可以是一个合理的方式来做那个。当你在Netflix规模时，当你将那个模式应用到20个程序员在50万行代码的代码库上工作的团队时，你是白痴。

你只是不需要将方法调用变成网络调用。这是分布式编程的第一规则。不要分布你的编程。它使一切更难。作为程序员你必须考虑的所有失败条件当涉及网络电缆时变得无限更难。

所以我讨厌过早分解的想法，微服务正是那个。单体说。让我们尝试专注于构建单个人类实际可以理解的整个系统，并通过压缩所有概念将那个范式推到尽可能远，这样更多的它将适合单个操作人类的内存。然后我们可以有一个系统，我实际上可以理解所有Basecamp。我实际上可以理解所有HEY。

这两个系统都刚好超过十万行代码。我见过人们做这个也许两倍，也许三倍那个规模，然后它开始分解。一旦你到达肯定超过50万行代码的北方，没有个人人类可以做它，那时你进入也许某种程度的微服务可以有意义。

**Lex：** Basecamp和HEY都是十万？

**DHH：** 大约十万行代码。

**Lex：** 哇，它很小。

**DHH：** 是的。考虑到Basecamp我认为有大约420个屏幕，不同的方式和配置的事实。

**Lex：** 啊，你包括前端吗？

**DHH：** 不，那是Ruby代码。好吧，它是前端，在某种意义上，一些Ruby代码。

它对前端有益，但它不是JavaScript，例如。现在我们可能稍后谈论的另一件事是我们实际上为我们所有的应用程序写很少的JavaScript。HEY，这是Gmail竞争对手。Gmail我认为发布28兆字节的未压缩JavaScript。如果你压缩它，我认为大约是6兆字节，28兆字节。想想那是多少行代码。

当HEY启动时，我们发布了40千字节。它试图解决同样的问题。如果你做不同的事情，你可以用28兆字节的未压缩JavaScript或40千字节解决电子邮件客户端问题。但那通过本质上同样的问题来。这就是为什么我激烈地战斗分离前端和后端。

在我看来，这是对Web开发的伟大犯罪之一。我们仍然在为此赎罪。我们分离和分割了什么是和应该是统一的问题解决机制。当你在前端和后端上工作时，你理解整个系统。

你不会进入这些分解的阵营，最终你最终得到像GraphQL这样的东西。

**Lex：** 好吧，让我们快速浏览教条的其余部分。没有一个范式。

**DHH：** 没有一个范式涉及Ruby在其核心是一个激烈的面向对象编程语言的事实，但它也是一个函数式编程语言。

我告诉你的这个五次，你基本上可以做这些匿名函数调用，你可以将它们链接在一起，非常符合真正的函数式编程语言如何工作的精神。Ruby甚至通过使字符串不可变而更接近函数式编程的规模。有来自所有不同学科和软件开发的所有不同范式的想法可以适合在一起。

例如，Smalltalk。只有面向对象，就是这样。Ruby试图主要是面向对象，但借用一点函数式编程，一点命令式编程，能够做所有那些。Rails试图做同样的事情。我们不只是要选择一个范式并通过一切运行它。

面向对象在它的中心，但邀请所有这些其他学科是可以的，被启发是可以的。混音是可以的。我实际上认为Rails的主要好处之一是它是一个混音。我没有发明所有这些想法。我没有想出active record。

我没有想出分割应用程序的NBC方式。我采取了我从每个不同阵营学到和拾起的所有伟大想法，我把它们放在一起。不是因为会有一个单一的总体一切理论，而是我会有一个连贯的单元，结合了来自各处的最好的。

**Lex：** 那个想法与单体系统的美丽有点紧张吗？

**DHH：** 我认为单体可以被认为是相当宽敞的，相当像一个大帐篷，单体实际上需要借用一点函数式编程来解决那种学科擅长的问题类型，那种范式擅长解决。如果你也想要面向对象在其核心。

我实际上认为当我看函数式编程语言时，有很多可爱的。然后我看到当他们正在解决的问题的一部分要求改变某些东西时，他们必须经历的一些疯狂扭曲。你说，神圣的狗屎，这是90%问题的伟大范式。然后当你试图解决最后10%时，你完全扭曲自己。

**Lex：** 哦，崇尚美丽代码是下一个。

**DHH：** 我们已经详细谈论了那个，这里有一个真正总结Ruby on Rails的领域特定语言质量的伟大例子，你可以让代码实际上愉快地写和读。这对我来说真的很有趣，因为正如我们谈论的，当我开始学习编程时，它甚至不是一个考虑。我甚至不知道那可能是前提的一部分，那可能是解决方案的一部分。写代码可以感觉像写诗一样好。

**Lex：** class Project, ApplicationRecord belongs_to :account has many participants. Class_name, Person, validates_presence_of :name.

**DHH：** 看，你可以读出来。你甚至没有改变。

**Lex：** 任何像俳句或什么的东西。

**DHH：** 对吧？那不美丽吗？

**Lex：** 是的，它很好。它真的很好。它有直觉的性质。好吧，所以我有具体的问题。

我的意思是ActiveRecord，只是采取那个切线。那必须是你最喜欢的功能。

**DHH：** 它是Rails的皇冠宝石。它真的是。它是如何与Ruby on Rails工作的定义特征。它诞生于有趣的争议水平，因为它实际上使用了马丁·福勒在"企业应用架构模式"中描述的模式。

任何在业务系统上工作的人的最伟大书籍之一。如果你没有读过它，你必须立即拿起它。"企业应用架构模式"，我认为它在2001年出版。它是我多次阅读的极少数编程书籍之一。它令人难以置信。

在其中，马丁描述了一堆如何构建业务系统的不同模式。ActiveRecord在那里有点像脚注。模式字面上叫做active record。你可以查找它。

**Lex：** 好的。

**DHH：** 它叫做active record。我甚至没有足够创造性来想出我自己的名字。但它允许创建，数据库和面向对象的结合，以很多程序员发现有点令人反感的方式。

他们实际上不想用SQL污染那种编程的美丽面向对象性质。前几天Uncle Bob有一个关于SQL是有史以来最糟糕的东西的咆哮。巴巴，好吧，好的，无论如何，我不在乎。这是实用的。我们正在制作CRUD应用程序。

你从HTML表单中取东西，你把它们粘到该死的数据库中。它不比那更复杂。你在光谱的那两端之间放的抽象越多，你只是在愚弄自己。这就是我们正在做的。我们正在与SQL数据库交谈。

顺便说一下，快速旁白，SQL是那些经受了NoSQL数据库结构化列表数据十年的冲击并仍然占主导地位的东西之一。SQL是投资你的时间学习的好东西。每个与Web工作的程序都应该在相当程度上知道SQL。即使他们与ORM，光学关系映射或active record一起工作。你仍然需要理解SQL。

Active record做的不是那么多试图在不同类型的范式后面抽象SQL。它只是让它写起来不那么麻烦。让它更适合在其他领域模型之上构建领域模型，以你不必手写每个该死的SQL语句的方式。

**Lex：** 我们只是说active record是ORM，这是一个层，使与数据库通信直观和人类可解释。

**DHH：** 甚至比那更简单。它将表转换为类，将行转换为对象。我实际上认为SQL大部分很容易理解。你也可以写一些SQL高尔夫。那很难理解。但SQL在其基础上，对SQL的很多批评是它是为人类消费而写的。

它实际上相当冗长，特别是如果你一遍又一遍地做插入之类的事情。它相当冗长插入到表括号，枚举你想插入的每一列，值，括号，与那列匹配的每个值。手写SQL变得乏味，但它实际上非常人类可读。

Active record只是把那个乏味拿走。它使得以人类可描述语言不能的方式组合事物成为可能。它将事物组合成方法，你可以组合这些方法，你可以围绕它们构建结构。所以我不讨厌SQL，我讨厌编程中的很多事情。我试图摆脱它们。SQL真的不是其中之一。

它只是一种感觉，我不想一遍又一遍地写同样的东西。它是一个，我们能稍微简洁一点吗？我们能稍微更好地匹配光学方向，而不试图隐藏我们将这些对象持久化到数据库的事实。那是我认为很多ORM出错的地方。他们试图生活在对象的纯世界中。

从不考虑那些对象必须一致到SQL数据库，然后他们想出了来回翻译的复杂方式。Active record说，你知道什么？只是接受它。这个记录，这个对象不会被保存到某个NoSQL数据库。它不会被保存。它会被保存到SQL数据库。所以只是围绕那个构建整个事情。

它会有属性。那些属性会响应数据库中的列。它不比那个东西让它如此更复杂。

**Lex：** 是的，但我应该说，所以我个人喜欢SQL，因为我是算法人，所以我喜欢优化。

我喜欢知道数据库实际如何工作，所以我可以匹配SQL查询和表的设计，这样有，你知道，最优的，从表中挤出最优性能。好吧，基于那个表实际使用的方式。所以我的意思是，我认为那推到这样的点，就像学习理解SQL有价值。我想知道，因为我开始看active record，它看起来真的很棒。

那会让你懒惰吗？不是你，而是一个滚进来并开始使用Rails的人，你可能可以逃脱从不真正学习SQL，对吧？

**DHH：** 只要你想停留在能力的入门级。这实际上是我与Rails的总体使命，将进入门槛降低到如此之低，以至于某人可以开始在他们的浏览器上看到东西，而基本上不理解任何东西。

是的，他们可以运行Rail的新博客，运行几个生成器，他们有整个系统，他们不理解任何东西。但这是学习更多的邀请。我被激怒的地方，这与AI讨论联系回来，是当那被变成这个模因，程序员不再必须有能力。我的意思是AI会弄清楚它。生成器会弄清楚它。

我不需要知道SQL，active record会从我这里抽象它。不，不，不，伙计，等等。这里的路径是能力。我试图教你事情。我理解我不能在五分钟内教你一切。没有人曾经在任何值得的事情上变得好，可以在五分钟内被教一切。

如果你想成为一个完全全面的Web应用程序开发者，那需要年。但你实际上可以在几天内变得有些生产力。你肯定可以在几天内有乐趣。你会在几分钟和几小时内有乐趣。随着时间的推移，我可以教你更多一点。Active record说，是的，是的。

好吧，从这里开始，然后下周我们会做SQL课。

**Lex：** 实际上你有这个我喜欢的美丽表达，一个伟大的编程语言。像Ruby有一个软坡道，坡道到无穷大。

**DHH：** 那完全正确。

**Lex：** 所以是的。它超级可访问，超级容易开始。

**DHH：** 它从不停止。

总是有更多要学习的。这是我仍然有乐趣编程的原因之一。我仍然在学习新事物。我仍然可以结合新事物。Web作为领域足够深。你永远不会学会所有它。

**Lex：** 提供锋利的刀。

**DHH：** 这是一个好的，因为说这个的另一种方式，说这个的相反方式，Java方式的说法是不要提供脚枪，对吧？我不想给你锋利的刀。你是孩子。你不能处理锋利的刀。这里是钝黄油刀。切你该死的牛排，对吧？那是一个非常令人沮丧的体验。你想要锋利的刀，即使你可能能够割伤自己。我以Matz信任人类的同样方式信任人类。

也许你切掉一个手指。好吧，你不会再这样做。谢天谢地，那是一个虚拟的思考手指。它会重新长出来。你的能力会增长。用锋利的工具工作更有趣。

**Lex：** 那实际上有助于到无穷大的坡道。

**DHH：** 是的，对学习

**Lex：** 重视集成系统。

**DHH：** 我们有点触及了那个。

这是Rails试图解决Web的整个问题，不只是一个小组件。它不是留给你一堆片段。你必须自己把它们放在一起。

**Lex：** 进步胜过稳定。

**DHH：** 你知道什么？如果有一个过时的，可能是那个。

在这个阶段，Rails在许多，许多代中一直非常稳定。最后一个主要版本Rails 8对任何运行Rail 7的人基本上是无升级。Rail 7对任何运行Rail 6的人几乎是无升级。我曾经认为需要更多流失来获得进步，以保持在新东西的前沿。我在经历2010年代JavaScript社区的屈辱之前写了这个。

在那里，似乎稳定性不仅不被重视，它实际上被鄙视，流失本身是我们应该追求的价值。如果你三个月后仍然与同一个框架工作，你是白痴。我看到那个，我实际上退缩了。

如果我今天要写教条，我会不同地写那个。我不会说进步胜过稳定。

**Lex：** 好吧，也许它是编程语言年龄的函数。

**DHH：** 也许，或者对问题的更深理解。我认为技术如此迷人的部分是我们有这种感知，一切不断移动如此快。

不，它不是。一切以冰川速度移动。偶尔有范式转换，就像现在AI正在发生的。就像2007年iPhone引入时发生的，就像95年互联网发生的。那基本上是我职业生涯的总和。三件事改变了。

其间的其他一切都是增量小改进。你可以识别2003年写的Rails应用程序。我知道，因为我当时写的Basecamp仍然在运行，在ARR中赚数百万美元，为客户服务，在当时启动的初始版本上。如果我稍微眯眼，它看起来像我今天会写的Rails代码。所以大多数事情甚至在计算中都不改变。

那实际上是好事。我们看到JavaScript生态系统。当每个人对持续流失变得疯狂时会发生什么？事情不经常改变。

**Lex：** 顺便说一下，在那个小切线上。你只是有点可见地，口头地改变了你15年前的想法。

**DHH：** 是的。

**Lex：** 那很有趣。

你注意到自己多年来相当多地改变想法吗？

**DHH：** 我会说，哦，是的。然后也，哦，不，在这种意义上，绝对有关于人性，关于机构，关于编程，关于业务的基本事情，我改变了想法。然后我也有几乎更有趣的经历，我认为我改变了想法，我尝试了新方式，意识到为什么我首先有原始意见，然后回到它。所以它两种方式都发生。后一部分的例子，例如，

是37signals的经理。很长时间，我会对工程经理作为小型甚至中型公司的不必要负担进行咆哮。在某个时候，我实际上开始有点怀疑自己。我开始想，你知道什么？也许所有程序员确实需要每周与他们的工程经理进行一对一治疗会话，以成为完整的个人。

所以我们尝试了几年，我们雇佣了一些非常好的工程经理，他们以你应该做的方式做工程管理，以到处都做的方式。在那之后，我想，不，不，我是对的。这是正确的。我们不应该有经理。不是每个程序员都需要每周与工程经理进行治疗会话。我们不需要这些和最少的预定聚会。

我们不需要所有这些会议。我们只需要让人们该死的独自工作他们享受的问题，长时间不间断的时间。那是找到快乐的地方。那是找到生产力的地方。如果你能逃脱它，你绝对应该。

工程管理是当那个分解时的必要邪恶。

**Lex：** 那么经理的案例是什么？

**DHH：** 经理的案例是如果你确实有很多人。有一堆工作有点只是出现。一对一是程序员需要某人检查的一个例子。有另一个理想化版本，某人需要指导初级的职业，例如，给他们重定向反馈和所有这些其他东西。

这不是在抽象中，我不同意其中一些事情，但在实践中，我发现他们经常创造比他们解决的更多问题。这里的一个好例子是你能从不比你更好你的工作的人那里得到反馈吗？你得到一些反馈。你可以得到关于你如何在工作中出现的反馈。

你对其他人有礼貌吗？你是好沟通者吗？好吧，是的。但你不能得到关于你的工作的反馈，那更重要。如果你希望在职业中进步，你在比你更好你的工作的人下面和与他们一起工作更重要。我曾经与之工作的每个程序员都对在那个指标上在他们的职业中进步，在他们的手艺上变得更好比他们在拾起中层经理可以教他们的指针更感兴趣。

那不是说它没有价值。那不是说成为更好的人或更好的沟通者没有价值。当然有所有那些事情。但如果我必须选择其中一个或另一个，我更高地重视能力。就像那又是，我警告这个一百万次，因为我知道人们有时听到什么。

他们听到天才混蛋就很好，那很棒，如果某人只是真的擅长他们做的事情，你应该原谅各种恶意行为。我根本不是说那个。我说的是能力的历史是从比你更好的人学习的历史，那种关系应该优先于所有其他，当引入工程经理时，那种关系被稍微放在一边。

现在有趣的是这个对话与我们之前谈论的事情联系回来。大多数工程经理实际上是前程序员。他们至少在某种程度上没有程序，但我一次又一次看到的是他们失去他们的触觉，他们对它的感觉非常，非常快，变成尖头发老板非常，非常快，他们真的擅长检查更新。

只是看我们在项目A上在哪里，如果你需要任何东西或我们准备交付。好吧，是的。也，不。闭嘴，让我该死的独自。让我编程，然后我会出现错误。我会与其他程序员交谈，我可以与他们切磋，我们可以学到一些东西。我可以与他们翻转问题，我们可以前进。如果你回顾计算机行业的历史，所有发生的伟大创新，它都是由没有工程经理的小团队完成的。只是充满高技能个人。

你有约翰·卡马克在这里。我曾经如此仰望它的软件。不仅因为我喜欢快速，不仅因为我喜欢他们正在做的事情，而且因为他分享了一些关于公司如何工作的。没有经理，或者也许他们有一个商业人员做一些商业东西，但那只是为了得到报酬。

其他一切基本上只是设计师和程序员。他们大约有八个，他们创造了该死的"Quake II"。所以你为什么又需要所有这些人？你为什么又需要所有这些经理？我认为，再次，在某个规模上它确实分解。很难只是有十万程序员四处狂奔，没有任何产品妈妈或爸爸告诉他们做什么。

我理解那个。然后即使我说那个，我也不理解它。是的，因为如果你看像Gmail这样的东西，例如，有像Buchheit在当时谷歌的副项目。甚至所有这些巨大公司的如此多的持久长期价值是由没有经理的人创造的。

那不是意外，那是直接因果关系。所以我在某种程度上多年来对这种管理概念变得更加激进，至少对我自己和知道我是谁以及我想如何工作。因为这个的另一部分是我不想成为经理，也许这只是我投射我是内向的人，不喜欢每周与人们进行一对一通话的事实。

但它也概括了我如何能够推进我的职业。我直到我有一扇我可以关闭的门，没有人可以打扰我连续六小时，我才真正与Ruby或其他方面达到下一个水平。

**Lex：** 所以在公司中，可能原因之一是雇佣经理非常容易，经理也从你那里委托责任。

所以如果你只是有一堆程序员四处跑，你有点响应，就像它是工作，它是智力工作，必须处理正在进行的每个问题的第一原则。所以经理就像你可以放松，哦，我会被照顾。但他们然后雇佣他们自己的经理，它只是乘以乘以乘以。

我希望如果一些伟大的公司在美国会有，如果有像一个额外的侧分支，我们总是可以运行，也许物理学家可以想出如何分割模拟，所有经理都被移除。也只是在那个分支中，只是PR和通信人员也。

甚至律师，只是工程师，让我们看看，然后我们合并它回来。

**DHH：** 我基本上在37signals运行那个分支20年。我已经实验了分叉回到另一边。我已经实验了在员工上有全职律师。我已经实验了有工程经理。

我可以告诉你，当那些个人或那些角色都没有时，50、60人的生活要好得多。这从来不是关于个人。这是关于角色。那些角色都不在你的组织全职。偶尔你需要经理。偶尔你需要律师。我可以偶尔扮演经理的角色，好的。然后我可以将它设置回零。这几乎像云服务。

我想要一个经理服务，我可以这周调用七小时，然后我想在接下来的三个月将它降到零。

**Lex：** 是的。我读。我不知道这是否仍然是情况，Basecamp是LLC，没有CFO，像全职会计师。那是升级吗？

**DHH：** 所以最有趣的。这些天，我们确实有财务主管。我认为我们前19年的生活没有。

我们基本上只是逃脱了会计师做我们的账簿，以你会做小冰淇淋店的同样方式，除了我们随着时间的推移会做数亿美元的收入。规模似乎古怪。在某个时候，你也可以爱上你自己的古怪到实际上不健康的程度。

我肯定随着时间的推移这样做了，我们应该有某人安装或更勤奋地数豆子，更早一点。这是只是疯狂盈利和销售基本上可以有无限边际的软件的祝福的一部分，你有点可以逃脱一堆你也许不应该的东西。

部分教我这个教训的是当我们意识到我们没有在我们有Nexus的不同美国州收集销售税。我们花了大约两年和500万美元的和解和清理才摆脱那个混乱。在那之后，我说，好吧，好的，我们可以雇佣财务人员。

我们现在有一个美妙的财务人员，Ron，他实际上最终替换了其他东西。我们曾经有全职数据分析人员，他会做各种洞察挖掘，为什么人们注册这个东西。我们运行了10年，意识到，你知道什么？如果我可以有数据分析人员或会计师，我选择会计师。

**Lex：** 我在如此多的水平上喜欢这个。

我们能停留在你给出的建议，小团队更好吗？我认为那真的更少。更少是更多。你之前说什么？更糟糕是更好。好吧，对不起。

**DHH：** 更糟糕更好在技术采用上很多时候。我认为实际上来自同样的事情。它来自许多伟大突破不仅仅是由小团队而是由个人创造的事实。

个人写一些东西。个人在某个参数上写一些东西，他们做的是更糟糕的。当然，当一个人必须制作一个巨大公司有数百甚至数千开发者可以在那个问题上工作时，它更糟糕。

但在如此多其他参数中，那种更糟糕是价值，那种更少是价值。在我们2006年写的Getting Real中。我们谈论这种更少软件的概念。当我们2004年开始Basecamp时，人们会一直问我们，你们不害怕微软吗？他们有如此多更多资源。他们有如此多更多程序员。

如果他们喜欢你这里的小利基，他们出现，他们只是向问题投掷一千程序员怎么办？我的答案也许部分因为我像24岁是首先，不，世界上没有关心。但真正的答案是他们不会产生同样的东西。你不能用一千人的团队产生Basecamp是的那种软件。你会构建一千人构建的那种软件。

那根本不是同样的事情。如此多的主要突破在最终用户系统中，也在开源系统和基本系统中，他们由个人或非常小的团队完成。甚至所有这些苹果的经典历史总是像，好吧，它是大组织，但然后你有实际上在突破上工作的团队。它是四个人。它是八个人。

它从来不是200。

**Lex：** 大团队似乎减慢事情。

**DHH：** 是的。

**Lex：** 这如此迷人，部分是经理事情。

**DHH：** 因为人类不扩展。人类之间的沟通肯定不扩展。你基本上每次添加新节点时得到网络成本效应，它指数上升。

这也许是为什么我变得如此喜欢在Basecamp没有经理的关键事情，因为我们的默认团队大小是二。一个程序员，一个设计师，一个功能。当你在那种规模水平上操作时，你不需要复杂性。你不需要高级方法论。你不需要多层管理，因为你可以只是做。小团队的魔力是他们只是做。

他们不必争论，因为我们不必设定方向，我们不必担心路线图。我们可以只是坐下来制作一些东西，然后看它是否好。当你可以逃脱只是制作东西时，你不必计划。

如果你可以摆脱计划，你可以跟随从代码，从产品，从你在那一刻工作的东西中出现的真理。当你落后一步时，你对伟大的下一步是什么知道得更多，而不是如果你试图提前18个月映射所有步骤。我们如何从这里到非常远的地方？你知道什么？那很难提前想象，因为人类在那方面非常糟糕。

也许AI有一天会比我们好得多，但人类可以迈一步或把一只脚放在另一只前面。那不那么难，那允许你摆脱所有那种复杂性。所以过程变得简单得多。你需要更少的人，它复合。你需要更少的过程。

你需要在会议中浪费更少时间。你可以只是度过这些长时间光荣的日子和周，不间断的时间解决你关心的真正问题，那些有价值的，你会发现那是市场实际想要的。没有人因为背后有巨大公司而购买某些东西。大多数时候。他们购买某些东西因为它好。

你得到好东西的方式是你不坐着开会。你尝试东西。你构建东西。

**Lex：** 真的是有点令人难以置信，一个人，诚实地一个人可以在100小时的深度工作，专注工作中做什么，甚至更少。

**DHH：** 所以我会告诉你这个，我准确地跟踪了我在Basecamp第一版上花费的小时数。

我这样做是因为当时我在为杰森做合同工作，他付我钱，我要说每小时15美元。那是我们刚开始时我得到的报酬。我认为他已经将我的工资提高到光荣的25美元。但我在向他开账单，我知道Basecamp第一版的发票是400小时。那是2004年一个唯一个人创建整个系统所需要的，然后继续总收入数亿美元并继续做得非常好。

一个人只是我设置一切。那个故事的一部分是Ruby。那个故事的一部分是Rails。但很多也只是我加杰森，加瑞安，加马特。那是当时整个公司。我们可以用如此小的团队创造持续价值的东西，因为我们是小团队。不是尽管如此。小不是垫脚石。

这是人们进入他们头脑的另一件事。这是"重新工作"的大主题之一。它给企业家许可拥抱成为小团队。不是作为路点，不是像我试图成为一千人。不，我实际上喜欢成为小团队。小团队更有趣。

如果你问几乎任何人，我确信Tobi也会这样说，即使在他的规模上，构建某些东西的纯粹享受是与小团队一起构建它的享受。现在当你有巨大公司时，你可以在不同规模上产生影响。我完全认识到那个，我看到它的吸引力。

但在实际构建事物中，总是小团队，总是。

**Lex：** 你如何保护小团队。Basecamp成功地保持小。一直是龙，你必须击退。那就像基本上你赚很多钱。有增长的诱惑。所以你如何不增长？

**DHH：** 不要接受风险投资。

**Lex：** 好吧，那是第一步。

**DHH：** 那是第一点。

**Lex：** 首先，

**DHH：** 第二点每个人都接受风险投资。

**Lex：** 所以你已经去了。

**DHH：** 我的意思是那一直是最长时间的答案。因为问题不只是风险投资，它是其他人的钱。一旦你接受其他人的钱，完全可以理解，他们想要回报，他们更愿意有尽可能大的回报。因为不是他们坐在代码中。

不是他们从构建某些东西中得到日常满足。从编辑器中凿出美丽的代码诗，对吧？他们不得到那种满足。他们得到满足也许从看到一些好东西放入世界，那是公平的。但他们肯定也从更高回报中得到满足。

肯定在风险投资中有这种感觉，在风险投资中声明，你接受钱的整个点是达到十亿美元或更多。现在那个的路径通常确实通过运行既定的剧本，然后当涉及软件时，企业销售剧本是那个剧本。

如果你正在做B2B软件SaaS，你会试图找到产品市场契合。你有它的第二个，你会放弃你的小型和中型账户，用巨大的销售力量追逐大鲸鱼，到那时你是一千人，生活糟糕。

**Lex：** 话虽如此，我的意思是人们只是对此好奇。我有机会了解杰夫·贝佐斯。

他投资了Basecamp，不是控制？

**DHH：** 他买了二级市场。所以这是有趣的事情，当投资有这两个双重含义。通常当人们想到投资时，他们认为你投入增长资本，因为你希望业务雇佣更多人做更多研发，这样他们可以增长更大。贝佐斯实际上没有这样做。

他直接从杰森和我那里购买了所有权股份，那次购买的100%收益进入了我和杰森的银行账户，个人银行账户。没有一分钱进入公司账户，因为我们不需要钱来增长。我们需要的或我们肯定享受的是在某种程度上也许信任投票，但更多的是从桌子上拿一点的安全性，是我们敢于拒绝风险投资的大钱。它本质上是对想要从想要将公司带到我们不想去的巨大的人那里接受更大支票的疫苗

从那些然后想要将公司带到我们不想去的巨大的人。所以杰夫给了杰森和我足够的钱，我们舒适地拒绝所有这些人，以这样的方式，如果它在六个月后翻肚皮，我们不会踢自己并说，我们有值得数百万的东西，现在我们什么都没有，我必须再次担心租金和杂货。

**Lex：** 这是信任投票。

我想知道从，我很想听到杰夫的故事的一面，就像为什么，因为他不需要钱。所以它真的，我认为它可能只是相信人们并想要在世界上创造酷东西并从中赚钱，但不像。

**DHH：** 100%杰夫的动机不是回报，因为他实际上有一个团队。他的私人办公室运行这些投资，他们对我们给他的投资推介做了计算，这如此荒谬，以至于杰森和我在写下我们的指标时笑得屁股都掉了。我想，没有人会付这个。没有人会给我们这个收入金额的倍数。那很好。

我的意思是我们接受电话本质上出于对杰夫·贝佐斯甚至想看我们的敬畏，就像，你知道什么？我们不想要风险投资。我们不需要其他人的钱，但就像让我们给他一个没有理智的人实际上会说是的胡说八道数字。然后我的意思是，我们可以各自走自己的路。他的投资团队说，"杰夫，没门。

这在经济上根本没有意义。他们要求太多钱，收入太少。"杰夫只是说，"我不在乎。我想投资这个家伙。"因为对他当时来说，这是零钱，对吧？就像杰森和我每人得到几百万美元。

我的意思是无论那天日元和美元之间的货币波动可能为他的净值移动了我们投资的10倍。杰夫似乎真正有兴趣在有趣的人，有趣的公司周围，帮助某人走距离。我实际上回顾那种关系有一些遗憾，因为我以我有点羞耻的方式理所当然地接受了那种信任投票，多年来我对亚马逊做的一些事情更加批评，我现在觉得是有道理的。所以那只是处理它的一部分。

但在经济意义上，他给了我们那种信心。他给了我们经济信心，但然后他也给了我们也许当时运行美国最重要互联网业务的CEO的信心。出现在我们的电话中，我们会与他有，就像一年一次，基本上只是说，是的，你们正在做很棒的东西。你们应该继续做很棒的东西。我读了你们的书，它很棒。

你们启动了这个东西，它很棒。你们应该做更多那个。我实际上不知道如何运行你们的业务。你们知道它。

**Lex：** 所以书出去了。我只是如此，从粉丝角度，我对杰夫·贝佐斯如何能够看到好奇，因为对我来说，你和杰森，就像特别是技术空间的人类。

杰夫能够看到那个的事实，对吧？看到那个有多难？

**DHH：** 他肯定很早就看到了它。我认为这是杰夫比几乎任何其他人都做得更好的事情。他如此提前发现那个机会，在任何其他人甚至睁开眼睛看它之前，或者肯定他愿意如此早期和如此努力地押注它，比任何其他人都是。

他只是一次又一次地正确。我的意思是，我们不是他做的唯一投资。肯定亚马逊有极其长期的愿景。比我曾经有勇气保持的要长得多。就像我认为自己是长期思考者。与杰夫正在玩的游戏相比，我在玩孩子的游戏。

就像当我看亚马逊在网络繁荣和萧条周围的经济学时，它们看起来荒谬。就像他们失去如此多钱。他们如此被市场讨厌，他们...没有人相信它会变成现在的样子，但杰夫做了。以那种我真正渴望的信念水平。我认为那是我从那种关系中带走的主要事情之一，你可以相信自己到那种程度，对抗那些几率。那是荒谬的。

他在如此多倍我们的水平上做了那个，如果我怀疑自己，那是可悲的。

**Lex：** 是的，我认为亚马逊是那些公司之一。我的意思是，它多年来受到一堆批评。这是关于人类的事情，我们不那么欣赏，我们理所当然地接受一个东西带来的积极，真的很快，然后我们只是开始批评那个东西。这是飞机上的Wi-Fi。

**DHH：** 那正是它。

**Lex：** 但我认为亚马逊，可以说亚马逊是过去一百年最伟大的公司之一

**DHH：** 当然，我认为这是一个容易的案例。我也认为的是，成为过去一百年最伟大公司之一你付出的代价是很多诋毁者，很多推回，很多批评，这实际上是宇宙中恢复的秩序。

我在互联网上一直以来最喜欢的老师之一是凯西·塞拉。我不知道你是否知道她的工作，但她实际上在残酷的互联网赶走她之前的几年是Vern。但她写了一个叫做创造热情用户的博客，她在我的大脑中刻下了这个宇宙平衡的概念。

如果你正在创造很多人喜欢的有价值的东西，你必须创造一个相等和相反的仇恨者力量。你不能有喜欢你做的事情的人而不也有讨厌你做的事情的人。从那个逃脱的唯一方法是平庸。如果你如此无聊和如此无趣，以至于没有人在乎你是否存在，是的，你不得到仇恨者，但你也不得到真正享受你的工作的人的影响。

我认为亚马逊只是在大规模上，对吧？他们为技术为商业带来如此多价值和变化，他们必须简单地有黑洞大小的仇恨者，否则宇宙简单地会翻倒。

## 关于合作伙伴关系的讨论

**Lex：** 让我问你关于小团队。所以你多次提到杰森，杰森·弗里德，你们已经是合作伙伴很长，很长时间。

也许可以公平地说他更多在设计业务方面，你就像技术，工程巫师。你们多年来创造如此多令人惊叹的产品，如何没有互相谋杀？这是合作伙伴关系的伟大故事。你能说什么关于合作？你能说什么关于杰森，你喜欢的，你从中学到的？为什么这有效？

**DHH：** 所以首先我会说我们多年来已经试图互相谋杀几次，但我认为在过去十年中要少得多。

在早期，我们的产品讨论如此激烈，以至于当我们在办公室有它们并且周围有其他员工时，其中一些人合法地担心公司即将分崩离析。因为从房间出来的音量会如此高，听起来如此尖刻，以至于他们合法地担心整个事情会分崩离析。但你知道有趣的是，在那一刻它从来没有感觉像那样。

它总是感觉像只是对更好东西的顶峰有力搜索。我们能够承受那种关于想法优点的对抗水平，因为它是关于想法。它不是关于人，它从来没有真正变得个人。不是甚至从来没有真正，它没有变得个人。它不像，杰森，你是混蛋。

它像，杰森，你是白痴。你是白痴，因为你以错误的方式看这个问题。让我告诉你正确的方式来做它。

**Lex：** 作为小切线，让我说这个。一些人说，哦，可能回到这个，你有时可以在互联网上有脾气爆发等等。我从来不那样看，因为它是同样的类型。

也许我没有看到正确类型的脾气痕迹，但通常它是关于想法，它只是兴奋，热情的人类。

**DHH：** 那正是我喜欢想的。它不总是那样出现。我可以看到为什么特别是旁观者有时会看到看起来像我在追人而不是球的东西。我确实认为我试图在那方面变得更好。

但在我与杰森的关系中，我认为它工作得如此好，因为我们有我们完全信任彼此的自己独特的能力领域。杰森信任我做正确的技术决定。我信任他做正确的设计和产品方向决定，然后我们可以重叠并在业务上，在营销上，在写作上，在它的其他方面分享。

所以那是一件事，如果你与某人开始业务，你做与他们完全相同的事情，你不断竞争谁是更有能力的人，我认为那要困难得多，要不稳定得多。所以如果你开始业务，你们都是程序员，你们都在同样类型的编程上工作，啊，祝你好运。我认为那很难。

我试图选择与设计师工作的更容易路径，我知道至少一半时间我可以只是委托给他的经验和能力并说，你知道什么？我可能有意见，我一直对设计有意见。但我不必赢得论点，因为我信任你。现在偶尔我们会在业务或方向上有重叠，我们都会感觉我们在游戏中有强烈利害关系，我们都对那个领域有能力声明。

但然后无论什么原因，我们也都有长期愿景，我会说，你知道什么？我认为我们在这里错了。但正如我从杰夫·贝佐斯学到的，顺便说一下，我会不同意并承诺。那是他给我们的那些早期教训之一，绝对关键，也许甚至有助于确保杰森和我已经一起工作四分之一世纪。

不同意并承诺是老时代杰夫·贝佐斯的伟大之一。

**Lex：** 我只是惊讶洋子·小野没有出现。你知道我的意思吗？就像这个世界上有如此多洋子。

**DHH：** 它可能已经发生了。如果不是部分因为我们不一直坐在彼此的腿上。我们职业生涯的大部分时间，我们甚至没有住在同一个城市。

就像我在2005年搬到美国后，我们开始时在芝加哥住了几年。但然后我搬到马里布，然后我住在西班牙，然后我住在哥本哈根。杰森和我从我们关系的基础学会了以非常有效的方式一起工作，我们实际上不必那么多交谈。

在任何给定的周，如果杰森和我花费超过两小时的直接交换和沟通，我会感到惊讶。

**Lex：** 是的，有时是你只是积累的基本人类摩擦。

**DHH：** 是的，我认为你摩擦另一个人，如果太多太长，那个人最好是你的配偶。

**Lex：** 是的，但即使在那里。

**DHH：** 即使在那里。

**Lex：** COVID真的测试了关系。

这很迷人观看。

**DHH：** 它有，我确实认为有一些分离，这有点反直觉，因为我认为很多人认为你可以有的合作越多，越好。可以来回反弹的想法越多，越好。杰森和我，无论什么原因，在职业生涯早期得出结论。绝对不是，那是完全胡说八道。

这就是为什么我们是远程工作的巨大支持者。这就是为什么我享受在我的家庭办公室工作，我可以关门，一次六小时不看到另一个人类。我不想一直与你反弹想法。我想偶尔与你反弹想法，然后我想去实施那些想法。

有太多反弹正在进行，没有足够得分，没有足够扣篮。我认为这是执行规则的伟大陷阱之一。一旦创始人将自己一直提升到执行者，他们正在做的只是告诉其他人做什么，那是他们24/7生活的领域。他们只是生活在想法领域。哦，我可以只是告诉更多人更多事情做什么，我们可以只是看到它发生。

如果你实际上必须成为实施那个的一部分，你放慢你的马。你想，你知道什么？我上周有一个好想法。我要保存我的其余好想法直到下个月。

**Lex：** 经理和执行层的人有做某些事情的诱惑，那某些事情通常意味着会议，对吧？所以那就是为什么你说...

**DHH：** 他们的工作是告诉其他人做什么。

**Lex：** 是的，会议，所以这是你反对的大事情之一是会议...

**DHH：** 会议是有毒的。这真的我认为与杰森和我联系。如果我必须计算我们在24年合作中有的会议总数，我们亲自坐在彼此面前并讨论主题，我可能它会少于无论什么，粉丝公司的三个月。我们只是没有那么多做那个。我们没有磨损它。

这是特朗普在某个时候想出的有趣隐喻之一，人类在他们的生活中有有限数量的步骤，对吧？就像那是这里的长寿论点。你可以做如此多活动，然后你用完。在那个想法中有一些内核可以应用于关系。有一些我们可以有的交换量。

有一些我们可以一起度过的时间量，你可以磨损它。杰森和我勤奋地不磨损彼此。我认为那绝对是关系长寿的关键，结合那种信任水平。然后只是结合我们真正喜欢工作本身的水平。我们不只是喜欢头脑风暴。

说我们只是想出好想法的地方。不，我们喜欢做想法，我们喜欢直接自己成为那个过程的一部分。我喜欢编程，他喜欢做设计。我们可以去做我们的小事情长时间。在情况下你，聚在一起说，嘿，让我们启动一个伟大产品。

**Lex：** 这可能听起来像我要求你做治疗，但我发现自己有时想要或渴望会议，因为我孤独。就像因为远程工作只是独自坐着。我不知道，长时间它可能变得真的孤独。

**DHH：** 让我给你一个提示，找个妻子。

**Lex：** 是的，哦，该死。

**DHH：** 找几个孩子。

**Lex：** 好吧。

**DHH：** 就像家庭真的是孤独的伟大解药。

我尽可能真诚地意味着那个。我肯定在我职业生涯早期有你描述的完全那种感觉，当我远程工作时，我只是一个，就像我住在公寓里。一个完全刻板印象，很长时间当我第一次搬到芝加哥时，我在地板上只有一个床垫，然后我买了这个大电视，我甚至没有安装它。然后我有一堆DVD。

我基本上，我工作很多时间，然后我只是回家做那个。那不太好。它真的不是。就像我确实认为人类需要人类。如果你不能在工作中得到他们，我实际上有点不想在工作中要他们，至少我不想要他们一周40小时。那不是我喜欢的。

你需要其他东西。你需要生活中的其他关系，如果你能找到你实际上只是想花很多时间的某人，没有更大的关系深度。那是它的关键。我认为对杰森和我来说，我们相当长时间有家庭是关键，它以这样的方式让你们两个扎根，创业的冲刺可以被交易为持久公司的马拉松。你以某种方式安定下来。我们简短地谈论了有时我被激怒。

我的意思是很多时候，也许甚至大多数时候我对主题被激怒，但我现在不像我24岁时那样以同样的方式被激怒。我仍然对想法和试图找到正确的事情极其热情，但有家庭会议，我的妻子，围绕那个建立生活只是以完全陈词滥调的方式让一切平静下来。

但我认为它实际上是关键。我认为如果我们能让更多甚至年轻人不等到他们在该死的30多岁后期或40多岁早期才与某人结合，我们会更好，我们也会有更稳定的商业关系，因为人们会在其他地方得到那种培养人类关系。

现在当我说所有那些时，我也接受有很多伟大业务多年来已经建立，没有远程建立，已经由一群流氓坐在办公室无数小时建立。我的意思是，约翰·卡马克和蒂姆·斯威尼都在90年代谈论他们的职业生涯，那基本上只是工作，睡觉，与办公室的家伙闲逛，对吧？完全公平。那从来没有吸引我。

杰森和我都在这个想法上看法一致，每周40小时专门用于工作是足够的，如果我们要走距离，不只是建立VC案例到退出需要的5到7年。但可能10年，20年或更远，我们需要成为完整的人类。因为只有那种完整的人性会走距离，包括在工作之外建立友谊，有爱好，找到伴侣并有家庭。

那整个更高存在，工作不是生活中唯一事情的凳子的那些腿完全与我们已经存在25年的事实相关。有太多，特别是在美国的虚假权衡。哦，你想建立成功业务？好吧，你可以有金钱享受或家庭或健康选择一个。

什么？为什么我们必须放弃所有这些？现在再次，我不是说，生活中有你可以冲刺的付费时刻。但我说如果那个冲刺变成十年，你会为它付出代价。你可以以方式为它付出代价。我一次又一次看到似乎是非常糟糕的交易，即使它有效。顺便说一下，大多数时候它没有。大多数时候创业公司破产。

大多数时候人们花费五，七年或没有成功的东西，他们不得到支付，然后他们只是坐着后悔，我的20多岁发生了什么？早期，杰森和我基本上达成协议，一起工作不会导致那种后悔。我们会允许自己和彼此在工作之外建立完整生活。

那个有效的事实是我感觉几乎像禁忌知识，肯定在美国的技术圈子中。这是我们试图倡导20年的东西，我们仍然得到松懈。就在两天前，我与某人有另一个Twitter争吵，说，"哦，好吧，也许它有效，但你没有变成Atlassian。所以你是失败。

Basecamp不是Jira，所以你为什么甚至打扰？"这是如此迷人的赢家通吃心态，除非你在所有方式中主宰其他人，你已经失败了。当生活的如此多对多个赢家更开放，我们可以最终得到多年来赚了数亿美元的业务，我们保留了大部分来做我们想要的任何事情，那就足够了。那很好，那很棒。那实际上是值得渴望的东西。

肯定它应该是某人考虑选择的路径，而不是主宰一切的VC独角兽或破产心态。

**Lex：** 是的，我想问你关于这个交换，所以你可以向我解释整个传奇，但只是稍微链接那个，我认为有一个概念，技术创始人的成功就像全力工作几年然后退出，有点为，我不知道，数亿美元出售你的公司。那是成功。当在现实中似乎，当你看像你这样的人，

真正聪明，创造性的人类，他们实际上是谁以及快乐需要什么。它实际上需要一生稍微工作。就像因为你实际上喜欢编程，你喜欢建造，你喜欢设计师，你不想退出。那是你真正雄辩地谈论的东西。

所以就像你实际上想创造一个生活，你总是在做建造，以不完全接管你生活的方式做它。

**DHH：** 莫吉托岛是海市蜃楼。它总是。对雄心勃勃的人没有退休。没有只是坐在海滩上啜饮莫吉托什么？两周前你变得该死疯狂并想回到行动中？那正是对大多数有能力建立那种退出的人发生的事情。

我从来没有看到，我不应该说从来。我几乎从来没有看到任何人能够做到那个。然而如此多人认为那就是他们为什么这样做。那就是他们为什么牺牲一切。因为一旦我到达终点线，我是金色的。我赢了，我可以退休，我可以坐回去，我可以只是放松。你发现那种放松实际上是地狱。

对创造性人来说，浪费他们上帝给予的创造性汁液和能力是地狱。我真的很幸运早期读了米哈伊·奇克森米哈伊的书"心流"

**Lex：** 好的，发音，完美。

**DHH：** 你知道什么？我必须在过去几天与AI练习那个，因为我知道我要引用他，我几次屠杀了他的名字。

所以AI教我如何发音那个，至少有点正确。但他职业生涯的主要工作本质上是心流概念，来自寻找理解快乐的搜索。为什么一些人快乐？他们什么时候快乐？他学到的相当启发。

他学到人们在莫吉托岛上坐着时不快乐。当他们摆脱所有义务和责任时，他们不快乐。不，他们在这些时刻快乐，他们正在达到和伸展他们的能力刚好超过他们目前能做的。在那些心流时刻，他们可以忘记时间和空间。

他们可以坐在键盘前编程一个困难问题，认为20分钟过去了，突然已经三小时了。他们以最大快乐量回顾那些时刻。那就是顶峰快乐。如果你拿走对那种问题的追求，如果你从你的盘子中消除所有问题，你会抑郁。

你不会有好时光。现在有人可以做那个，但他们不是建立这种公司的同样类型的人。所以你必须接受你是什么样的个人。如果你在这条路径上，不要胡说自己。不要胡说自己认为，我只是要牺牲一切，我的健康，我的家庭，我的爱好，我的朋友，但在10年中我要弥补一切，因为在10年中我可以做它。它从来不那样工作。

它在它的两端都不工作。如果你成功并出售你的公司，它不工作，因为你会在退休两周后无聊得发疯。如果公司失败，你后悔最后10年为了什么都没有而花费，它不工作。如果一切都工作，你留在业务中，它不工作，因为它从来不变得更容易。

所以如果你只是说，只有工作，没有其他，你会在所有指标上失败。我不想要那个。我想要心流的快乐。我理解那种洞察是真的，但我想以我可以持续旅程40或50年的方式做它。

**Lex：** 还有另一个有趣的警告，我听你说过，如果你确实退出，你出售你的公司，你想留在，你想做另一个公司，那通常不会那么充实。因为真的你的第一个婴儿，就像。

**DHH：** 你不能再做它，或大多数人不能再做它。A，因为他们的第二个想法不会像第一个那样好。在瓶子中捕获闪电如此罕见，就像我们例如与Basecamp有的。

我从经验中知道这个，因为我一直试图从那时起建立很多其他业务。其中一些是适度成功，甚至好成功，它们都没有是Basecamp。两次做那个真的困难。但创始人是傲慢的刺，包括我自己。我们喜欢认为，你知道什么？我们成功很大程度上因为我们只是很棒。我们只是比其他人好得多。

在某些方面，那有时是真的。但你也可以真的擅长在热门时刻重要的东西，那扇门是开的。门现在关闭，你仍然擅长那个东西，但它不重要。没有人关心。有那个部分。然后有它的部分，回到第一次体验事情只在第一次发生。你不能再做它。

我不知道我是否有它在我身上再次经历早期的胡说八道。我以最亲爱的意义说胡说八道。做它都很棒。我知道太多。这是为什么，每当我被问问题，如果你能告诉你年轻的自己真的会的东西，你会对你年轻的自己说什么？我他妈的不会说任何事情。

我不会抢夺我年轻自己的所有生活经历，我由于对世界如何工作的无知而被祝福。建立关于世界如何工作的智慧是快乐。你必须一次一个破坏地建立它，如果你只是交给所有结果。就像，哦，我们应该看你的电影吗？这是它如何结束。我现在不想他妈的看电影。你破坏了它。我不想你破坏我的商业经历。

我不想破坏我的任何无知。当你开始新东西时，一半时间最大的祝福是A，你不知道它会有多难；B，你不知道你不知道什么。就像冒险是要付出，责任是要付出。这是乔丹·彼得森真正教我表达的东西。

这个概念，责任实际上对我是关键。"人类寻找意义"，维克多·弗兰克尔也谈论这个，我们可以忍受任何困难，如果有为什么的原因。现在他在真正改变生活的集中营中谈论它。但你也可以在较小规模上应用，较少关键性，甚至只是你的日常生活，所有那些建立原始业务的困难，那是你承担的责任，吸引力。

你承担那个的原因部分是因为你不完全知道它需要什么。如果我提前知道，如果我提前知道，它会有多难？沿途会有多少挫折？如果你只是在我开始之前在叙述中告诉我那个，我会像，呃，也许我应该只是去找工作。

**Lex：** 你在那里说了如此多聪明的事情。

只是选择一个。有趣的是，有时建议给予者，智慧给予者已经经历了所有胡说八道。所以有一定程度你想犯错误。所以我认为我仍然会给建议，你想有你生活的一段，你工作太努力，包括失败的事情。

我不认为你可以以任何其他方式学习为什么那是坏想法的教训，除了通过做它。有一定程度，但当然你不。

**DHH：** 我认为你应该伸展。你应该必须伸展十年吗？我不那么确定。

**Lex：** 是的，十年事情是20多岁是特殊时间。

**DHH：** 这是很多交易。你不会得到你的20多岁回来。

是的，你不会得到你的30多岁回来。你不会得到你的40多岁回来。真的，我个人会后悔，如果我没有做我在20多岁做的其他事情，如果我没有我有的乐趣，如果我没有我有的朋友，如果我没有建立我做的爱好，如果我没有在足够早的年龄开始驾驶赛车以实际变得真的擅长它，如果我只是全力投入业务，因为我会在最后得到同样的。

这是德里克·西弗斯真正教我的东西，他有这个关于当他去骑自行车时的伟大文章，他可以真的努力全力，他可以我认为在无论什么19分钟内做骑行。或者他可以享受骑行，慢5%，在21分钟内做骑行，意识到只有两分钟分开。

要么我一直全力，没有其他，我在alt完全疲惫。或者我旅行同样距离，我也许晚两分钟到达，但我得到享受风景，听鸟，闻花。那个旅程也有价值。

现在我说那个，同时接受和庆祝，如果你想成为世界上一件事的最好，不，你必须牺牲一切。你必须只对那件事着迷。没有世界上某件事最好的人不完全着迷的例子。我不需要成为任何事情的最好。

这是我早期有的谦逊的罕见祝福，就像，你知道什么？我不那么聪明。我不那么好。我不那么有才华。我可以通过结合我知道的不同方面和元素做有趣的事情，但我不会成为任何事情的最好。那从这种单一痴迷中释放了我，只是说，我要成为世界上最好的程序员。呃，我知道我不是。我在甚至得到条件如何工作之前失败了两次。

我不够聪明成为任何事情的最好。我不够专注做那个。那有点祝福。我认为作为社会，我们必须跨越庆祝顶峰卓越，我们一直做，庆祝成为那个需要的使命的顶峰强度。然后也说，你知道什么？我们都不需要成为迈克尔·乔丹。

只会有其中一个。

**Lex：** 好吧，我们应该说有某些追求需要单一痴迷。篮球是其中之一。顺便说一下，可能赛车。如果你想成为世界上F1的最好...

**DHH：** 如果你想成为塞纳，你必须是疯子。

**Lex：** 但我会争论大多数学科像编程允许，如果你想成为，引用，"最好"，无论那意味着什么，我认为那在你生活结束时被判断。通常如果你看那条路径，它会是非线性的。它你不会看起来像奥林匹克运动员的单一焦点生活。

会有20多岁的一些酸，或者会有几个绕道，真正的伟大。会有绕道。有时他们不会是史蒂夫·乔布斯资产类型的情况。他们只是你工作的不同公司，不同职业或你分配生活的不同努力。但它会是非线性的。它不会是单一焦点。

**DHH：** 我有时想这个的方式是我想要学习的好交易。我可以成为我定义为擅长某事的前5%，容易得多。也许它是20倍容易，100倍容易进入前5%，比进入前0.1%。那几乎不可能难进入那个。

但如果我满足只是在前5%，我可以一次在五件事的前5%。我可以真的擅长写作。我可以在驾驶赛车上变得体面。我可以变得相当擅长编程。我可以运行公司。我可以有家庭。我可以同时做很多事情。那给我那种几乎被理想化的卡尔·马克思有这个想法的多样性，"哦，我要在早上钓鱼，在晚上锤击，在周末画画，对吧？"对我至少有一种感觉，他的异化诊断是真的。那只是那种隧道视野。只有这一件事我只是要专注于那个

给我一种我不能忍受的异化感。当我真的深入编程，有时我深入几周，也许甚至在少数情况下几个月，我必须出来透气，我必须去做其他事情。就像，好吧，那是今年的编程。

我已经做了我的部分，我要去骑行或在互联网上惹恼人们或驾驶一些赛车做其他事情。然后我可以明年再次以全强度做编程事情。

**Lex：** 说到在互联网上惹恼人们，你必须向我解释这个戏剧。好吧，所以这个说的家伙是什么，想象输给Jira但吹嘘你每年有几百万美元。

所以这与这个现在几乎模因决定离开云有关。DHH离开了云。我认为那字面上是模因，但它也是迷人的决定。你能谈论DHH离开云的完整传奇吗？

**DHH：** 是的。

**Lex：** 离开AWS节省金钱。我猜这个人现在正在做的案例。

**DHH：** 是我们浪费时间优化一个如果我们只是追求月亮可能大100倍的业务。

**Lex：** 追求月亮包括？

**DHH：** 风险投资。

**Lex：** 但也...

**DHH：** 以及不关心成本的其他事情。

**Lex：** 但也因为AGI在拐角处，你应该一直投资AI，对吧？这只是部分？

**DHH：** 有点坦克生姜？我认为这是有点混乱的论点，但如果我们只是在它的顶峰理想中接受它，我实际上认为这是合理的点，是你可以近视地专注于数便士，当你应该专注于获得，对吧？我通过离开云优化了我们在基础设施上的支出

那花了一些时间，我可以花那个时间制作更多功能，会吸引更多客户，或花更多时间与AI或做其他事情。机会成本是真实的。我不否认那个。

我推回的想法是，对我们规模的公司每年在我们的基础设施账单上节省200万美元，大约在一半到三分之二之间直接到底线，这意味着它是对杰森或我作为所有者和我们员工的利润分享计划的一部分的回报，完全值得做。

这个成本不重要的想法是非常硅谷的思维方式，再次，在某种规模的东西上理解也许，但我也实际上认为它在美学上不令人愉快。我发现低效业务，就像我发现充满线噪声的低效程序只是我大脑中的刺。我讨厌看费用报告，只是看到不成比例的浪费。

当我几年前回顾我们在37signals的支出时，我看到没有通过我的嗅觉测试的账单。我记得我们在云之前曾经在基础设施上花费多少，我看到与我们需要的不成比例的数字。

计算机随着时间变得如此快，事情不应该变得更便宜吗？为什么我们花越来越多钱服务更多客户？是的，但用更快的计算机，摩尔定律应该降低成本，相反的事情正在发生。为什么那发生？那开始了解开为什么云不像人们喜欢认为的那样伟大交易的旅程。

**Lex：** 是的，我们能看具体情况，只是为了不知道故事的人，然后概括它对云和技术业务的角色意味着什么。所以具体情况是你使用AWS S3为...

**DHH：** 我们为一切使用AWS。HEY.com作为完全云应用启动。它完全在AWS上用于计算，用于数据库，用于所有它。

我们使用所有系统，因为他们最好规定我们应该。我们与AWS的总云账单，我们的总支出我认为在其顶峰是320万或340万。那有点很多钱。340万，我的意思是我们有大量用户和客户，但仍然那只是让我觉得不合理。

它如此不合理的原因是因为我耳朵里响着云的推销。嘿，这会更快。这会更容易。这会更便宜。你为什么试图生产你自己的电力，对吧？就像，你有你自己的发电厂吗？你为什么要那样做？把计算机留给超大规模者。他们无论如何都更擅长它。我实际上认为那是令人信服的推销。

我在那个推销上买了几年，想，你知道什么？我再也不拥有服务器了。我们只是要渲染我们的容量，亚马逊会能够为我们提供比我们自己能买的便宜得多的服务，因为他们会有这些规模经济。我想着杰夫的话响起。我的竞争对手的边际是我的机会。那是他曾经用来驱动amazon.com的东西。

如果他可以只是赚2%，当另一个家伙试图赚4%时，他会最终得到所有钱。在量上，他仍然会赢。所以我认为那是AWS的操作精神。结果那根本不是真的。顺便说一下，AWS操作它几乎40%的边际。

所以只是在那个中，有一个线索，竞争对手不能做我们喜欢资本主义的竞争事情，即降低成本等等。所以在我的光学中，云推销，它根本上是虚假的。首先它没有变得更容易。我不知道你最近是否使用过AWS，它是地狱复杂。

如果你认为Linux难，你从来没有试图设置IAM规则或访问参数或AWS的任何东西。

**Lex：** AWS总是困难。它总是复杂。

**DHH：** 好吧，我认为它变得更困难。但是的，现在其中一些是它困难，因为它非常有能力，你有一堆容量在手。有原因我不认为它们足够好来证明整个jing-a-ma-jing变得如此复杂。但肯定真实的是它不再更容易。

使用AWS不比运行你自己的机器更容易，我们当我们拉出云并没有雇佣单个额外的人时学到了。即使我们操作我们所有自己的硬件，团队保持完全相同。所以你有这个三方推销，对吧？它会更容易，它会更便宜。

肯定不更便宜。我们刚刚通过将我们在基础设施上的支出削减一半到三分之二证明了那个。它会更快。最后一点是真的，但太多人高估了那种速度的价值。如果你需要一千台计算机在接下来的15分钟内在线，没有什么击败云。

你甚至如何采购那个？如果我们只是需要另外20台服务器，得到装在托盘上的盒子运送到数据中心并拆包和机架，所有那些东西，对吧，会花一周或两周？但我们多久需要做那个？如果购买那些服务器便宜得多，我们得到相同金额的钱的大量更多计算，我们多久需要做那个？

我们可以只是购买更多服务器，甚至不关心我们在计算实用程序上不是超优化的事实吗？我们不必使用像自动缩放这样的东西来弄清楚事情，因为我们必须减少成本？是的，我们可以。所以我们经历了这个旅程，在2023年早期的实现，当我最终受够了我们的账单。

我想摆脱它们。我想花更少钱。我想自己保留更多钱。在刚好超过六个月中，我们将七个主要应用程序从云中移出，在计算缓存数据库方面到我们自己的服务器上工作。一个光荣，美丽的新舰队从服务器之王迈克尔·戴尔购买，顺便说一下，他真的是我的另一个偶像，我看到他刚刚庆祝了41年的业务。

41年这个人一直在销售我们整个存在一直使用的很棒服务器。但无论如何，这些托盘在几周内到达，我们机架它们并让一切运行。我们出来了。至少与计算部分。然后我们有长期多年承诺S3，因为顺便说一下，在云中获得体面定价的唯一方式不是按日购买，不是按数据库基础租用，而是将自己绑定到多年合同，计算通常一年。

那是我们的情况，存储是四年。我们签署了四年合同，将我们的客户文件的PB存储在云中，能够得到只是中途体面负担得起的东西。所以所有这些项目聚集到我们现在字面上节省数百万美元的感觉，预计五年约1000万。它总是困难。

你如何准确地做会计，TOC这个，那个和其他东西。但它是数百万美元。但不只是那个。它也是离开云意味着回到互联网的更原始想法的事实。互联网不是这样设计的，三台计算机应该运行一切。

它是分布式网络，这样个别节点可以消失，整个事情仍然会继续。DARPA设计了这个，这样俄国人可以拿下华盛顿，他们仍然可以从纽约反击，整个通信基础设施不会消失，因为没有中心和辐条。它是网络。我总是发现那是极其美丽的愿景。

你可以有这个光荣的互联网，没有单个节点控制一切。我们已经回到更多单个节点控制一切的想法与这些超大规模者。当US-East-1，AWS的主要和原始区域离线时，多年来已经发生了不止几次。似乎互联网的三分之一离线。

就像那本身只是对DARPA设计的侮辱。它只是从AWS建立的是奇妙的事实中减损。我认为云已经将如此多事情向前推进如此远，特别是围绕虚拟化，自动化设置。它是系统管理的所有那些巨大向前，现在允许我们能够以闻起来和感觉很像云的方式在本地运行事情，只是以一半成本或更少，以及拥有硬件的自主性和满足感。

我不知道你上次看像实际服务器并拆开它并看里面是什么时候。这些东西是华丽的。我的意思是，我发布了我们在数据中心的机架的几张图片，人们总是为它们疯狂，因为我们在这个云时代已经变得如此抽象，从底层金属看起来像什么，大多数人没有想法。他们不知道现代CPU有多强大。

他们不知道你可以在一个U机架中装多少RAM。计算进步真的令人兴奋，特别是我会说在过去四到五年中，TSMC在苹果的帮助下真正推动了信封。我的意思是，我们有点坐在那里一段时间，而英特尔在旋转他们的轮子无处可去。

然后TSMC与苹果推动他们真正向前移动事情。现在服务器又令人兴奋了。就像你年复一年得到跳跃，15，20%而不是我们被困的单数字。那都意味着拥有你自己的硬件是比以往任何时候都更可行的命题。

你需要更少机器运行更多，更多人应该做它。因为尽管我爱杰夫和亚马逊，就像他不需要另一个无论什么，我购买运行我们业务的所有技术东西的40%边际。这只是我一直专注的东西。既因为围绕尊重DARPA原始设计的意识形态，运行我们自己硬件的实用性，看我们可以用最新机器推动事情多快，然后节省钱。

那都如此享受做，但也对很多人如此反直觉，因为似乎，我认为对行业中很多人来说，就像我们都决定我们完成了购买计算机，那是我们只是委托给AWS和Azure和谷歌云的东西，我们不必再拥有这些东西。所以我认为对一些人有一点鞭打，"哦，我认为我们同意了。

我们完成了那个。"然后我们来说，"啊，你知道什么？也许你应该有计算机。"

**Lex：** 运行你自己的服务器有一些痛点吗？

**DHH：** 哦，很多。操作各种计算机有痛点。你试过只是像这些天使用个人计算机吗？一半时间当我的孩子或我的妻子有问题时，我说，你试过只是关闭并再次打开吗。计算机对人类本质上是痛苦的。

拥有你自己的计算机虽然有点让一些那种痛苦值得。有责任伴随实际拥有硬件，对我，至少让操作那个硬件的负担似乎稍微更享受。现在有你必须学习的事情，肯定在我们的规模也。

我的意思是，我们不只是购买单个计算机并插入以太网。我们必须有机架和机架与它们，你必须用网络电缆设置它，在那个中有一些专门专业知识，但它不像那个专业知识像建造核火箭。它不像它不广泛分布。

字面上整个互联网建立在人们知道如何将计算机插入互联网，对吧？哦，以太网电缆去这里，电源电缆去这里，让我们启动Linux。那是每个人在10，12年前云有点接管之前将任何东西放在线上的方式。所以专业知识在那里，可以重新发现。你也可以学习如何操作Linux计算机。

**Lex：** 是的，当你得到一堆它们时，有一堆闪烁LED，它只是如此令人兴奋。

**DHH：** 哦，它们美丽，平静，令人惊叹。计算机真的有趣。这实际上是我在我们搬出云后甚至更深入的东西。现在我的下一种刺痛是，如果你可以搬出云，你也可以搬出数据中心吗？个人服务器变得真的可怕地快和高效，个人互联网连接与我们只是十年或二十年前连接数据中心的竞争。所以有围绕这个家庭实验概念的整个社区，本质上在你自己的公寓中安装服务器硬件，将它连接到互联网，并直接向互联网暴露那个。那回到90年代的那些光荣日子，当为互联网建造的人会在他们在壁橱中的实际计算机上托管实际网站。

我对那个相当激动。我正在做一堆实验。我已经从我自己的公寓订购了一堆家庭服务器。我惊叹于我现在可以得到五千兆位，五连接，我认为，你知道五千兆位可能已经将Basecamp带到数百万MRR，以当时的方式，我在2004年技术或可能一百兆位电缆的单个盒子上运行整个业务。

就像我们在计算和连接方面有访问的容量是人们没有重新调整的东西。这有时在技术中发生，进步偷偷接近你。这与SSD发生。顺便说一下，我喜欢那个。我们围绕有某些寻求率属性的旋转金属磁盘设计了如此多我们的技术和存储方法和数据库设计。

然后我们去了NVMe和SSD，人们花了相当长时间意识到系统现在必须根本不同地建造。当你不旋转这些金属板与必须从它们读取的小头时，内存和磁盘之间的差异现在要小得多。

你本质上只是处理另一种类型的内存。我认为当涉及新业务字面上从你该死的卧室启动的容量时，我们有点在同样阶段。

**Lex：** 所以你可以用家庭实验与大用户基础走得相当远？

**DHH：** 绝对地。

**Lex：** 那令人兴奋。那就像老学校。

那真的令人兴奋，对吧？

**DHH：** 它在字面物理意义上带回车库中的创业。现在其中一些是我们需要吗，如果你不需要很多，你可以得到相对便宜的云容量。

**Lex：** 地狱是的，我们需要。我的意思是自己做那个的感觉，在你自己家中看LED灯。我的意思是没有什么像那个。

**DHH：** 有只是我完全爱上的美学，我想试图推动。现在它不会是与离开云同样的事情。我不确定我们离开云的退出不是离开数据中心的退出。我们基本上只是购买硬件，将它运送到我们甚至实际上没有触摸的专业管理数据中心。

这是人们对搬出云有的另一个误解。我们有一堆人不断驾驶到某处数据中心机架新盒子并更换死RAM。那根本不是现代世界中事情如何发生。我们有一个叫Summit的公司，以前Deft，那是我们称为白手套的。

当我们需要像"嘿Deft，你能下去并交换六号盒子中的死SSD吗？"时，他们在数据中心工作。他们做它。我们看到的类似于与云工作的某人会看到的。你看到IP地址在线。你看到驱动器在线。它不那么不同，但当你在我们的规模上操作时，它是整个地狱便宜得多。当然它是。

当然，如果你需要那些东西年而不是租用它，拥有东西更便宜。在没有其他领域中，我们会混淆那两个事情，长期拥有比租用更便宜。

**Lex：** 有一些灰色区域，就像我有机会与xAI团队一堆互动。我可能回到孟菲斯那里做与Grok发布相关的大播客。

那些人，为了实现建立集群的速度并解决与GPU，与训练有关的一些新颖方面，他们必须更亲自动手。它是较少白手套。

**DHH：** 哦，我喜欢那个，对吧？他们处理前沿问题，他们不通过从他们的主要竞争对手以巨大加价租用一堆GPU来处理它。

他们说，"不，去那个。我们要在我们自己的帐篷中放十万GPU，"对吧？并在绝对记录时间内建造它。所以我认为如果有什么，这是拥有硬件可以在小规模，中等规模和计算先锋水平给你优势的想法的证明。

**Lex：** 顺便说一下，你知道，说到团队，那些是xAI，特斯拉是大公司，但所有那些人，我不知道那是什么，你说杰夫真的擅长找到好人，在人们中看到力量。就像埃隆也极其，我不知道那是什么。实际上，我从来没有实际看到，也许你可以说到那个。他擅长找到伟大。

**DHH：** 我不认为他找到那么多，他吸引。他因为他的目标和使命的大胆而吸引人才。他陈述它的清晰度。他不必搜索地球找到最好的人。

最好的人来到他那里，因为他，这里谈论埃隆，是最单一令人振奋的人物之一。在宇宙这里的同样秩序中，仇恨者和爱好者，对吧？就像他在如此规模上产生如此影响，当然他必须有字面上数百万人认为他是世界上最糟糕的人，他也会有数百万人认为他是人类最伟大的礼物。取决于日子。我在中间某处。

但我更在人类最伟大礼物的规模端，而不是在另一端。我认为那真的以我们几乎忘记那种大胆水平如此罕见的方式激励人们，当我们看到它时，我们不完全知道如何分析它。我们认为埃隆找到伟大人才，我确信他也擅长那个。

但我也认为这个使命的灯塔，我们要他妈的去火星。我们要将交通转变为使用电力。我们要用互联网覆盖地球，如此宏大，以至于有些日子我醒来说，我他妈的在用这些待办事项清单做什么？就像耶稣，我应该去注册那样的东西吗？是的，那在某种意义上听起来令人振奋。

我只能想象像在10，50年代骑自行车回去说，我们应该去诺曼底吗？你可能在路上死去，但哦，男孩，那听起来像旅程和冒险。

**Lex：** 有几个组成部分。有一个肯定是这个比生命更大的使命并真正相信它。你知道，每隔一句话是关于火星，就像真正相信它。它真的不重要什么，就像任何其他人，批评，任何东西。有非常单一专注的大使命。

但我认为它也与一堆其他组成部分有关。就像一旦人们，一旦灯塔吸引，能够很好地雇佣。我刚刚看到在纸面上不一定有简历与记录的人。我看到真的现在结果是传奇人物。他基本上像向他们抛出领导力的球。

在他们中看到一些东西并说，"你去。"并给他们所有权，他们与它一起跑。那在每个规模上发生。有真正的精英制度。就像有一些东西，只是像，你可以在这些会议中看到人类智力的繁荣，在这些群体聚集中，他们像，能量是可感知的。对我来说，只是在那周围就令人兴奋。

没有很多公司我看到那个，因为当公司变得成功和更大时，它以某种方式窒息那种能量。我猜你在早期阶段的创业公司中看到。但就像在实际能够实现规模的大公司中看到它很酷，你知道？

**DHH：** 我认为那里的秘密的一部分是埃隆实际上知道事情。

当你知道事情时，你可以评估工作产品的质量。当你可以评估工作产品的质量时，你可以非常快速地告诉谁充满胡说八道，谁实际上会带你去火星。你可以解雇充满胡说八道的人，你可以押注让我们到火星的人。

直接评估个人能力的那种能力，它实际上有点罕见。它不在经理，雇佣经理中广泛分布。它不是你可以容易地委托给在工作本身不非常熟练的人的东西。埃隆显然知道很多关于很多，他可以闻到谁真正知道东西。

这在我们的小规模上，我试图以同样的顺序做的东西，例如，当我们雇佣程序员时，现在与AI作为新挑战会很有趣。但直到这一点，被雇佣的主要支点不是你的简历，不是你有的学校教育，不是你的成绩，不是你的血统，是你在两件事上做得多好。

A，你的求职信。因为如果他们是好作家，我只能与人们远程工作。所以如果你不能写适当的大写字母，不能费心努力专门为我们写它，你出局了。二，你必须能够真的很好地编程，到我可以看你的代码并说，是的，我想与那个人工作的程度。

不仅我想与那个人工作，当我必须在五年后再次看到它来修复一些该死的错误时，我想在那个人的代码上工作。所以我们要给你一个编程测试，模拟我们真正工作的方式，我们要看你如何做。我一次又一次地惊讶，我肯定认为这个候选人是鞋入，他们听起来正确，简历正确，然后你看到代码被交上来。我想，没门。我们绝对不雇佣这个人。

另一种方式也是真的。我说，我不知道这个家伙或这个女人。呃，我不知道。然后他们交上他们的教练东西，我想，神圣的狗屎，那个人明天可以在我的团队上，最好？评估工作产品的能力在雇佣时是超能力。

**Lex：** 有一个步骤我看到埃隆做得真的很好，就是能够出现并说，这可以做得更简单。

**DHH：** 是的。

**Lex：** 但他知道他在谈论什么。然后工程师，因为埃隆知道足够，工程师的第一反应，你有点可以告诉，就像如果你的父母告诉你一些东西，几乎像翻白眼。这不是，不，我们已经我在这上面工作了一个月，你不...

但然后当你有那个对话更多一点时，你意识到不，它可以做得更简单。找到方式。所以当两个工程师交谈时有好的，一个可能没有完美信息。但如果高级工程师有像好本能，那是像战斗赢得的，然后你可以说简化。它实际上会导致简化。

**DHH：** 我认为这是真正伟大的标志。他们不仅对做工作需要什么有洞察，而且他们也有超越工程师会做的，程序员会做的超越愿景。我认为如果我们看这些稀有性，显然史蒂夫·乔布斯的神话也是这个。

即使也许他在许多方面比埃隆技术性更少，他有同样的能力出现在产品团队并真正挑战他们更努力地寻找简化或以会从应该做它的人那里获得不信的方式让事情更伟大。就像这个家伙充满胡说八道。

就像这疯狂。我们永远不能，然后两个月后它是。所以有一些这个，你需要愿景，你需要它由知道足够关于什么是可能的现实锚定，知道足够关于物理，知道足够关于软件，你不只是建造胡说八道。有很多人可以告诉一群工程师。不，只是做得更快。就像那不是技能。

它必须锚定在一些真实的东西中，但它也会锚定在，它是疲倦的词，但对结果的热情到你如果做糟糕工作会个人受到侮辱的程度。这是我最近一直在写关于苹果的。

他们失去了那个会出现并告诉工程师他们做的不够好的混蛋，以实际上也许会让他们在那一刻感觉有点小的方式，但会激发那种真正修复它的热情。现在他们有物流人员，非常擅长采购组件和排列生产甘特图，但你不得到那种魔力。

现在那整个场景有趣的是，我实际上认为蒂姆·库克运行事情和在苹果运行事情如此长时间多好，也许我们错了。也许我们对史蒂夫·乔布斯对整个使命的关键性错了。也许你可以逃脱不有它。我认为账单会稍后来，现在它有了。

苹果在所有这些方式中失败，会炸毁史蒂夫的鬼魂并真正侮辱他的某人会说，看？这就是现在正在发生的。所以这里的另一件事，当然，是不可能离婚。

就像你对什么是系统关键组成部分的感知和生活现实中一百万不同移动部分的混乱现实。你应该一直对你自己的分析和你自己的论文持怀疑态度。

## 关于苹果的讨论

**Lex：** 既然你提到苹果，我必须问互联网上某人提交的问题。DHH仍然讨厌苹果吗？我相信问题是。

所以有一个时候Basecamp与苹果就30%开战。你能告诉那场战斗的传奇吗？

**DHH：** 是的，但首先我会告诉你，我爱上了苹果，那一直回到2000年代早期，当微软以我们现在看到苹果和谷歌主宰手机的方式主宰行业。微软在个人计算机方面只是一切，我真的不喜欢90年代的微软。

90年代的微软是切断网景空气供应的那种角色，是比尔·盖茨在与司法部的采访中坐着挑衅地询问什么的定义是什么。总的来说不愉快，我认为。你可以对所取得的成就有尊重，但我肯定不喜欢它。

正如我们谈论的，在Commodore分崩离析后，我勉强来到PC，我不能继续使用Amiga。所以我已经对PC有点骨头要挑，只是因为我如此爱我的Amiga。但然后在2000年代早期，苹果作为可信替代出现，因为他们将新一代Mac押注在Unix基础上。

那允许我从微软逃脱，突然我成为苹果最大的支持者之一。我在哥本哈根商学院的毕业班中。我从第一个白色iBook开始，第一个使用Mac的人。到我们毕业完成时，我基本上已经将班级的一半转换为使用苹果计算机，因为我会如此努力地传播它们并演示它们并做超级粉丝会做的所有事情。我多年来继续那个工作。

杰森或我实际上在我认为2004，2005年，为苹果做了一个广告，他们在开发者方面发布，我们都关于苹果对我们做的一切如此不可或缺，我们仰望他们，我们被他们启发。那种爱情关系实际上持续了很长时间。我基本上只是成为Mac人20年。我甚至不关心看PC。

对我来说，微软正在做的任何事情似乎无关紧要，那感觉如此解脱。因为在90年代，我感觉我不能逃脱微软，突然我找到了我的逃脱。现在我与苹果在一起，它是光荣的，他们分享如此多我的感性和我的美学，他们继续推动信封，有如此多要自豪的，如此多要仰望的。

然后那有点开始与iPhone改变，这很奇怪，因为iPhone是让现代苹果的。它是我在2007年与杰森排队五小时站在队伍中购买第一代产品，苹果员工会在你走出商店时为你鼓掌。我不知道你是否记得那个。

它是整个仪式，它是那个神话和神秘和苹果敬畏的一部分。所以我不在其他计算机的市场中。我不在其他计算机想法的市场中。我认为也许我会与Mac直到日子结束。但当苹果发现金矿时，它是操作收费站，你不必创新，你实际上甚至不必制作任何东西，你可以只是拿其他人业务的30%。

有腐烂爬进苹果的基础，那从应用商店的初始启动一直开始。但我不认为我们当时看到，我当时没有看到手机对一般计算会变得多关键。我认为当iPhone出来时，哦，它像手机。我从90年代早期就有手机。

好吧，它不是手机。它是移动计算机。甚至比那更多，它是最重要的计算机，或它会成为世界上大多数人最重要的计算机。这意味着如果你喜欢制作软件并想将它卖给人们，你必须通过那台计算机。

如果通过那台计算机意味着通过苹果的收费站，不只是必须向他们请求许可，这本身只是尊严。当你习惯于互联网，你不必向任何人请求任何事情的许可，你购买域名，你启动业务，如果客户出现，砰，你成功。

如果他们不，好吧，你失败。现在突然在你甚至可以启动之前，你必须向苹果请求许可。那总是让我感觉不对。但直到我们在2001年启动HEY，我才看到已经潜入苹果的苹果的腐烂的全部程度。

**Lex：** 对不知道的人，我们会谈论它，HEY是这个令人惊叹的电子邮件有点试图解决电子邮件问题的尝试。

**DHH：** 是的，我喜欢将它推销为如果Gmail可以实际发布，应用20年教训的Gmail会是什么。Gmail在2004年启动时令人难以置信。它仍然是伟大产品，但它也被困在其初始成功中。你今天不能重新设计Gmail，它只是有太多用户。

所以如果你想要电子邮件的新鲜思考，我想要电子邮件的新鲜思考。我需要建立我自己的电子邮件系统。不只是我自己的电子邮件客户端。那是很多人多年来做的。他们为Gmail建立客户端，但如果你不也控制电子邮件服务器，你严重受限。如果你真的想用电子邮件向前推球，你必须控制服务器和客户端。

那是我们用HEY设定的大胆使命。那是有趣的，我认为我们这里的主要障碍会是Gmail。它是电子邮件空间的800磅大猩猩。美国所有电子邮件的大约70%通过Gmail发送。我认为他们的世界率可能也在那个邻域。

他们只是绝对巨大，试图攻击像那样的巨大既定竞争对手，实际上仍然被很多人喜爱。是免费的似乎像自杀使命。那只是我们签署的使命，因为我们在制作Basecamp 20年后已经变得足够雄心勃勃，我们认为我们可以解决那个问题。所以我想，嘿，这愚蠢。

我不会建议任何人与Gmail正面交锋。那似乎像自杀使命。我们无论如何要尝试，因为你知道什么？如果我们失败，它会很好。我们只是要为我和杰森以及公司的人和我们的猫建立更好的电子邮件体验，那会好，因为我们负担得起这样做。

但当我们准备启动时，在花费两年建立这个产品，数百万美元投资到它之后。我们显然需要移动应用。如果你不在手机上，你不会是电子邮件的严肃竞争者，你需要在那里有本地客户端。

所以我们为iOS和Android都建立了伟大的本地客户端。当我们准备启动时，我们将它们都提交到应用商店，我认为在周五下午为iOS应用都得到批准。然后我们在周一上线。我们如此兴奋。嘿世界，我们一直在这个新东西上工作，我很想让你检查它。当然，与任何东西一样，当你启动新产品时，有一些错误。

所以我们快速在iOS客户端中发现了几个，并向苹果提交了新构建。嘿，这是我们的错误面孔，你能请更新吗？那时地狱爆发了。他们不仅不会证明我们的更新，他们说，哦等等，我们给了你在应用商店的许可，但对不起，那是错误。

我们看到你没有使用我们的应用内支付系统，这意味着我们不得到你业务的30%。你必须纠正那个，或者你不能在应用商店中。首先我想，好吧，它已经被批准了。我们在同样模型上运行。我们在应用商店中运行Basecamp十年。如果你不通过应用注册，我们在我们自己的网站上注册我们自己的客户，他们只是去应用商店下载他们的伴侣应用，我们会很好。

那是真理，对吧？那是为什么我从来没有对应用商店如此激动。即使苹果开始拧紧螺丝，就像我的业务好。现在突然我的业务不好。如果我们不同意给他们通过iOS应用来的所有注册的30%，苹果愿意摧毁HEY。

它不只是关于30%。它也关于分裂，不再与我们的客户有直接关系。当你在应用商店中销售应用时，你不向客户销售应用。你向苹果的库存销售应用，然后苹果向那个客户销售应用。那个客户与苹果有购买关系。

所以如果你想给折扣或退款或任何东西，它是完全地狱。如果你想容易地支持多平台，如果某人在他们的iPhone上注册HEY，他们想切换到Android，那是完全地狱。但那个账单关系，它与苹果绑定，它是完全地狱。出于一百万原因，我不想将我的业务交给苹果。

我不想将我们收入的30%交给苹果。所以我们决定做苹果似乎从来没有听过的事情。我们说不，我们不会添加应用内支付。我不在乎你是否威胁我们。这不公平，这不合理，请批准。当然他们没有，它升级了。

几天后我们意识到，你知道什么？这不是错误。这不会消失。如果他们继续这个，我们会死。如果我们不屈服并给他们30%，他们会踢我们出去，除非我们制造如此大的骚动，如此大的噪音，他们会后悔它。那正是然后发生的。我们被祝福，我们在他们的全球开发者大会WWDC前一周启动HEY。

苹果喜欢在台上起来并喋喋不休他们为开发者做多少，他们多爱他们，为什么你应该为他们的新设备开票等等。然后我们也只是碰巧在互联网上有平台，当你需要与3万亿美元公司开战时，这非常方便。所以我开始踢和尖叫

**Lex：** 哦，男孩。

**DHH：** 本质上在战斗方面将它调到11，公开我们被拒绝在应用商店中。那变成与苹果的延长两周战斗，本质上以我们作为大卫战斗歌利亚可能得到的最好可能结果结束，这有点停战。我们不会将30%交给苹果。

他们不会将我们踢出应用商店，但我们必须建立一些胡说八道虚拟账户，这样当你下载它时应用做一些事情。那是菲尔·席勒似乎在媒体第五次询问为什么我们不能在应用商店中，当一百万其他伴侣应用可以时，即兴编造的规则。

但我们只是碰巧能够为苹果创造如此多痛苦和噪音，对他们来说让我们存在比继续战斗更容易。

**Lex：** 你对蒂姆·斯威尼与Epic对苹果的胜利怎么看？

**DHH：** 我认为这令人难以置信，整个开发者生态系统。不只是在iOS上，也在Android上，欠Epic，蒂姆·斯威尼和马克·雷恩，巨大的感激债务，因为承担了在这整个垄断执行的分类运动中唯一对苹果造成系列伤害的战斗。

那就是Epic对他们的战斗。蒂姆最近透露，对苹果进行这场战斗的法律费用已经超过一亿美元。我们，在热门时刻，考虑在他们威胁踢我们出去时起诉苹果。我们与几家律师事务所购买了案例，也许当然他们会告诉我们你有好案例。

我的意思是他们试图在这里销售产品，但他们也会告诉我们。

**Lex：** 这至少要花费1000万美元，通过所有上诉要花5到7年。

现在我们现在学到实际价格标签高10倍，对吧？Epic花费超过1亿。在法律领域承担苹果会摧毁我们。只有像Epic这样的公司可以做它，只有像蒂姆，像马克这样的创始人运行的公司可以以他们做的方式冒险业务，他们首先挑起战斗的大胆，我认为只是令人难以置信。

坚持长期，没有董事会会签署这个诉讼给专业CEO，绝对没门。所以他们能够以也是最搞笑可能方式击败苹果的事实，我认为只是令人难以置信。因为记住他们在案例中的第一次胜利实际上不是很多胜利。审判中有大约11项指控。

苹果基本上赢了其中10项，法官授予Epic这一个小胜利，苹果不能告诉他们不要链接到互联网能够做支付处理。所以他们赢了这一个小东西。苹果，而不是只是接受11中的10次胜利并说好，你可以有你的小链接，但所有这些其他规则保持在位，决定本质上犯罪蔑视法庭，因为他们现在被提交起诉，激怒法官到如此程度，美国的法治现在

是你可以在应用商店中启动应用。你不必使用应用内支付，但如果你只是链接到开放互联网，当你拿信用卡然后跳回应用时，你可以与客户有直接账单关系。我们欠所有那个给蒂姆和马克。我们欠所有那个给Epic。

我们要任何分钟启动新应用，我希望实际上下周利用这个，改造hey应用，这样从苹果应用商店下载Hey应用的人可以在应用中注册，然后可以使用网络放入他们的信用卡，所以我们不必支付30%。

相反，我们有直接账单关系，这样他们可以将那个订阅带到Android，到PC，任何东西，没有任何麻烦。我们有蒂姆和马克要感谢。

**Lex：** 是的，蒂姆，我的意思是，就像你说的创始人，但也特定类型的创始人。因为我认为也许你可以教育我这个，但蒂姆是某人维持。到今天有点原则的不合理性。是的，那是我喜欢的。我认为有时也许甚至与创始人你可以被磨损。它是大公司。

有很多聪明，引用，人们在你周围律师，只是随着时间的推移在你耳边低语，你像，好吧，只是合理，成为一个，你知道，这是不同的事情。成为那种维持，我的意思是史蒂夫·乔布斯做了这个。仍然是混蛋。谁说，"不，我会为了这个沉没这整个他妈的公司。

**DHH：** 那是我在我们原始运动中基本上使用的确切语言。我会在将它的30%交给苹果之前烧毁这个业务。那种愤慨，那种实际愤怒是我试图小心挖掘的东西，因为它有点挥发性化合物。

因为我的意思是我有一堆员工，我们有一堆客户。如果37signals在25年后的旅程会因为苹果会烧毁我们或我会为了与苹果的这场战斗烧毁业务而结束，那会相当悲伤。但我认为你也需要那种信念水平能够甚至驱动日常决定。

其他苹果例子之一，我知道我们在这里有点抨击苹果，我实际上不讨厌他们。我真的不。我对不需要为如此少而出售的浪费关系极其失望。现在我理解应用商店收费站实际上是相当大的业务。它是多个十亿，但苹果是万亿美元公司。

我认为在历史的镜头中，这会作为巨大错误出现，我认为它已经作为巨大错误出现。Vision Pro的失败部分是因为苹果惹恼了每个其他开发者。没有人急于来为他们的新硬件建立那种也许会让它成功的体验。

所以当你在顶部，你有所有卡片时，你可以稀释自己认为你可以一直在所有时间指定所有条款，没有长期后果。苹果最终学习，有长期后果的事实，开发者实际上对苹果的业务重要。关系不完全是单方面的。我们不欠我们的存在给苹果和苹果独自。

我们建立了我们自己的客户基础。苹果对行业有益。我很高兴iPhone存在，等等等等。不是它不双向，但苹果只想要一种方式。我认为那是错误，它是可避免的错误。A，那令人失望。对我来说肯定令人失望。

我字面上花了20年传播这个狗屎，对吧？我花了如此多钱购买苹果硬件，多年来原谅他们做的一堆事情。然后为了什么？为了你想要我以最不合理可能方式创造的东西的30%的事实？我们不能找到更好的方式做这个吗？我认为他们会被迫做更好的方式。但你也必须经历对你有刑事蔑视指控被提交起诉的屈辱吗？它只是似乎如此在苹果之下，但它也似乎如此符合由引用，"专业经理"运行的巨大公司发生的事情

而不是创始人和不合理的人。

**Lex：** 好吧，我们可能也应该说你喜欢苹果的东西，苹果的伟大精神我认为仍然持续，有案例可以说这个30%事情是切片，公司的特定切片，不是公司的定义方面。它苹果在它制作的硬件和它制作的很多事情上仍然在顶部。

你知道这是，那可能只是在为人类做很多很棒东西的伟大公司的长故事中的打嗝。所以就像苹果是真正特殊的公司。我们提到亚马逊，没有像苹果这样的公司。

**DHH：** 我同意，这就是为什么失望都更大。

因为我们对苹果有如此高的愿望和期望，他们是山上闪亮的城市，他们以一百万积极方式指导行业。我认为正如我们之前谈论的，硬件再次令人兴奋，很大程度上因为苹果购买了PA Semi并追求对抗所有几率的使命，将ARM提升到今天的水平。我们现在有这些令人难以置信的M芯片因为它。

苹果带到桌子的设计感性是无与伦比的。没有人肯定在硬件水平有像苹果这样的品味，甚至在软件水平。我会说苹果中留下很多品味，但现在也有一些真正酸味。

所以我认为他们必须首先洗掉那个，然后他们找到他们的穿戴回来。但苹果之前在海市蜃楼中，我的意思是沃兹尼亚克和史蒂夫·乔布斯在车库中开始这个东西，与Apple II有伟大成功。他将公司交给糖饮料销售员，他将公司坦克到90年代。他不学习教训，花费接下来20年建立这个令人惊叹的公司，然后再次将公司交给物流人员，他推测有比第一个负责的家伙更多救赎品质，但仍然最终领导公司误入歧途。现在这是常态。

常态是伟大公司不永远持续。在历史的长弧中，几乎没有公司永远持续。很少公司在100年前在这里，甚至更少200年前。实际上没有什么是1000年老的，除了少数日本剑制造商或类似的东西，对吧？所以当你在那一刻时，你可以稀释自己认为某些东西是永远的。

他们似乎如此大。苹果绝对可能跌倒，我认为他们现在有比以往任何时候更多跌倒的理由。他们在AI上落后，可怕地落后。他们的软件质量在一堆方式中摇摆。竞争在硬件游戏上追赶，部分因为TSMC不是苹果子公司，而是服务AMD和NVIDIA和其他现在能够使用同样类型先进过程的代工厂。

这是我在最长时间不看PC硬件后学到的东西，神圣的烟雾，AMD实际上制作与苹果一样快，如果不更快的CPU。他们还不那么高效，因为ARM对x86有一些基本效率，但他们仍然相当好。所以苹果应该有理由担心。苹果的股东应该有理由关心。

不只是关于所有这些跌倒，也关于苹果由老人运行的事实。苹果的董事会有平均年龄，我认为，75。他们的整个执行团队在60以上。现在那听起来可怕地年龄歧视。在某些方面，它有点是。以同样方式，我对自己年龄歧视。

就像我现在45岁，我有点必须强迫自己真正进入AI，因为它是如此范式转换，很多人当他们达到某个年龄时只是高兴与他们知道的保持。他们不想回到成为初学者。他们不想回到必须重新学习一切。我想，啊，这对我在45岁有点难。

你在75岁如何地狱做那个？

## 关于育儿的讨论

**Lex：** 我必须回到，你之前提到它，你是父母。你能说到成为父亲对你生活的影响吗？

**DHH：** 我认为关于父亲身份有趣的是，对我来说，我甚至不确定它是我想要的东西。它花了遇到正确的女人并让她说服我这是正确的想法，在我们甚至开始之前。

我在20多岁后期或甚至30多岁早期没有开始我自己的家庭在优先级列表上。它真的是遇到我的妻子杰米并她告诉我这是我想要的推动力。我想有家庭。我想结婚。我想有孩子。我想有三个。我一秒钟说哇，哇，哇。

然后呃，好吧，让我们做它。我认为那是那种快乐意外，我生活的一些部分非常驱动，我确切地知道我想要什么以及如何向前推动它以及回报会是什么。但当涉及有家庭时，那总是感觉像非常模糊抽象的想法，确定，某天，也许。

然后它变得非常具体，因为我遇到了知道她想要什么的女人。现在回顾它，它几乎似乎疯狂。就像现实中有这个分叉。如果那没有发生，我坐在这里现在，不是父亲，没有家庭，知道我现在知道关于有那个家庭的快乐的后悔水平会是存在的，会是...

我不知道他们是否会是毁灭性的。我认为男人有比女人更长的窗口追求这些事情。只是有某些生物事实。但最终得到我现在有的家庭，最终得到我的三个男孩只是变革性体验，在这里有某些东西结果是最重要的事情的意义上。它是开放秘密。

甚至不是开放秘密。它是通过所有历史的开放真理。你听任何曾经有孩子的人，他们都会说，"我的孩子对我最重要。"然而以某种方式那种智慧不能沉入，直到你自己在情况中。我发现那些真理迷人，当你实际上不能用话语传达它们。

我可以告诉你，嘿Lex，你在做什么？找个妻子，制作一些孩子，继续它。这些只是话语。他们不传达实际感觉经历体验的重力。你不能真正学习它而不经历它。现在当然你可以被影响，无论什么，我们都可以帮助贡献，小火花和小种子可以在你的头脑中关于它成长。

但它仍然必须发生。现在我在这种情况中，只是日常基础上的纯粹快乐，你认为你的生活满足水平在1到10的规模上，然后看到你的孩子理解某些东西，完成某些东西，学习某些东西，做某些东西，只是存在的满足。

只是说，哦我的上帝，规模不从1到10。它从1到100。我一直在这里1到10范围中玩。有1到100，以本身有影响的方式谦逊。这整个想法，我认为我在30多岁早期对生活边界有公平理解，就像这是关于什么？我的意思是，我在这个地球上足够长时间现在这里知道一些东西。你意识到，我不知道，我不知道。

我不知道规模要宽广得多。我经常谈论有孩子的快乐，只是看到你自己的DNA，这对我来说令人惊叹，因为字面上那一直是人类从时间黎明的追求。

我今天在这里，因为无论什么，30,000年前，一些尼安德特人有同样的实现，我应该生育，我应该继续我的血统。那都等于我现在坐在这里。但它没有成为我的实际现实，在遇到正确的女人之前。我认为那有时不够是对话的一部分，关于人们在西方世界如何配对有一些破碎。

它是为什么我们没有足够孩子的源头，因为没有足够夫妇，没有足够婚姻。没有很多这些...没有足够所有这些传统价值，甚至50，60，70年前只是理所当然。我们在这个宏大实验中，如果我们只是移除一堆机构会发生什么？如果我们不再重视婚姻作为渴望的东西会发生什么？如果父母身份现在在一些阵营中被看到会发生什么？它几乎像奇怪或反对你自己的自我表达。它是宏大实验

我有点好奇它如何结果。我更愿意作为电影观看它，就像"人类之子"，我想，那是好节目。我有点希望那不是现实。但我们看到那个现实展开，而我坐在这里在非常传统的两个父母爱的家庭中，有三个孩子，说，这现在在顶部。我在我的生活中做了很多事情。

我建立了软件，我建立了公司，我赛车，我做了各种事情，我会在心跳中为我的孩子交易所有它。那只是真的迷人的人类体验，那种纽带的深度是你在有它之前不能欣赏的东西。但我也认为有角色要扮演，谈论它。

因为我们不断被轰炸为什么不的原因。哦，它太昂贵。好吧，你可能离婚，然后你可能失去一半。有所有这些声音不断阐述反对婚姻的案例，反对有孩子，我们这些选择做传统事情结婚并有孩子的人有义务有点谈论它，这在50年前会看起来荒谬，你必须谈论如此基本的东西。

但我在那种意义上变得有点有义务做正是那个。谈论它，说，你知道什么？你可以看我做的一切，如果你喜欢其中一些部分，意识到对我在这种情况下，孩子，家庭，妻子比所有它更重要。它听起来像陈词滥调，因为你之前听过它一千次。

通过成为陈词滥调，也许你开始相信它不是真的，它只是人们说的东西，但它是现实。我知道几乎没有我有个人关系的父母不认为他们的孩子是他们生活中最重要的事情。

**Lex：** 所以你说了很多有趣的事情。

所以一个，它确实似乎是，我知道很多父母，也许更有趣，我知道很多是父母的超级成功人，他们真的爱他们的孩子，他们说孩子甚至帮助他们更成功。现在有趣的事情，说到你说的，对我们人类来说，似乎更容易阐述负面，因为他们有点具体，实用，你知道，它花费更多，它花费一些时间，你知道，他们可以是，你知道，到处哭。他们是，你知道，到处跑的小自恋者或任何东西。

**DHH：** 这都是真的。

**Lex：** 是的，到处拉屎，那种东西。但阐述你说到的事情，有这个小生物，你爱它超过你生活中曾经爱过的任何东西。很难将那个转换为话语。你必须真正体验它。

但我相信它，我想体验那个，但我相信，因为只是从科学方法，看到很多诚实地不非常有能力爱的人，完全爱上他们的孩子。就像，你知道，非常有点...让我们只是称它为它是什么，非常像嘟嘟的工程师。他们只是爱上。

它像，好吧，就像你说的人，他们不真正想要，他们不真正关心或不真正想有孩子那种东西，一旦他们做它改变一切。所以，你知道，但很难将它转换为话语。

**DHH：** 我认为也困难的原因之一是，我的意思是，我喜欢孩子。不是我积极不喜欢他们，但当我在其他人的孩子周围时，我没有情感反应。一些女人有，对吧？他们看到婴儿，他们说，"哦。

我从来没有任何那种情感。我的意思是，我可以欣赏，我为你高兴你有孩子。它没有在我身上挑起任何东西。当我看我自己的孩子时在我身上挑起的情感，这不存在在同样宇宙中。

所以你没有某些东西，你没有完整平行，或至少很多男人，或至少我，我没有有点框架将它放入有我自己的孩子会是什么样？然后你体验它。它像噗。它也发生得如此快。这是我发现迷人的。它在那个小人类甚至能够向你返回任何话语之前发生，你对婴儿发展的爱，它发生得相当快。

不一定立即，我不知道，不同人有不同体验，但它花了我一点。但然后一旦它击中，它只是像马踢击中。我喜欢它也只是如此普遍体验。你可以是世界上最成功的人。你可以是世界上最贫穷的人。你可以在中间某处。

我们分享这种体验，成为父母，对他们大多数来说，结果是他们生活中最重要的事情。

**Lex：** 但你知道，与正确的伴侣做那种体验真的很好。但我认为因为我是如此共情者，有错误伴侣的成本对我来说很高。

但然后我也意识到，男人，我有一个朋友离婚了，快乐地，他仍然爱他的孩子的狗屎。它仍然美丽。它是混乱，但所有那种爱仍然在那里，它是，你知道，你只是必须让它工作。只是那个，我不知道，那种离婚会摧毁我。

**DHH：** 你应该听生活学校。他在YouTube上有这个伟大的片段。你会嫁给错误的人。如果你提前接受你会嫁给错误的人，每个你可以嫁的潜在人在某个维度上会是错误的人。他们会惹恼你。他们在某些维度上不会是你希望的。

浪漫理想，一切一直只是完美，对搭配并制作婴儿的现实不是非常有利。因为我认为正如你刚刚叙述的，即使当它变成狗屎时，我发现我个人知道的大多数人，事情分崩离析并变成狗屎，一百万年中从来不会说，"我后悔它。

""我宁愿，我的孩子不存在，因为关系变酸。"我的意思是，我认为你应该非常努力尝试。我认为这也是那些事情之一，我们没有完全理解那些围栏。当我们拉起它们并庆祝离婚多容易时，例如，那不会有一些负面后果。

我不是说你不应该有离婚。我不是说回到过去时代。我说虽然文明在数千年中发展了某些技术，确保其自己机构和自己生活的延续，也许我们没有完全欣赏。

我的意思是，再次，这是乔丹·彼得森和其他人更雄辩地说话的东西，我学到很多只是分析我自己的情况。为什么这个令人难以置信的负担，对你带到这个世界的其他人的生活负责也是存在最有回报的部分？那只是好奇。

在我听到彼得森阐述承担你知道如何承担的最大负担的价值之前，我总是认为负担是负面事情。我为什么想要孩子的负担？我可能搞砸它。我可能是坏父母。他们可能有坏...所有这些东西，对吧？所有你不应该的原因。如此少声音阐述你为什么应该。

**Lex：** 是的，但我也应该在你提到的那个事情之上添加，目前，也许在西方，配对过程破碎。破碎，技术让它更糟。是的，这很迷人。

这整个事情没有被解决。所以雇佣伟大团队，那可能在配对中被解决得最好。找到伟大的人雇佣。第二，找到伟大朋友。那像，那也没有被解决。

**DHH：** 它正在分解。

**Lex：** 它正在分解。第三是关系的配对。那像最糟糕。

实际上，技术让它甚至更糟。这很迷人。

**DHH：** 是的。这是再次所有最大意图仍然直接领导我们到地狱的伟大例子。我真的享受路易斯·佩里对性革命不是不合格好的分析，这是我之前她阐述它之前根本没有想过的东西。

当然女人应该能够有自由和自我决定和堕胎和所有这些事情。路易斯·佩里也不反对那个，当然。但有我们当时不欣赏的二阶事实，我们可能没有现成解决方案。那只是有趣。你以一百万不同方式让生活更好，以某种方式我们最终更悲惨。

为什么那样？为什么人类在困难中找到意义？我认为其中一些是通过科学回答困难问题。再次，彼得森很好地阐述这个想法，你必须通过艺术找到一些，通过作者找到一些，通过不同...我刚要说知识模式，在我停止自己之前，因为那听起来像胡说八道。

但有不同方式获得那些深层教训，有点论文不会告诉你。

**Lex：** 我的意思是，这真的，这个点也适用于宗教，例如。如果你从社会中移除宗教的软件，你最好有好替代。

**DHH：** 我们有一堆坏替代，特别是在过去几十年中。

宗教是我挣扎很多的那些事情之一，因为我不宗教。我有点希望我是。我现在可以完全欣赏有像那样的操作系统带来的巨大价值，不只是在个人水平，而是在社会水平。完全不清楚答案是什么。

我认为当涉及替代时，我们尝试了很多死胡同，人们一直以一百万不同方式填充那个空白，似乎比所有宗教更糟，尽管他们在无数方式中的错误已经能够交付。

**Lex：** 是的，宗教像钴代码。我

**DHH：** 是的，它是我们不完全理解规则和为什么它们在那里以及如果我们移除它们会发生什么的机构。是的，其中一些对我来说似乎明显只是时代的胡说八道。哦，你应该需要无论什么贝类，因为在世界那个地区。有一些东西，一些东西，一些东西，好吧，好的。但有一堆其他事情对保持社会长期运作至关重要，我们不完全理解哪个是哪个。什么是胡说八道，什么是社会的承重柱。

**Lex：** 你能说到孩子对生产力的打击吗？他们增加你的生产力，减少它，或者那甚至是错误的问题问？

**DHH：** 我认为这是雄心勃勃的人经常害怕有孩子的原因之一，因为他们认为我有如此多更多要做，我现在勉强有足够时间。

如果我添加另一个人类到混合中，我如何可能能够完成我想完成的事情？现在，A，我们总是工作每周40小时，不是80或100或120。我认为那非常有益。B，孩子不存在在只是他们独自被输入你生活的这个真空中。希望有伴侣。在我的生活中，我嫁给了美妙女人，她决定在我们在一起时停止她的企业工作，能够承担那个责任的巨大部分。我刚要说负担。

我认为那正是它经常被呈现的方式，特别是从女权主义角度，照顾你自己的孩子是某种无薪劳动，必须以某种特定方式补偿，超出将生命带到这个世界，赛跑美妙人类的补偿。关于那个分析有一些扭曲，我实际上认为现代传统运动是对它的回复。他们是否有所有答案，我肯定也不确定。

但在分析中有一些只是不对，孩子是负担，如果女人选择与孩子在家呆，那是女权主义雄心的某种失败模式。我认为那实际上是完全死胡同。现在取决于不同人，不同情况。

我只能说到我的生活，嫁给美妙女人，她决定与孩子在家，至少在他们早期年龄，承担很多那些责任。现在它不意味着没有很多方式我必须成为那个的一部分，必须贡献，但它允许我继续工作我总是工作的每周40小时。

但它让40小时更严格。我有时间表，我醒来，无论什么，6:30，我们必须在8:00前一点出门。我通常必须与我最小和有时中间孩子玩至少一或两轮"Fortnite"。然后带孩子去学校，进入，在，我不知道，8:39开始工作。没有工作直到5:00，5:30，有时六点钟。

但然后是晚餐，我必须在那里。然后我必须给孩子读书，到那个完成时，我不想回到工作。所以我的工作时间真的是9:00到5:00，9:00到6:00，取决于无论什么正在进行。有时有紧急情况，你必须照顾它们，但它让它更结构化。

我在那个中发现一些好处，我在那个中发现一些生产力，我不能相当那么多闲逛，日子会在大约5:36结束。那只是如果我没有完成我今天想做的，如果我到达那个时间，它完成了。我结束了，我必须明天再试。而在有家庭之前和有孩子之前，我可以只是不做它，只是在晚上弥补。

所以在那种方式中，它让我更结构化，但它没有真正改变我的工作量那么多。我仍然工作大约同样小时数。顺便说一下，那足够。这是我们在中做的关键点之一。在工作中不必疯狂。我们写的最新书是有足够时间。每周40小时实际上是大量，如果你不浪费它。大多数人确实浪费它。他们在会议中浪费它。

他们在只是不重要的东西上浪费它，当甚至每天三小时，四小时集中不间断时间会移动他们真正关心的目标在场地下方。

**Lex：** 我认为孩子确实以那种方式让你更有生产力，对需要它的人，特别是像我这样的人。他们创造他们的紧迫性。如果你必须在5:00完成，这是也许反直觉概念，但对像我这样喜欢工作的人，你真的可以用工作的绒毛填充日子。

如果你必须在5:00完成，你会必须做深度工作并完成它。就像真正专注单一工作。然后你只是要切断所有压力。

**DHH：** 它让你诚实。它让你诚实，因为你可以浪费一天，你可以浪费两天。但如果我浪费整周，我感觉糟糕。

现在那只是我身上有的一些驱动，如果我实际上做重要的事情，我感觉满足和完整意义，如果我可以回顾周并说那是好周，真的。我们向前移动。也许我们没有完成，但我们向前移动，一切变得更好。我认为孩子真的帮助只是以那种方式时间盒事情。

很多人需要那个，因为我发现对过度工作的如此多庆祝如此令人厌倦。哦，我工作每周60小时或80小时或100小时，只是像，首先，不，你不。不，你不。就像那些80小时充满各种你标记工作的绒毛，但我会笑，大多数人笑。

如果你实际上做那个时间去哪里的分析，你会笑。大多数必须做的重要东西在这些不间断块中完成，这里两小时或那里四小时或那里五小时。困难部分是确保你在整个片段中得到它们。所以不要给我那个。有足够时间。

也什么如此重要，它排在继续你的血统之上。我认为在事实中有一些古老荣誉，再次，坐在这个椅子上的这个DNA旅行30,000年到达这里，你要浪费所有那个，只是这样你可以发送更多电子邮件。

**Lex：** 有一些也难以转换为话语的东西，只是与你的孩子玩你可以有的那种乐趣。我不知道在表面上它像什么，我可以有那种乐趣只是自己玩视频游戏。但不，它像有一些关于它的魔法，对吧？

**DHH：** 我从19年起在"Fortnite"中记录了一千小时，我认为，所有它与我的孩子。我从来不会玩"Fortnite"。好吧，我不知道我是否从来不会。

如果不是为了我的孩子，我不会玩一千小时"Fortnite"。对我的享受是与他们做我也碰巧享受的东西。我真的爱"Fortnite"。它是现象游戏。我不必强迫自己与他们玩那个。我经常问，嘿，你想玩"Fortnite"吗？但仍然，它是我与他们分享的活动。

它是我与他们分享的热情。我开始与我最大的做卡丁车。我长时间驾驶赛车，现在他们进入卡丁车，只是在卡丁车赛道，看他们转，看他们变得更快，看他们学习那个技能，你只是看，我还会用我的生活做什么？在我的年龄，45，我站在这里真正享受生活。

我带到这个世界。在这个阶段还有什么如此重要，我否则会花我的时间？

## 关于赛车的讨论

**Lex：** 好吧，就像你提到的，你喜欢赛车，你在世界级竞争水平做它，这令人难以置信。所以你如何进入它？什么吸引你赛车？你喜欢它什么？

**DHH：** 关于进入赛车的有趣事情是我直到25岁才得到我的驾驶执照。

我在丹麦哥本哈根长大，汽车税基本上超过200%。所以你为三辆车付钱，你得到一辆。我甚至没有一辆车的钱，更不用说三辆。所以我长大时负担不起汽车。我们长大时没有汽车，但哥本哈根是能够骑自行车或公共汽车四处走动的好城市，或者正如我长时间做的，滑轮。

但当我25岁时，我意识到我想在美国花更多时间。我还不确定我要搬到那里。那后来结果是真的。但我知道如果我想在美国花时间，我需要有驾驶执照。如果我不知道如何驾驶汽车，我不会很好地四处走动。所以我在25岁得到驾驶执照。

然后那年晚些时候最终搬到美国，我一直喜欢视频游戏，赛车视频游戏，Dreamcast上的"Metropolitan Street Racer"是真正吸引我的那些游戏之一。它是Project Gotham的前身，它是本质上，Forza Horizon的前身，我认为。

**Lex：** 哦，好吧，有趣。

**DHH：** 我认为那是血统如何去。

只是伟大游戏。我实际上几周前在模拟器上启动它。它仍然有点坚持，因为它有足够真实汽车动力学，它闻起来有点像驾驶真实汽车。它不只是像街机赛车手，像世嘉拉力赛或类似的东西。但我一直喜欢那个。

然后我在25岁得到我的驾驶执照并搬到美国，然后两年后，我在芝加哥遇到的朋友带我到芝加哥Autobahn乡村俱乐部，这是这个伟大赛道，距离芝加哥大约45分钟。我坐在赛车中，我第一次驾驶赛车。我有同样类型的伪宗教体验，当我开始在Ruby上工作时，我做了也许20圈在这个基本上来自，我认为，它像90年代或什么的马自达赛车，像相当便宜的赛车，但真实赛车。

单座，手动变速箱，但暴露光滑轮子，所有东西。在有那个体验后，首先它只是有史以来最令人惊叹的事情。就像驾驶赛车的物理感觉真的独特。我认为如果你快速驾驶汽车，你有也许2%的味道。

你在单座赛车中得到的元素暴露，特别是像那样的，你的头实际上在元素中，你可以看到个别轮子，你的速度感觉只是如此高，在完全不同水平。

**Lex：** 所以你实际上能说到那个吗？所以甚至在那个马自达，所以你能感觉，你能感觉什么，像赛道反响，你看？你感觉抓地力？

**DHH：** 哦是的，你不仅可以看到颠簸，因为你字面上直接看轮子，你可以感觉所有颠簸，因为你运行光滑轮胎。

它是真的僵硬设置。它不像在赛道上拿快速街车并试图稍微驾驶。

**Lex：** 所以你能感觉像滑动，牵引？

**DHH：** 是的，你感觉滑动。那是驾驶赛车满足的巨大部分，在我们称为粘附边缘驾驶。汽车实际上稍微滑动。

几个百分点滑动角是驾驶赛车最快方式。你不想滑动太多，看起来很棒，很多烟雾，但它不快。你想驾驶它的方式只是在粘附限制，你旋转汽车尽可能多你的轮胎可以管理。

然后稍微更多，玩它，保持它只是在那个水平。因为当你在水平或粘附限制时，你本质上只是离旋转一个微小运动。我的意思是，它不需要很多。然后汽车开始旋转。一旦它开始旋转，你失去抓地力，你去墙。那种危险和技能的平衡如此令人陶醉。

它也比赛车视频游戏好得多，因为关键性提高两个档次。我经常想真正喜欢赌博的人，我想，你不只是玩扑克吗？就像不，点不是扑克。

扑克可能是它的一部分，但点是我可能失去我的房子，对吧？就像那是一些人对赌博得到的瘾，有一些真实的东西在线上。当你在赛车中时，有一些非常真实的东西在线上。如果你弄错，至少，你会旋转并适当撞墙，它会昂贵。最糟糕，你不会活着出来。

即使现代赛车变得比过去安全得多。有那种真实的危险元素。仍然有人在赛车中严重受伤甚至死亡。与过去相比，这是仁慈地罕见，当60年代那些疯子会做一级方程式，无论什么，13%的网格不会到年底，因为他们只是在火焰火球中死亡。但仍然有一些在那里。

我认为既然有一些东西在线上真的贡献它。但它比那更多。只是有物理感觉。有你所有力量的激活。有心流。我认为那真的巩固了为什么我上瘾，因为我总是，我爱我从编程中得到的心流，但从编程中得到心流是非常不一致过程。

我不能只是坐在键盘前说，好吧，让我们让心流进行。它不那样发生。问题必须正确。它必须在正确时刻遇到我的技能。它有点彩票。在赛车中根本不是彩票。你坐在那辆车中，你转点火，你出去赛道，我几乎保证得到心流。

因为你需要，或我至少需要100%的大脑处理能力能够以我去的速度而不撞车。所以没有时间想其他夜晚或下周会议或产品启动。它完全禅，实际上在字面意义上。我想真正擅长冥想的某人，那可能是他们进入的那种状态，只是清楚你在现在，除了你和下一个弯道什么都没有。那是真的令人上瘾的体验。

所以在我有那个后，我不能得到足够。我继续去赛道每个我得到的机会。大约四年每个周末，我会去赛道。到那个时间结束时，我最终工作足够技能和与公司足够成功，我可以负担去，引用，"真实赛车。

所以我开始做那个。我开始驾驶这些保时捷，然后一旦我进入那个，一旦我进入，引用，"真实竞争"，我想，我想知道你可以把这个带多远。它没有花那么长时间，在我决定，你知道什么？我可以把这个一直带。我在赛车中的伟大英雄是汤姆·克里斯滕森，丹麦同胞。

勒芒先生，正如他们称呼他。世界上最伟大的耐力赛。勒芒24小时被汤姆·克里斯滕森赢得比任何其他人更多次。他赢得比赛九次。所以汤姆真的让我喜欢勒芒。我从我认为80年代开始观看勒芒。我有在电视上观看那个的最早记忆。

比赛从我认为20年代开始进行。但在80年代我有点进入它。然后在90年代后期，2000年代早期，当汤姆开始赢时，我像几乎每个其他丹麦人开始几乎虔诚地观看比赛。所以我想，你知道什么？我想到达勒芒。

这是关于赛车的魔法事情，如果我进入篮球，就像我不能设置现实期望我要在NBA打球，然后我要去决赛。或者我进入网球，我要在温布尔登打球，那只是不发生。但赛车事情在这种方式中特殊，因为它需要相当数量金钱保持这些汽车运行。它真的昂贵。

它像有小创业公司。你需要飞一堆人到世界各地并购买昂贵设备等等。所以你需要一堆资本。我通过公司的成功有一些。所以我可以做它，这意味着我可以到达勒芒。所以我设置那个作为我的目标。我想到达勒芒。我在2009年开始在真实竞争中赛车。

三年后在2012年，我第一次在勒芒的网格上。

**Lex：** 我们应该说，所以勒芒，24小时赛车耐力。我的意思是，这疯狂。

**DHH：** 有三个司机，记住。所以它不像一个家伙只是连续驾驶20小时，24小时。但仍然，它是相当艰难比赛，身体和精神上，特别是精神上。

当你已经起来24加小时时，你不像你第一次醒来时那样敏锐。这关于勒芒也有趣。它在下午大约四点钟开始。所以到比赛开始时，你已经起来半天。然后在你完成之前有24小时。

你会在汽车中任何地方从通常一小时半到最多四小时。规定说六中四是你可以做的最大。我在勒芒单次驾驶也许两小时半。它相当费力。你以每小时200英里进入其中一些弯道，赛道上有另外60辆车。

每当我在我的正常类别，这是LMP2类别时，我有GT汽车，更像法拉利和保时捷，我必须超越。然后我有这些超级汽车，这是超越我的顶级类别。所以你有很多进行，你必须保持敏锐连续两小时半做那个。

那只是保证方式得到令人难以置信心流长，长时间延伸。那就是为什么你对它上瘾。那就是为什么我上瘾。

**Lex：** 你必须与我谈论这个视频。你在这些LMP2中的这个视频。

**DHH：** 哦，是的。

**Lex：** 这如此酷。这如此酷。

**DHH：** 这可能是我职业生涯最喜欢的战斗。

**Lex：** [评论员] Heinemeier Hansson击败过去添加五...

**DHH：** 是的，所以这是我在上海国际赛道对尼科·穆勒驾驶。

**Lex：** 你在外侧这里？

**DHH：** 我在外侧在...

**Lex：** 多酷？

**DHH：** 蓝色和白色。我们基本上在这个后直道上座位之间有一张纸整个赛道。

我如此接近他，因为我想强迫他到赛道另一边，这样他不能只是把我装箱。我们在这一点已经基本上连续战斗40分钟。我一直设法保持这个专业司机在我后面40分钟。他最终超越我，但我们只是继续整个时间战斗。它真的只是显示这两种汽车。勒芒原型。我们实际上从来不触摸。

我们在大约一英寸内。继续围绕上海赛道。

**Lex：** 多酷。你如何变得如此好？就像什么？我的意思是那是迷人故事，对吧？你能够变得如此好。

**DHH：** 对我是的那种司机，我相当好，这叫做绅士司机，这意味着我不是专业司机。

就像许多好绅士司机，当我们在我们真的最好时，我们可以与甚至一生做这个的专业司机相当竞争。我们和专业人员之间的差异是专业人员可以每次或多或少每次做它。所以我不能一直这么好。当一切正确时，我可以与专业司机竞争，但那不是你如何赢得冠军。

那不是你如何被工厂付钱驾驶。你必须每次出去都好。所以那是巨大差异。但其中一些也只是，我真的把我的心思放在它上，到我意识到赛车是我想做作为我的严肃爱好的时候。我投入数千小时。

**Lex：** 你撞过吗？最糟糕撞车是什么？

**DHH：** 我有很多撞车，但谢天谢地，敲木头，我没有任何撞车，我真的严重受伤。

**Lex：** 你像破坏汽车吗？

**DHH：** 哦是的，哦是的。我破坏了许多汽车。

**Lex：** 所以那感觉如何？只是你破坏汽车。就像你如何得到...

**DHH：** 如果你在真实比赛中，其他人依赖你，感觉像完全狗屎。

它甚至不那么多汽车，虽然它也有时这些汽车修理昂贵，那糟糕，当你撞车其中一些汽车时，它以某种方式感觉如此浪费。但你让团队失望的感觉。耐力赛车是团队运动。你不仅有你的机械师，你通常有共同司机。

所以当我撞车时，我只是感觉，该死，我可以避免这个。

**Lex：** 是的，但你也可能死了。

**DHH：** 你知道什么有趣？我从来不想那个。我不认为你可以。因为我认为你开始想能够死的那一刻，你不能做它。你不能快。

**Lex：** 好吧，我确信不要去所有卡尔·荣格和弗洛伊德这里，但我确信那总是在你头脑后面某处存在。你只是不把它带到表面。

**DHH：** 它是在它是吸引力一部分的意义上，它是有一些东西在线上的感觉一部分，这不只是虚拟。我不能只是击中重置，重新开始，重新启动。

如果我撞车这辆车，我们会出局，或者我们会处于劣势，或者它会被摧毁，或者我可能受伤。我几次轻微受伤。我实际上有我们在我们类别中赢得勒芒24小时的年，我一直在这个方程式3.5汽车中训练。它是真的快汽车。它是真的好练习做，但它也是，它没有动力转向。

所以其中一些赛车，特别是开放座位，他们没有动力转向。这意味着方向盘基本上直接连接到前轮。所以如果你撞车其中一辆汽车，前轮突然转，如果你不把手从轮子上拿开，你真的会伤害你的手。

我没有赛车足够那些汽车知道我必须得到，或有本能，发展本能我必须把我的手从轮子上拿开。所以我没有，我真的伤害我的手，这只是我认为勒芒24小时前一个月。所以我想，啊男人，我今年要错过它。我有像，不是石膏，它只是严重扭伤。

然后以某种方式奇迹般地像事件前一周，我想，是的，实际上现在好了。所以得到做它。如果我必须看我的团队继续赢得比赛，我必须坐在边线，那会是严重后悔。

但我真的在我的大多数撞车只是昂贵或运动不便的意义上相当幸运。他们从来没有是我严重受伤的东西。但我看到很多实际上的人，我今年和几年的共同司机，Pietro Fittipaldi在Spa驾驶赛车。Spa是有史以来伟大赛道之一。它有这个标志性弯道叫Eau Rouge，这可能是所有赛车运动中最著名弯道。

它在你爬上坡之前有伟大压缩。它极其快，非常困难弯道。正如他做压缩，他的汽车基本上设置出来，他失去他的动力转向，他直接驾驶到墙并打破他的两条腿，基本上面临也许他的职业生涯结束的前景。

我有其他队友和我知道的人有真的伤害他们的严重伤害。然而有趣的是，正如你说，你会认为那会沉入。我们在2014年赢得的前一年，同样汽车在勒芒有丹麦司机在我驾驶的比赛中死了。当赛道上有一点雨时，他失去汽车控制，赛道不幸地以如此糟糕方式设计，栏杆后面有非常大树。

他以全速撞那棵树，拉90 G并当场死亡，这只是如此极其可怕体验经历。我那年完成第二，这应该是一堆庆祝的原因。但它只是被不仅司机死，丹麦同胞死的事实玷污。我知道的家伙死了。那相当艰难。

**Lex：** 所以那投入必须考虑的事情堆中是你提到的赛道天气条件。天气干燥或湿润。

**DHH：** 它是它的巨大部分。

**Lex：** 甚至去年在勒芒。下雨，我出去，我从2012年第一次比赛以来没有在勒芒24小时犯严重错误，我把它放在沙坑中，像四小时，我们失去几圈被拉出，但它实际上没有改变我们结果的任何东西

因为那只是场地如何分布。我多年来犯小错误，但没有真正让我们出局的。去年比赛下雨时，我首先撞福特野马，当我在赛道潮湿部分做过度雄心勃勃超越，不能及时停止，然后感觉绝对可怕，当我坐在砾石坑两圈，知道我们的比赛结束。我们高度竞争的比赛。

你不被祝福每年有竞争汽车，竞争团队和竞争设置。我知道那多罕见。所以知道我们那年有机会，我有点浪费它感觉真的坏，但那得到复合。我回到赛道，勉强做另一次驾驶，然后当它开始在保时捷入口下雨时，再次把它放在砾石坑。

所以这是为什么赛车也如此令人上瘾的部分，因为高点非常，非常高。当你赢得像勒芒24小时的比赛。感觉只是令人难以置信。有如此多情感。但如果你搞砸它，低点非常，非常低。

**Lex：** 当你驾驶时，你注意什么事情？什么是参数？你加载什么？你感觉抓地力吗？你基本上增加速度并看什么，像恒定反馈系统对抓地力的影响，你试图管理那个并试图找到那个最优滑动角？你四处看使用你的眼睛吗？

你闻东西吗？你听只是感觉风，或者你也看场地？就像你如何不撞那个家伙？你接近英寸，对吧？所以你也必须注意那个。关于我们字面上几英寸分开的那个特定战斗真的有趣。

我不能完全解释它，但人类可以发展令人难以置信空间感。我不能看到我汽车后面的边缘，但我可以确切知道它在哪里。我可以在我头脑中有心理模型，给我这辆汽车的确切尺寸，只是我可以在竞争对手汽车几英寸内或墙几英寸内运行，当事情进展顺利时不撞任何一个。汽车大约两米宽，它相当长，五米。

你不能看到一切。镜子实际上有点狗屎。这些汽车中没有后视镜。你不能看出后面。你只能通过你的两个侧镜看。但当你在这个上变得足够好时，你形成这个直觉心理模型。但我实际上最注意的是我运行程序。

我试图做当我去赛道时是我试图为每个弯道加载我知道如何的最好程序。我的制动点是什么？我的加速点是什么？我的制动拖尾曲线是什么？我试图部分通过自己找到它和我可以多快拾起那个程序。

但甚至比那更多，通过复制我的专业竞争对手或不是竞争对手，共同司机。所以我通常总是与专业人员赛车。现代赛车产生绝对巨大数据量，你可以在每次出去后分析所有那个数据。你可以看到你推制动踏板多少的确切轨迹，你在转向输入方面做多少。

当你踩油门时，你可以看到你失去的每毫秒在那些图表中明显。所以我试图做的是我试图看图表，然后我试图加载那个并像那是我必须做的。哦，在这个弯道17，我必须在制动上轻10巴。所以我试图加载那个程序，然后我试图现在重复它。然后有所有改变的事情。你的轮胎改变相当多。

这些轮胎在许多情况下只持续40分钟。有时在勒芒我们可以去更长，但在一些赛道，他们在真正下降之前只持续40分钟。所以你必须管理那个。抓地力不断改变。所以你的程序必须突然适合那些改变情况。

然后在耐力赛车中，你不断与其他汽车互动，因为你超越较慢类别或你被更快类别超越。所以那是方程式的一部分。然后你试图在粘附限制周围跳舞汽车。所以你同时有所有那些因素玩。

但最重要的对我是试图成为机器人。就像我如何连续两小时半确切重复这组步骤，我应该不犯100毫秒价值错误？

**Lex：** 是的，低延迟算法。

**DHH：** 那实际上真的是它的巨大部分。你的延迟在能够捕捉汽车开始滑动时极其重要。

你在你身体中得到这种感觉，G力有点偏，滑动角有点偏，然后你必须反向转向。显然最好赛车司机只是感觉像直觉。我有一些直觉。我没有所有它，所以我偶尔旋转我的汽车。但那是挑战。

**Lex：** 从你学习和理解的一切，在赛车中实现精通需要什么？就像成为世界上最好赛车司机需要什么？

**DHH：** 痴迷是它的一部分。当我读和听关于塞纳和其他伟大，他们只是单一专注。马克斯·维斯塔潘是当前世界冠军，他是同样类型。马克斯观看一直迷人。

我的意思是他是现象赛车司机，但他也字面上不做其他任何事情。当他不在赛道时，他驾驶模拟赛车。就像他字面上在视频游戏中做更多赛车，当他不做他已经做的所有赛车时。

**Lex：** 有特定技能，他们有，就像通过所有那种痴迷突出给你作为超自然？它是一堆因素，或者他们实际上能够，就像你说，发展感觉？他们能够到达滑动的非常边缘吗？

**DHH：** 他们能够为汽车滑动时发展非常精细调整感性。

他们可以感觉只是这些微小时刻或底盘中的运动，通常通过他们的屁股传输。那就是为什么你称我们像屁股计量器，上升，你感觉汽车松散，或者你感觉你刚要锁定。你真的可以磨练那个调整。然后另一件事是你必须有真的好反应时间。

当你看伟大一级方程式司机时，他们通常可以有刚好在200毫秒下的反应时间，这很棒。甚至10毫秒差异产生巨大差异。当一级方程式网格时，你会看到它，例如，他们做站立开始，你看到五个红灯亮起。当最后灯熄灭时，他们应该释放离合器并开始。他们可以计时这个。所以你可以确切看到谁有反应时间。

甚至偏离20毫秒可以产生你在第一弯道前面或后面的差异。

**Lex：** 获胜多少也只是争夺位置的策略？

**DHH：** 有其中一些，其中一些也只是神经。谁更想要它？那正是那种危险感觉进来时。

有费尔南多·阿隆索的伟大引用，当他在铃鹿对舒马赫驾驶时，我认为。他们来到这个令人难以置信快弯道。它非常危险。阿隆索基本上叙述我要做超越，因为我知道他在家有妻子和孩子。

**Lex：** 那如此黑帮。

**DHH：** 只是绝对无情，对吧？

**Lex：** 是的，哇。

**DHH：** 我知道他比我更重视生命。

所以有时有一点扑克。谁会屈服？在那方面有一点鸡肉比赛。有时它不工作。没有人屈服，你们都撞车。但经常一个人会首先眨眼。

**Lex：** 超越可以在内侧和外侧吗？

**DHH：** 你可以在任何地方超越，只要你有汽车在赛道上的轻微部分。

**Lex：** 然后你只是即兴发挥并冒险。什么运动。

然后塞纳，当然，像传奇冒险者。

**DHH：** 是的，甚至在他之前，到时候，我的意思是他在90年代死了。但到我们到达90年代时，赛车已经比尼基·劳达在60年代赛车时安全得多。那种危险水平不再在那里。仍然只是它的残余，它仍然危险，但不像那样。

有点难通过年龄比较，就像谁是有史以来最伟大司机。我认为有公平论点塞纳是，但我们没有数据。我们不知道他对抗谁。就像如果我们今天让他对抗马克斯·维斯塔潘，他会如何表现。我确实认为有时你可以对有史以来伟大有点怀旧，但世界向前移动，新记录一直被设置，专业性继续改进。有时对运动不利，我认为。

有很多专业司机不仅只是非常擅长驾驶，而且非常擅长成为企业发言人。它过去相当不同。过去在赛车中有更多角色，有更多个性，他们被允许闪耀，因为没有十亿赞助在线上，他们害怕失去。

**Lex：** 荒谬问题，有史以来制造的最伟大汽车是什么，或者也许驾驶最有趣的是什么？

**DHH：** 对我来说有史以来最伟大汽车是帕加尼Zonda。

**Lex：** [Lex] 好吧，我正在查这个，帕加尼Zonda。

**DHH：** [DHH] 所以帕加尼Zonda由这个美妙阿根廷人叫Horacio Pagani制造。

**Lex：** [Lex] 我的上帝，那是美丽汽车，哇。

**DHH：** 它是华丽汽车。你可以查我的。

它是帕加尼Zonda HH。所以那是我在2010年在我们访问摩德纳工厂后制造的汽车。通过纯粹意外最终得到这辆汽车，但它基本上成为我世界上最喜欢的汽车。当我观看"Top Gear"一集时，我认为在2005年，其中一个主持人驾驶帕加尼Zonda F。我只是想，那是世界上最美丽汽车。

它是世界上最令人难以置信声音汽车。如果我有一天有选择，这是我想要的。然后我在2010年有选择，我从那时起有汽车。我从来不会卖它。它真的是经受时间考验的杰作。有一些历史上伟大汽车被认为在他们时代伟大。这辆汽车仍然伟大。

**Lex：** 你把它带到赛道吗？

**DHH：** 我有，它在那方面糟糕。好吧，我不想说它糟糕，那不是它设计的目的。它为道路设计，那就是为什么它伟大。有很多快汽车跨越他们为道路的赛车。你实际上不想要世界的赛车。世界的赛车是屁股痛苦。它太僵硬。它太吵。它太不舒服。你实际上不能带它去公路旅行。

**Lex：** 所以这实际上感觉好驾驶正常道路？

**DHH：** 完全，完全。

**Lex：** 你当然总是去速度限制？

**DHH：** 总是。这就是为什么我喜欢在西班牙有这辆汽车，因为他们稍微更放松。不完全放松，但比他们在很多地方更放松。

在丹麦，我不开玩笑，如果你在高速公路上，你去超过速度限制两倍，他们没收你的汽车并保留它。你不会拿回它。他们甚至不关心它是否是你的汽车。就像如果你借我的汽车，你去速度限制两倍，它消失了。所以他们在西班牙不那样做。

我的意思是，在除了德国高速公路的大多数地方，如果你去速度限制两倍，他们变得生气。出于各种公平原因，我不主张你应该去比那更多。但有某些特殊道路，你可以打开事情，没有人处于伤害方式。那是令人难以置信感觉。我确实认为其中一些速度限制实际上有点愚蠢。我不只是在真空中说那个。在德国，他们有光荣高速公路。

在高速公路上，在一堆段中没有速度限制。他们如此致力于他们的无速度限制高速公路，顺便说一下，德国人非常奇怪。他们通常爱规则。他们通常对它非常精确。然后他们有这个叫高速公路的光荣事情。

几年前有伟大案例，一个家伙拿出布加迪Chiron；在高速公路上去每小时400公里，他填充它并放在YouTube上，案例被带来对抗他。因为即使他们没有速度限制，他们确实有规则你不能鲁莽驾驶。他赢了案例。他没有鲁莽驾驶。他只是去非常，非常快。我几次做高速公路。

我妻子和我在2009年在欧洲去公路旅行，我得到兰博基尼Gallardo。我们驾驶到每小时200英里。我之前在赛道上驾驶每小时200英里或接近它。那感觉像一件事。在公共道路上驾驶，每小时200英里感觉真的，真的快。

**Lex：** 可怕？

**DHH：** 实际上有点可怕，是的。

因为你不断想，就像在赛道上，你知道道路，你知道表面。大多数时候你可以走赛道。你可以知道是否有凹陷。在公共道路上，你不能知道是否突然有坑洞。推测德国高速公路上不会有坑洞。但它确实感觉有点可怕，但也令人振奋。速度只是本质上真的有趣。

我不知道我带出去快汽车的任何人。好吧，实际上我确实知道几个人。我带出去快汽车的大多数人，他们咧嘴笑。当你去真的快时，咧嘴笑是人类反应。

**Lex：** 你知道你曾经去的最快是什么吗？

**DHH：** 我可能在勒芒，我认为当LMP2在他们最大功率时，有600马力和真的粘轮胎，我们去每小时340公里，这刚好超过每小时200英里。

有点超过每小时200英里。那确实感觉快。关于速度真的有趣的是，去，比如说150和160之间的差异实际上不感觉那么多，那些10英里每小时。但去190和200之间的差异感觉疯狂更快，作为百分比变化实际上比从150到160更少。

但一旦你到达那些限制，有一些指数感觉，它只是在完全不同水平。

**Lex：** 是的，因为对我来说像110，120感觉快。200？那疯狂。

**DHH：** 它真的疯狂。

## 关于编程设置的讨论

**Lex：** 我必须问你关于你编程设置的细节，IDE，所有那种东西。让我们描绘完美编程设置的图片。

你有你享受的编程设置吗？你非常灵活吗？就像多少显示器？什么类型键盘？什么类型椅子？什么类型桌子？

**DHH：** 有趣，因为如果你问我，让我看，一年半前，我会给你与我基本上20年给任何人的同样答案。

我想要Mac。我喜欢Magic键盘。我喜欢单显示器。苹果制造令人惊叹6K 32英寸XDR屏幕，我仍然没有找到任何人击败，我仍然使用，即使我从苹果计算机切换。我仍然使用他们的显示器，因为它只是奇妙。但我一直是单屏幕类型家伙。

我确实喜欢大屏幕，但我不想要多个屏幕。我从来没有发现那真的与我的感知工作。我想能够只是专注于单个事情。我不想要它到处都是。我一直使用多个虚拟桌面，能够在那些事情之间来回切换。但我今天有的设置是Linux。

我在一年多前切换，在我最终对苹果足够厌倦，我不能再那样做之后。然后我使用这个叫Lofree Flow84的低轮廓机械键盘，这只是我听过最光荣声音键盘。

我知道有很多机械键盘鉴赏家可能会在这上面与我争论。这太thy或太clicky或太clocky或任何东西。但对我来说，Lofree Flow84只是我甚至不知道存在的喜悦。这如此有趣，因为我的意思是，我编程很长时间。机械键盘很长时间是一件事。

键盘，当你看它像这样，它只是有点，它看起来平淡，它不看起来奢华。但你从推那些键得到的触觉感觉，当键击中板时你听到的talky声音。它只是崇高。我踢自己，我在这个Mac泡泡中如此长时间，我甚至不在市场找到这个。

我知道机械键盘存在，但坦率地说，我认为它有点书呆子事情，只有比我书呆子得多的真正书呆子会关心。然后我走出苹果泡泡，突然我必须再次找到一切。我必须找到新鼠标，我必须找到新键盘，我必须找到一切。我想，好吧，让我试试机械键盘。

我试了相当多。Keychron是其中一个大品牌，我根本不喜欢那个。我试了一堆其他键盘，然后我最终找到这个键盘，我只是说，天使在唱歌。你我整个生活在哪里？我们作为程序员花如此多时间与那些键互动。

它真的有点重要，以我没有完全欣赏的方式。我过去为苹果Magic键盘辩护，就像嘿，它很棒。它实际上是伟大键盘。我认为对它是什么，这个超低轮廓，超低行程实际上是真的好键盘。但一旦你试过更长行程机械键盘，没有回头。

**Lex：** 你确实必须记住在许多方式中，在软件方面和硬件方面，你确实在计算机后面花很多小时。值得...

**DHH：** 值得投资。

**Lex：** 也值得探索，直到你找到天使开始唱歌的东西，无论什么。

**DHH：** 那正确。我实际上确实有点后悔那个，特别是与这个该死键盘。

我的意思是，我可能多年多年听这些美丽thocky键，但有时你必须真的离开，在你睁开眼睛看到其他东西存在之前。我对Linux感觉同样方式。所以我从90年代后期开始在服务器上使用Linux，可能。我们当时在Linux上运行服务器。我从来没有认真考虑它作为桌面选择。我从来没有直接自己运行Linux。

我总是想，你知道什么？我想专注于编程。我没有时间所有这些配置文件和所有这些设置胡说八道等等。苹果足够接近。它建立在Unix基础上。我为什么需要打扰Linux？再次，它是那些事情之一。

我需要尝试新事情并尝试其他东西，意识到有其他事情除了苹果。再次，不是因为我讨厌苹果。我认为他们仍然制造好计算机。我认为很多软件仍然也相当好。但我已经意识到作为网络开发者，Linux只是更好。Linux只是更好。它更接近我部署的。工具实际上现象。

如果你花一点时间设置它，你可以记录可重现环境，我现在用这个Omakub概念或项目做，我可以在不到30分钟设置新Linux机器。它完美。它不是相当好。它不像我仍然需要花两小时。

它完美，因为你可以将开发环境的所有方面编码到这个中。我不知道。公平地说，我甚至不知道Linux可以看起来像它可以那样好。如果你看股票Ubuntu或Fedora启动，我的意思是，不是它丑，但我会一周任何一天选择Mac。你看Omakub，我的意思是，我在这里有偏见，当然，因为我用我自己的感性建立它。但我看那个并说，这更好。

这美丽。然后你看其中一些真正Linux上升设置，人们与一切疯狂，你说，哦是的，我记得当计算机过去以这种方式有趣时，当有这种个性和这种设置。它不只是所有计划同样性。

我认为那是像苹果这样东西的翻面有时，他们有真的强烈意见，他们有真的好意见，他们有非常好品味。它看起来非常好，它也看起来完全同样。Linux有远更多多样性和远更多纹理和味道。有时也烦恼和错误和任何东西。但我现在运行Linux。它是基于Ubuntu的，顶部有Omakub东西。

低自由键盘。我使用罗技，它叫什么？MX 3鼠标，我爱它在我手中感觉如何。我不爱它看起来如何。我实际上最长时间是Magic鼠标支持者。我认为苹果将触控板集成到鼠标中是天才，我使用那个。

我总是认为人们只是因为你必须通过翻转它充电而抨击它是荒谬的。因为电池会持续三个月，然后你会充电半小时。我想，那与我的感性完美兼容。如果某些东西美丽，我不介意放弃一点不便，那个Magic鼠标美丽。但它不会在Linux上工作。

所以我找到其他东西。S3好，但我有时确实希望像Magic鼠标。那相当好。

**Lex：** 是的，Linux对定制一切，对平铺，对宏，对所有那个真的伟大。我也在Windows中用AutoHotKey做同样或只是定制整个事情到你的偏好。

**DHH：** 如果你是开发者，你应该学习如何用键盘控制你的环境。它更快，它更流畅。我认为我已经真正欣赏关于我Omakub设置的那些愚蠢事情之一是我可以，在刷新屏幕需要的任何时间，可能5毫秒从一个虚拟桌面切换到另一个。甚至在Windows上，你不能得到那么平滑。你可以接近，你不能得到那么平滑。

在macOS上，无论什么原因，苹果坚持在你在虚拟桌面之间切换时有这个令人愤怒动画，这使得只是你不想要。你不想运行全屏应用，因为在虚拟桌面之间切换太麻烦。

你可以从美妙Linux设置在那方面得到的那种即时性，它只是下一水平。

**Lex：** 是的，它似乎像微妙事情，但你知道，毫秒差异和在切换虚拟桌面之间的延迟，例如。我不知道，它改变...

**DHH：** 它改变你如何使用计算机。它真的做。

**Lex：** 与VR类似事情，对吧？如果有某种延迟或像它只是完全带你出来。

**DHH：** 有趣，我实际上必须观看，我认为它是YouTube上的ThePrimeagen，当他展示他的设置时，我看到他在那些虚拟桌面之间切换多快。

我一直使用虚拟桌面，但我不喜欢切换太多，因为只是那种延迟，它像，哦，你可以在Linux上做那个？哦，那相当酷。所以我运行那个，然后我现在的编辑器选择是Neovim。

**Lex：** 哦好，好吧。好吧，我们没时间了。不，好吧，你做了很多，很多年。你使用，它是什么？TextMate。

**DHH：** 是的，TextMate。

**Lex：** TextMate，是的。

**DHH：** 那实际上，那是离开苹果的主要阻碍。其他一切我想，你知道什么？我可以摆动它。但TextMate是并且是美妙编辑器。一个，我帮助诞生到这个世界。

程序员Allan Odgaard是我的好朋友，一直回到聚会日子，当我们拖我们的计算机。他是大Mac家伙。在2005年，他写这个编辑器，我帮助他项目管理有点保持他在轨道上，保持他专注于得到某些东西发布。因为我真的为我自己想要它。

我想，这是我认为我从来不会切换的最后编辑器。

**Lex：** 原谅我不知道，但这个编辑器多功能？

**DHH：** 它相当功能，但它在某些方面是GUI驱动编辑器。它真的早期有记录宏和有有点复杂语法高亮的方式。

它做了一堆第一，它只是真的愉快编辑体验。我认为这些天很多人只是使用VS Code。VS Code在某些方式中存在与TextMate同样宇宙中。实际上我认为与原始TextMate包兼容，原始TextMate格式。

所以它真的在那里追踪路径，但它也只是没有进化。现在很多人看到那个巨大问题。他们像，"哦，它需要有更多功能。它需要有所有这些事情。"我像，我对这个文本编辑器快乐。那根本没有改变。

基本上当Allan停止在它上工作十年或更多时。我不需要其他任何东西。因为正如我们原始讨论去，我不想要IDE。我不想要编辑器为我写代码。我想要文本编辑器。我想直接与字符互动。Neovim允许我以甚至比TextMate更好的某些方式做那个。我爱TextMate，但Vi正如你知道。

一旦你学习命令，它听起来，我有时感觉Vi粉丝过度播放学习它多困难，因为它也许让他们似乎有点更棒，他们能够做它。它不那么困难。在我看来，学习足够组合移动得到那种神圣狗屎的高，我不能在任何其他编辑器中做这个，不花那么长时间。

**Lex：** 你花了多长时间？顺便说一下，我不知道，我还没有。

哦，我知道，智力上。但就像与孩子，我没有一直进入。我没有使用Vim。

**DHH：** 你有心中待遇。好吧，我在大约一年前切换时大约三天切换。我有三天诅咒，我认为它绝对糟糕，它从来不会发生。我有三天烦恼，已经下周我像，这甜美。

我不去任何地方。但我也在大约20年前2000年代早期有一点领先开始。我试了Vim像一个夏天，它没有坚持。我无论什么原因当时没有爱它。但Neovim真的好。Neovim的关键是意识到你不必自己建立整个编辑器。

有很多Neovim支持者像，这是如何从头写冲突。超过17集，那会花你三周。啊，我不那么关心。我爱伟大编辑器。我爱稍微定制它，但不那么多。所以你必须将Neovim与这个叫LazyVim的东西配对。Lazyvim。

org是Neovim的分发，拿走所有苦差事，立即得到令人惊叹编辑器体验。

**Lex：** 荒谬问题，我们谈论了一堆编程语言。你告诉我们你多爱JavaScript。它是你第二喜欢编程语言。TypeScript会是第三吗？

**DHH：** TypeScript甚至不会在这个宇宙中。我讨厌TypeScript就像我喜欢JavaScript一样多。

**Lex：** 所以你讨厌什么，哦男人，我不够聪明理解那个数学。好吧，在我问关于其他编程语言之前，如果你可以将你对TypeScript的仇恨封装成可以人类解释的东西，推理会是什么？

**DHH：** JavaScript在其元编程某些方面闻起来很像Ruby。

TypeScript只是将那个复杂化到令人愤怒程度，当你试图写那种代码时。甚至当你试图写正常类型代码时，没有积累给喜欢它的人的好处。就像自动完成是我关心的东西。我不关心自动完成，因为我不使用IDE。

现在我理解那是分离它和为什么的一部分。我看不到好处。我只看到成本。我看到额外输入。我看到你有时必须做的类型体操，一堆人放弃并只是做任何，对吧？就像他们实际上不使用类型系统，因为使用它只是太令人沮丧。

所以我只是感觉TypeScript的挫折和TypeScript在代码中的混淆，给我没有回报。再次，我理解有回报。我不想要回报。所以对我的情况，我不愿意做交易，我不愿意拿在下面像Ruby一样动态的语言，然后将它变成这个假装静态类型语言。我发现那只是智力侮辱。

**Lex：** 你认为它会并且你认为它应该死TypeScript吗？

**DHH：** 我不想从享受它的人那里拿走某些东西。所以如果你喜欢TypeScript，你的大部分。如果你使用TypeScript，因为你认为那是专业程序应该做的，这是我的许可，你不必使用TypeScript。

**Lex：** 有一些深深享受关于像你这样聪明程序员，DHH正在说狗屎。它像我生活中最喜欢的事情之一。如果你与初学者交谈，每个人应该学习的前三编程语言是什么？

**DHH：** 我会100%从Ruby开始。它对初学者来说是魔法，在只是理解条件和循环和任何东西的核心概念方面，因为它让它如此容易。

即使你只是制作输出到终端的shell程序，在Ruby中运行hello-world基本上是puts，P-U-T-S，空格，开始引号，hello world，结束引号，你完成了，对吧？没有绒毛。没有什么包装它。有其他语言做那个，特别是在Perl或Python会相当类似。但Go不会，Java不会。

有很多其他语言有很多更多仪式和样板。Ruby没有它。所以它是美妙开始语言。有一本叫"学习编程"的书，由Pine写，使用Ruby本质上只是教基本编程原则，我看到大量推荐。所以那是伟大语言。

**Lex：** [Lex] 你多快会去Rails？

**DHH：** 取决于你想做什么。

如果你想建立网络应用，立即去Rails。与Rails一起学习Ruby，因为我认为真正帮助推动学习编程的是建立你想要的程序，对吧？如果你只是抽象地学习它，很难激励自己实际做它。好吧，一些人只是为了它们的乐趣学习语言。大多数人不。

大多数人学习它，因为他们有使命。他们想建立程序。他们想成为程序员。所以你必须为某些真实东西使用它。我实际上发现以那种方式学习编程也更容易，因为它驱动你的学习过程。你不能只是提前学习整个事情。你不能只是坐下来读语言规范，然后说，哦，像尼奥。

现在我知道功夫。现在我知道Ruby。它不那样下载。你实际上必须在真实程序上愤怒地输入它。

**Lex：** 是的，是的，肯定。

**DHH：** 所以我会从那里开始。但然后第二，我可能会是JavaScript。因为如果你想与网络工作，JavaScript只是你需要知道的语言。

网络是有史以来最伟大应用平台，如果你制作业务软件，协作软件，所有这种东西。如果你制作视频游戏，你可能应该去学习C++或C或其他类似东西。但如果你在网络应用领域，你必须学习JavaScript。

无论你学习什么其他，你必须学习JavaScript。

**Lex：** 所以如果你学习Ruby，Ruby在编程概念方面没有什么，你需要其他语言？

**DHH：** 我不知道是否有任何概念缺失，但它没有速度或你需要建立3D游戏引擎的内存操作低级访问，例如。没有人会在Ruby中建立那个。

当涉及网络技术时，你可以在Ruby中建立相当低级东西。但在某个点，你会击中限制，你应该使用其他东西。我不是为一切只是规定Ruby的某人。只是一旦你达到与网络应用涉及的抽象水平，Ruby是极好的。

但如果你写，例如，HTTP代理，go，它对那个很棒。我们最近在公司为各种原因写了相当多HTTP代理，包括我们的云退出等等。Kevin，我与之工作的程序之一，他在Go中写所有那个。Go只是有原语，它有步伐和速度真的很好地做那个。我高度推荐它。

如果你写HTTP一般代理，在Go中做它。对那个伟大语言。不要在Go中骑你的业务逻辑。我知道人们做，但我看不到那个点。

**Lex：** 所以你会说三个，所以Go，Ruby加Rails，JavaScript？

**DHH：** 是的，如果你有兴趣与网络工作，我会选择那三个。Go，Ruby和JavaScript。

**Lex：** Go，Ruby和JavaScript，好吧。函数式语言？

**DHH：** 某人正在谈论Ocaml。

**Lex：** 他们总是要出现。必须是某种OCaml工业复合体或类似这样的东西。但他们总是说提到OCaml。

**DHH：** 我爱有人爱函数式语言到那种程度。

那些人不是我。我根本不关心。就像我关心函数式原则，当它们在这些孤立情况中帮助我时，那只是比其他一切更好。但在心中，我是面向对象家伙。那只是我如何想程序。那是我如何喜欢想程序。

那是我如何将大问题空间雕刻成域语言。对象是我的果酱。

**Lex：** 是的，我也是。所以我为像AI应用基本编程一堆Lisp。所以奥赛罗，国际象棋引擎，那种东西。我确实试了OCaml只是强迫自己编程只是非常基本生命游戏。小模拟。它很多，你知道，Lisp只是到处括号。它实际上根本不可读。

**DHH：** 那是我与Lisp有的问题。

**Lex：** OCaml非常直觉，非常可读。它好。

**DHH：** 我真的应该在某个点拾起像那样的语言。我编程足够长时间，我实际上没有在完全函数式编程语言中愤怒地做任何真实东西有点尴尬。

**Lex：** 是的，但就像我必须弄清楚，我确信有那个答案。

我可以做什么对我有用，就像我实际上想建立？

**DHH：** 是的，是的，那是我的问题

**Lex：** 函数式语言更适合。

**DHH：** 那正确。

**Lex：** 因为我真的想适当体验语言。

**DHH：** [DHH] 那正确。

**Lex：** 是的，因为我仍然，是的。在这一点，我非常面向对象大脑。

**DHH：** 是的，那也是我的问题。我不那么关心计算机科学中这些低级问题。我关心高级。我关心写软件。我关心与网络应用和业务逻辑真的很好漂浮的抽象层。我已经接受关于我自己那个。

即使，正如我们谈论当我是孩子时，我真的想成为游戏程序员。然后我看到写碰撞检测引擎需要什么。我说，是的，那根本不是我。我从来不会进入向量矩阵操作或任何那种东西。

它太多数学，我更多是写作人，而不是数学人。

**Lex：** 我的意思是，只是在你今天说话的方式中，你有像诗意文学编程方法。

**DHH：** 是的。

**Lex：** 是的。

**DHH：** 有趣，那实际上正确。所以我实际上在10年前RailsConf做了主题演讲，我称自己软件作家。我的意思是，我不是说那个的第一人。软件作家在白话中很长时间。

但大多数程序员在试图严肃时采用的现代身份是软件工程师。我拒绝那个标签。我不是工程师。偶尔我涉足一些工程，但绝大多数时间，我是软件作家。我为人类消费和我自己喜悦写软件。

我可以逃脱那个，因为我在像Ruby这样高级语言中工作，在协作软件和待办事项列表和所有其他东西上工作。再次，如果我试图将我的才能应用于写3D游戏引擎，不，那不是正确心态。那不是正确身份。但我发现软件工程身份稍微平坦化事情，我喜欢想我们有软件作家和软件数学家，例如。

然后那些实际上是描述你工作的抽象水平比工程师更丰富方式。

**Lex：** 是的，我认为如果AI变得越来越成功，我认为我们会越来越需要软件作家技能。因为感觉那是领域，因为它不是作家。你会必须做软件。你会必须是计算机人。

但有更多...我不知道，我只是不想浪漫化它，但它更诗意，它更文学。它更感觉像写好博客文章，而不是...

**DHH：** 我实际上希望AI对写作有更高标准。我发现它接受我草率，不完整句子的事实有点冒犯。我希望有像AI严格模式，它会打我的手指。

它只是喂它关键词，像说适当，做发音，做标点，因为我爱那个。我爱制作恰好正确句子，没有被煮沸，它没有肉，它没有字符。它简洁。它不过度花哨。

它只是正确，那个写作阶段，对我来说，只是令人上瘾。我发现当编程最好时，它几乎完全等同于那个。你也必须解决问题。你不只是传达解决方案。你必须实际弄清楚你试图说什么，但甚至写作有那个。

一半时间当我开始写博客文章时，我不确切知道我要使用什么论点。他们作为写作过程的一部分发展。那也是写软件如何发生。你大致知道你试图解决的问题类型。你不确切知道你如何解决它。当你开始输入时，解决方案出现。

**Lex：** 实际上，据我理解，你和杰森正在写新书。它在那种主题的早期日子。我认为他说，他推特它会被标题像，我们不知道我们提前做什么，类似那样。那种主题。你沿途弄清楚。

**DHH：** 那是它的大部分。

试图给更多人许可信任你自己的本能和他们自己的直觉，意识到发展你胃中那个超级计算机实际上是职业生涯的工作。你不应该丢弃那些感觉，偏好过度复杂...或甚至不复杂，分析，智力主义。

经常当我们看我们必须做的大决定时，他们来自直觉，你不能完全阐述，为什么我认为这是正确事情？好吧，因为我在这个业务中20年，我看到一堆事情，我与一堆人交谈，那正在渗透成这是正确答案。

很多人对业务中那个非常怀疑，或无法信任它，因为感觉他们不能合理化。我们为什么做某些东西？好吧，因为我感觉像它，该死。那是自举独立创始人的伟大特权，他们不欠他们的业务给其他人，不必产生回报。

因为我感觉很多真的爬进来，当你试图向其他人合理化你为什么做你做的事情，为什么你做你做的决定。如果你没有任何人回答，你自由跟随你的直觉。那是地狱享受工作方式。它也经常是正确工作方式。你的直觉知道很多，就像你不能阐述它，但它比不更多时候准确。

**Lex：** 是的，必须制定计划可以是瘫痪事情。

我经常，我的意思是，我想有不同类型大脑。首先，如果它实现，我不能等读那本书。我经常感觉在我生活中做的更有趣事情中，我真的不知道我提前做什么。我认为有很多关心我的人真的想要我知道我在做什么。

他们像，计划是什么？你为什么做这个疯狂事情？如果我必须等到我有计划，我不会做它。他们在这种东西上有不同大脑。一些人真的是计划者，它也许激励他们。我认为大多数创造性追求，大多数真的有趣，大多数新颖追求像，你有点必须只是跳跃，然后只是弄清楚当你去。

**DHH：** 我在"重新工作"中最喜欢文章是最后一个，它标题，灵感是易腐的。

我认为那捕获很多它，如果你花时间做详细计划，到你完成时，你很可能失去灵感。如果你在那一刻跟随灵感并信任你的直觉，信任你自己的能力，你会弄清楚它，你会得到如此多更多回来。你会去你否则不会有的冒险。

无论那只是业务决定或生活决定，你必须抓住那种灵感。有这个日本作者写的伟大儿童书籍集合，关于追逐想法并试图抓住它，它美丽地说明作为想法某些东西，作为你必须捕捉并抓住的某些东西漂浮。

我真的感觉捕获这个概念，灵感是易腐的，它会消失。如果你只是把它放回架子上说，好吧，我必须对这个勤奋。我必须排列计划。你可能用完，然后没有蒸汽继续。

## 关于开源的讨论

**Lex：** 我必须问关于开源。

运行成功开源项目需要什么？你说过那个。开源是民主的是误解。它实际上是精英制。那是美丽方式说它。所以经常在顶部有某种也仁慈独裁者。所以你能只是说到那个吗？运行成功开源项目自己并自己成为仁慈独裁者。

**DHH：** 这会是这里有点偏见证据，但我...

**Lex：** 但为什么君主制最好。

**DHH：** 它伟大。我们绝对应该有独裁者，他们应该控制一切，特别是当独裁者是我时。现在，好吧，我认为我很早学到在开源中燃尽的快速方式是将它作为业务对待，好像你的用户是客户，好像他们对你的时间和你的注意和你的方向有合法性声明。

因为我几乎立即与Ruby on Rails面临这个。一旦它被发布，有一百万人对我应该带它去哪里有各种意见。不只是意见，而是实际要求。除非你实施Oracle数据库适应，这总是要是玩具。

它实际上或多或少那个确切要求促使我在早期Rails会议之一有幻灯片，只是说，操你。

**Lex：** [Lex] 是的，我看到那个。

**DHH：** 我不会做你告诉我的。我在这里作为礼物带来者。我分享我在我自己时间，我自己意志写的代码。你不必说谢谢。我的意思是如果你做会很好。

你可以拿代码并用它做任何你想要的。如果你想要，你可以贡献回来，但你不能告诉我做什么或去哪里或如何行动。我不是供应商。这是开源用户偶尔步入的基本误解，因为他们习惯从真正关心他们业务的公司购买软件。我关心人们使用我的软件。

我认为它伟大，但我们没有交易关系。当你告诉我做什么时，我没有得到某些东西回来，除了悲伤。我不想要它，所以你可以保留它。所以我从开始的开源哲学一直是，我必须主要为我做这个。我爱当其他人在我的开源中找到用途。它不是我的主要动机。

我不主要为其他人做它。我主要为我和我自己的目标做它，因为正如亚当·史密斯说，不是为了屠夫的仁慈，我们期望我们的日常肉。它为了他的自利。我实际上发现那是美丽想法，当我们都追求我们的自利时，我们的评论增加价值。

肯定在开源领域。这也是为什么我拒绝这个概念，开源在某种危机中。有资金危机，我们必须花更多。不，我们不。开源从来没有做得更好。开源从来没有控制软件中比现在更多域。没有危机。

有一些制作开源的人和很多使用开源的人的误解。开源主要像商业软件。你购买的某些东西，某些东西，你然后可以作为客户做要求。客户总是对的。客户不总是对的。甚至在业务中不，但肯定在开源中不。

在开源中，客户，正如它是，是礼物接收者。我们有礼物交换。我出现并给你我的代码。如果你喜欢它，你可以使用它。如果你有一些代码适合我与这个去的地方，我会爱得到那些礼物回来。我们可以继续像那样交易。我给你更多礼物。你给我一些你的礼物。

一起，我们汇集所有礼物，这样某人全新出现，只是得到礼物山。这是开源的魔法事情，当我们都追求我们自己的自利时，它增加在公共中的总和价值。所以我为Rails建立我需要的事情。你知道什么？你想要我做那个。

你不想要我代表其他人建立我不需要的事情，因为我会做垃圾工作。当我可以通过我自己的使用评估那个软件的质量时，我建立更好软件。我需要这个功能。我要建立那个功能的好版本，我要建立只是足够只是为我。所以我不会膨胀它。我不试图在这里吸引客户。

我不试图看一些角度。我只是建立我需要的。如果你进入开源与那种心态，你为你建立，其他一切是奖金，我认为你有所有成分去距离。我认为在开源中燃尽的人是当他们进入想，"我制作所有这些礼物。

我自己真的不需要它们，但我像希望其他人做，也许他们也会给我一些钱。"那是失败命题。它从来基本上不工作。如果你想要你软件的钱，你应该只是卖它。

我们有人们可以制作那种的商业软件完全好模型，然后他们可以卖它。但我发现很多混乱，让我们只是礼貌地称它那个，在想要有他们的蛋糕并也吃它的开源贡献者中。他们喜欢与开源工作的模式。他们甚至可能喜欢来自开源的地位，但他们也想要为制作那个开源谋生。

因此，他们偶尔最终有某人在工作中感觉不被欣赏会发展的那种不满，当其他人不做足够认识他们的伟大礼物时。

**Lex：** 然后他们可能走开。我希望我对运行这些项目的个人的心理状态有更多洞察，就像他们是否感觉悲伤或他们需要更多钱或他们像，它只是如此黑盒。

**DHH：** 它可以是。

**Lex：** 我的意思是当然有一些沟通，但我只是悲伤地看到太经常他们只是有点走开。

**DHH：** 对，我认为那实际上也是开源美丽的一部分。

**Lex：** 走开。

**DHH：** 你没有义务永远做这个代码。你有义务做这个，只要你想做它。

那基本上是你自己的义务。

**Lex：** 但有一个，我知道，好吧...所以你可能批评一些反击。你确实写了博客文章关于永远，直到互联网结束与ta-da列表。有美丽方面，你在那里找到好平衡。

但我不知道，你用你创造的这个东西给人们带来如此多快乐。它不是义务，但照顾你创造的这个东西有真实美丽。有并且不忘记。我认为，我认为开源创造者没有看到足够，我的意思是有像，你让多少生活更好。有某些软件片段，我只是安静地使用很多。

就像他们给我的生活带来快乐，我希望我可以很好地沟通那个。有捐赠方式，但它低效。通常很难捐赠。

**DHH：** 它是。有一些人的一些方式让它更容易。GitHub捐赠是做它的一种方式。我捐赠给几个人，即使我不爱范式。我也接受我们可以有多个范式。

我接受我可以为一组动机做开源，其他人可以为其他动机做开源。我们不都必须以同样方式做它。但我确实想要反驳误解，开源以某种方式在危机中，除非我们都开始为开源付费。那个模型已经存在。它是商业软件。

它工作得非常好，很多伟大公司已经在它背后建立。期望非常清楚。我付你这个数量，我得到这个软件。开源，一旦你开始将钱混合到它中，变得真的泥泞真的快。很多它只是来自那些错位期望。如果你感觉你是饥饿艺术家作为开源开发者，你被欠X数量钱，因为你的软件流行，你妄想，你需要停止那个。

只是回到轨道，你意识到你正在将礼物放入世界。如果你在货币补偿方面得到某些东西回来，好吧，那是奖金。但如果你需要那个钱在货币补偿方面回来，你只是为软件收费或去为会雇佣你做开源的软件公司工作。有大量那个。

那可能实际上是今天世界上开源软件被开发的主要模式。商业公司制作他们自己需要的开源，然后贡献它回来。

**Lex：** 所以我很高兴你有点在这里画一些硬线。这是好时刻提出我认为是，也许有史以来最伟大开源项目之一，WordPress。

你在2024年10月说到一些与WordPress创始人Matt Mullenweg正在进行的东西，在博客文章开源皇室和疯王中。它是真的好博客文章，关于仁慈终身独裁者的想法，这个开源项目模型。然后基本含义是Matt作为WordPress的BDFL与WP Engine的战斗有点失去他的方式。

所以我也应该说我真的爱WordPress。它给我带来快乐。我认为它真的...它是开源可能是什么的灯塔。我认为它让互联网更好。它允许很多人创造美妙网站。我也认为，现在你可能不同意这个，但从我看到的一切，WP Engine只是给我坏感觉。

我认为他们不是这个中的好家伙。我不喜欢它。我理解挫折。我理解所有它，但我不认为那原谅行为。有一点，看这种有点反对你说的一点，这是当你有那个大小的开源项目时，有一点，就像当你是那么大王国项目的王时，有一点责任。

无论如何，你能说到你的，也许你对Matt的共情和你的批评，也许描绘他和WordPress如何再次获胜的路径？

**DHH：** 首先回应你关于WordPress存在是多美妙事情说的。在开源世界或大体世界中没有很多项目对互联网有像WordPress那样大影响。他为那个工作应得大量赞誉。

所以那是我的参与，本质上我的前提。你知道什么？我对Matt用WordPress建立的有巨大尊重。那个整个生态系统围绕自己建立的。它是真正奇迹。但有一些原则比我对涉及角色的个人同情更大。我同意。

与WP Engine涉及的Silver Lake私人股本公司不是我的自然盟友。我不是私人股本与VP Engine做一些游戏的自然盟友。那不是我在案例中的兴趣。我的兴趣本质上是一组原则。原则是如果你发布某些东西作为开源，人们自由使用它，正如他们认为合适。

他们自由捐赠代码或资源或钱回到社区，正如他们认为合适。你可能不同意他们是否做了足够，他们是否应该做更多，但你不能在你给世界免费软件礼物后出现，然后说，现在你使用了那个礼物，你实际上欠我你业务的巨大滑动，因为你使用我免费给你的东西变得太成功。

你不能拿回礼物。那就是为什么我们有开源许可证。他们确切规定方程式两边的义务是什么。开源用户不能要求开源制造者做什么和如何行动。开源制造者不能突然带着勒索信出现给用户说，实际上你欠我各种使用。我对那种互动100%过敏。

我认为Matt，不幸地，无论什么原因，如此包裹在他被欠的东西中，他没有意识到他正在摧毁什么。WordPress和Automatic已经赚很多钱。这是WordPress奇迹的一部分。这是产生数亿美元的项目，Matt没有感觉他得到足够那个。

那不是好论点，兄弟。你不能只是违反这些开源许可证的精神和字母，只是开始带着要求信出现，甚至对不特别同情的角色。这去我对开源一般解释的根。

GPL是实际上在某些情况下要求使用它的人代码的特定许可证。我从来不喜欢GPL。如果你不想给我，我不想要你的垃圾代码。我要用那个做什么？一些代码转储，你...我根本不支持Stallman愿景的那部分。我爱MIT许可证。对我来说，那是完美许可证，因为它仁慈地短。

我认为它是两段，三段。真的短，它基本上说，这是一些软件。它没有保修。你不能起诉我。你不能要求任何东西，但你可以用它做任何你想要的地狱。有好生活。那在我看来是完美开源互动。那个许可证需要被维护。

这些许可证，一般，甚至GPL，即使我不喜欢它，我们必须遵守它们。因为如果我们只是在我们在瞬间通知感觉某些东西稍微不公平时搁置那些许可证，我们失去一切。我们失去允许开源繁荣并允许开源成为商业如此整体部分的整个框架。

我的意思是，回到开源最初找到它的脚时，它与商业软件战争。Stallman与商业软件战争，总是有。比尔·盖茨反过来与开源战争最长时间。开源许可证和它们提供的清晰允许我们结束那场战争。今天，商业软件和开源软件可以和平共存。我制作商业软件。

我卖Basecamp，我卖HEY，然后我也制作一堆我作为礼物免费给的开源软件。如果我们开始违反这些合同，那不能发生。没有商业公司会说，"让我基于这个开源片段我的下一个项目。

如果我也运行一些疯狂制造者会在七年后出现并要求的责任，我给他们5000万美元。"那不是有利于商业协作或其他任何东西的环境。它只是基本上错误。我认为有一个分析，所有关于这种实际结果，我认为是坏的。

也有一些简单关于伦理的论点。这不对。你不能只是之后出现并要求某些东西。在我看来，这与我们之前谈论的整个苹果事情不太不同。苹果只是出现并感觉他们有权得到每个人业务的30%。不，那不对。

那不公平。所以我认为Matt不幸地有点盲目地引导自己在他认为正在对他犯下的侮辱上，因为有所有这些钱被VP Engine制作好产品赚取，在Matt看来没有给足够回来，艰难饼干。

**Lex：** 我认为那里，也许我读太多到它中，但可能有一些个人东西也，他们不仅没有给足够，而且可能隐含地承诺他们会给，然后以那种方式在他心中利用他，只是像人际互动。然后你得到像人际沮丧，忘记像它的更大图片伦理。它像当一个家伙继续说，你知道，承诺你会做某些东西。

然后你意识到你一天醒来，像一年或两年后。等一分钟，我整个时间被撒谎，我甚至不知道它是否关于钱。

**DHH：** 我也会生气。当人们让你失望时，生气完全好。那不是颠覆几十年开源许可证和我们围绕它建立的基本事实案例法的理由。

这就是为什么我选择甚至权衡这个，因为我喜欢WordPress。我不使用WordPress。我不是那个社区的一部分。我实际上在这场战斗中没有狗。如果有什么，我偏向Matt，只是作为同伴BDFL。我想看他在这个上做得好。但我也认为这里有一些原则，响得更大声。

我不想Rails突然被它是开源的事实玷污，公司是否可以依赖它并在它上建立业务。因为等等，也许有一天我要转Matt，我要转疯王，我要带着要求勒索信出现。不，去那个。我们在这里有更多保护。有比你与某人的个人牛肉或你对你被欠什么的感知不满更多利害关系。

**Lex：** 你会推荐什么？你认为他应该做什么，可以做什么来走回它来治愈？

**DHH：** 决定，这是好奇事情。他可以决定放弃这个。对驱动，雄心勃勃的人来说，那非常，非常困难。接受他们错误并放弃并放下他们的剑。所以我早期在这个中有希望那是可能的。我没有看到任何证据Matt对那个有兴趣，我发现那深深遗憾，但那是他的特权。

我继续说出，当他违反开源的精神和伦理时，但我希望他只是接受这是真的坏想法。他做了坏赌注，我想，我认为他认为他只是逃脱它。他们只是付钱，他可以施加压力。我的意思是，我知道那种诱惑。

当你坐在非常重要项目的头时，你知道那带着巨大程度权力，你真的需要巨大程度纪律来统治那个并且不在你感觉受委屈的每个步骤行使那种权力。我在Ruby on Rails的20多年中感觉受委屈一百万次。我真的非常努力尝试不让那些...

有时琐碎，有时实质性不满随时间渗透到生态系统基础并冒险毁掉一切。

**Lex：** 作为Rails王国的王，权力多年来冲昏你的头脑吗？

**DHH：** 我确信它有。我的意思是，谁不会？

**Lex：** 你在你的房间中愤怒地踱步吗？

**DHH：** 我偶尔做。

我确实惊叹于已经建立的，已经可能的。通过我看到的一个估计，超过一百万应用已经用Ruby on Rails制作，像Shopify和GitHub和一百万其他的业务已经在我开始的某些东西之上建立。那非常满足。但你真的必须小心不要太多闻你自己的排气。

你必须同样小心不要太多听仇恨者，不要太多听超级粉丝，你评估你正在朝向工作的价值和有点原则在它自己优点上，在你自己记分板上。我试图阻止那个，然后只是去，好吧，我在Rails上工作，因为我爱写Ruby。我爱使用Ruby制作网络应用。

那是我的北极星。我会继续做那个，我会继续分享我沿途发现的所有开源礼物，那就是它。那也足够。我不必从它得到所有。这有时只是与认为我放弃成为Jira或某些东西的家伙，而不是做Basecamp，多年来有人问，为什么你不为Rails收费？就像，你不知道多少钱已经从Rails赚取吗？如果我们只是看像Shopify这样的东西，

它价值数十亿美元。我不是亿万富翁，所以该死什么？我得到足够多。我得到我的份额很多。我会说虽然，我也足够内省意识到，如果它没有为我在我自己业务上如此好地成功，也许我会更诱惑。也许如果你看其他人在你工作背后建立巨大成功公司，你真的没有撒尿的锅，你可能诱惑对那个有点沮丧。

我在Rails世界中也看到那个，有人贡献实质性工作体，然后当他们没有感觉他们得到足够回来时真的恼火。我足够幸运，杰森和我用Ruby on Rails建立的业务像它那样成功。

我赚了我需要赚的钱，我不需要追逐其余它。

**Lex：** 但我们也应该只是明确，你位置的很多人追逐钱。追逐不那么困难。基本上你拒绝钱。你做了很多只是拒绝钱的决定。

**DHH：** 也许，我也想这个与Matt的例子。

他可能认为有容易钱拿。它不那么容易，是吗？它看起来像低悬美元钞票，它们结果是一些真的酸葡萄。结果他可能通过破坏整个WordPress信任和生态系统并在选择使用WordPress或其他东西向前的人们头脑中放置问号摧毁巨大金钱总和。

所以我经常想当人们想，你在桌子上留下钱。首先，所以什么？我不必有所有钱。但第二，也许钱根本不在桌子上。

**Lex：** 也许成本。即使你得到钱，也许其他方式的成本，就像我们谈论的，会超过你可能得到的所有钱。

意思像我认为你说让你快乐的事情是心流和宁静。那两个事情。美丽地，真的美丽地说。它，你知道，获得钱可能分配给你运行更大事情的责任，拿走你从成为...基本上对你心流意味着什么是编程得到的心流。

然后宁静像，我认为你也有美丽文章，像涅槃是空时间表。

**DHH：** 当我看即将到来的周，我看到我根本没有安排会议，这相当常见，或者也许我只是在一天有一小时一件事。我对自己想，你知道什么？这很容易非常不同。

我们可能运行数百人或数千人的公司，我的整个日历会被其他人对我注意和时间要求的小俄罗斯方块紧密包装，我会悲惨如操。我看那个并说那个，我还能要求什么？这是真的好存在状态，我实际上会说。我不总是有这个。

我确实在我职业生涯早期有一些感觉，我需要更多一点。更多一点安全。我记得这个真的有趣研究，一堆研究者问制作某些数量钱的人，需要多少钱让你感觉安全？他们问有一百万美元网络的人？你需要多少钱？可能需要200万。200万然后我会好。

然后他们问净值500万的人，你需要多少？啊，10，我需要10。问有1000万的人，你需要什么？20。每次人们需要他们做的两倍。我做那个几个加倍，直到我意识到，你知道什么？这愚蠢。我已经在我希望我会是的地方，一百万倍。所以还有什么更少追求？现在那不意味着如果更多钱来我的方式，我要对它说不。当然不。但它确实意味着我自由设置其他事情更高。

我也确实认为你意识到，正如吉姆·凯瑞会说，我希望每个人会得到他们希望的所有钱，他们会意识到它不是答案。钱解决一整套问题和焦虑，然后它创造一堆新的。然后它也根本不触摸人类体验的巨大片段。

世界充满悲惨，焦虑，受伤，富有人。它也充满悲惨，焦虑，贫穷人，我宁愿是悲惨，焦虑，富有人，而不是贫穷人。但它不是让一切消失的这个魔法棒。那再次，那些洞察之一，就像有孩子，你不能用话语沟通。

我从来没有能够说服不富有的人，财富不会解决他们所有问题。

**Lex：** 你经常回到的一个引用，我很享受，是可可·香奈儿引用，生活中最好事情是免费的。第二好事情非常，非常昂贵。我想任务是专注于用生活中最好事情围绕自己，像家庭和所有这个，不关心其他东西。

**DHH：** 我会容易说你可以关心其他东西。只是知道优先级顺序。

如果你被祝福有你爱的伴侣，一些你崇拜的孩子，你已经赢得大多数人类能够实现的最伟大奖品。这个世界中大多数人类，如果他们是结婚年龄，他们有孩子，如果你问他们什么是最重要事情，他们都会说那个。他们都会说那个。无论他们富有或贫穷。

当你追逐第二好事情时，容易失去那个视线。因为你知道什么？他们也非常好。我真的喜欢那个帕加尼Zonda。它是非常昂贵汽车，如果我没有在业务中变得相当成功，我不会有获得它的机会。所以我也不想解雇它。有钱很有趣。

它只是不像你认为的那样有趣那么长或那么深。这些其他事情，有你享受的职业和追求，能够用僵硬嘴唇承担负担，再次，有意义感是令人难以置信。有家庭，有朋友，有爱好，有所有这些实际上对世界各地大多数人可用的事情，那是获胜。

它不意味着你必须折扣你的雄心，它不意味着你不能达到更多，但它确实意味着如果你不意识到它不会以某种胡说八道woo感觉完成你制作更多，那相当愚蠢。它真的不是。

**Lex：** 什么给你关于我们在这里进行的这整个事情未来的希望？人类文明。

**DHH：** 我发现乐观比悲观更容易，因为我两种方式都不知道。所以如果我得到选择，为什么不只是选择相信它会成功？就像我们在我们想象中比我们在现实中遭受更多。那是来自斯多葛主义的引用之一。我也认为我们有倾向，很多人类有倾向对他们不知道如何成功的事情提前悲观。

例如，气候变化让很多人对未来非常焦虑和非常悲观。你什么都不知道。40年前我们认为问题是地球会太冷。我碰巧相信地球变得太热可能正确，CO2与它有关系。

我们是否有正确措施及时修复它，如果那甚至可能或不，完全悬而未决，我们不知道。如果你如此确定地说服自己世界会变成狗屎，它是。就在这里，在你头脑中今天。气候变化可能在200年内消灭这整个物种不是明年。

它不是从现在10年。生活可能变得更不愉快，可能有更多负面影响，等等。是的，好吧，但然后当它到达时处理那种困难。不要提前拿那个。你通过只是走来走去沮丧如何帮助地球？

**Lex：** 我认为我们今天整个对话也是指示。它只是两个人类交谈。

有数十亿我们，有一些关于我们想要解决问题并建立酷东西的东西。所以我们要建立我们走出我们让自己进入的任何东西的方式。这是人类做的。我们为自己创造问题并想出，弄清楚如何建立火箭船走出那些问题。

有时火箭船创造其他问题，像核弹头，然后，我确信，我希望，弄清楚如何避免那些问题的方式。然后会有纳米机器人，然后外星人会来，会有纳米机器人和外星人之间的巨大战争，那会将我们所有人类聚集在一起。

**DHH：** 有趣事情，只是拾起你提到的点之一，例如原子弹。

当那个首先被发明时，很多人认为我们本质上结束地球上生命，对吧？或者也许我们阻止第三次世界大战在过去80年发生，因为确保相互毁灭保持超级大国不攻击彼此至少正面，保持他们的战斗到代理战争。

你知道什么？代理战争不伟大，但他们可能比有核武器的第三次世界大战更好。所以在那一刻告诉什么实际上好处，什么不是相当困难。我认为我们应该更谦逊一点。我肯定随时间变得更谦逊，想我知道它会转哪种方式。

我认为大流行对很多人是巨大时刻，有如此多确定性关于这种干预是否工作或那种干预没有工作。大多数人错了。肯定很多非常聪明人，非常合格人得到那个只是完全和灾难性地错误。

所以只是一点智力谦逊，我想回到那个并说，你知道什么？我不是病毒学博士，我不声称像我以某种方式看到它总是如何发挥。但真的是它专家的人，他们得到一堆它错误。没有人知道任何东西。我每天提醒自己那个。没有人知道任何东西。

我们不能预测经济一个月。我们不能预测世界事务一个月。世界只是太复杂。

**Lex：** 是的，当我观看Netflix纪录片"黑猩猩帝国"，你知道，有黑猩猩等级制度，所有那个看起来令人毛骨悚然地类似我们人类。我们是最近后代。所以这些专家，一些黑猩猩得到博士学位，其他没有。

其他真的肌肉发达。其他像beta男性类型。他们吸到alpha。有很多有趣动力进行，真的干净地映射到当天地缘政治。他们没有核武器，但他们行为性质类似我们的。

所以我认为我们勉强知道正在进行什么，但我确实认为有像基本合作意志。基本同情，基础只是在那里的人类精神。也许那只是我乐观。但如果那确实在那里，然后我们会好。

**DHH：** 能力肯定在那里。

我们是否选择那种能力或不，谁知道，在什么情况下？我认为接受我们都有两种方式的能力，对令人难以置信慷慨和善良也残忍。我认为，荣格，与这个整个阴影理论真的准确。我们都有那种能力在我们中，接受试图培养我们人性更好部分是我们的工作，权衡我们有时成为我们自己最糟糕的倾向。

**Lex：** 我兴奋发现会发生什么。

成为人类如此棒。我不想死。我有点想活一段时间看我们做的所有酷狗屎。我想看的酷事情之一是你创造的所有软件和你推特的所有事情。你在Twitter上让自己陷入的所有麻烦。大卫，是的，我是巨大粉丝，就像我说的。

谢谢你为世界做的一切，为你启发的数百万开发者，其中一个是我。谢谢这个棒对话兄弟。

**DHH：** 非常感谢有我。

**Lex：** 感谢听这个与DHH的对话。

支持这个播客，请检查我们在描述中的赞助商，考虑订阅这个频道。现在让我用DHH和杰森·弗里德的"重新工作"中的一些话语离开你。你做什么重要，不是你想或说或计划什么。谢谢听，我希望下次见到你。

## 关于Chrome和反垄断的讨论

**Lex：** 我们要进行切线上的切线上的切线。所以让我们去Chrome。我认为Chrome对人类的积极影响是不可估量的，原因就是你刚才描述的。在技术方面，它们呈现的功能，它们创造的竞争，它刺激了Web技术的美妙繁荣。但无论如何，我必须问你关于最近司法部试图分拆Chrome和谷歌的事情。

你认为这是一个好主意吗？你认为这会造成伤害吗？

**DHH：** 这是一场灾难。我这样说是作为一个对反垄断斗争非常同情的人，因为我确实认为我们在技术中有反垄断问题。但我们没有这些问题的一个地方，总的来说，是浏览器，是我们用来访问开放网络的工具。

首先，我们有Firefox。现在Firefox做得不是很好。Firefox多年来一直由谷歌支撑，以阻止正在与司法部发生的事情，即他们是镇上唯一的游戏。苹果有Safari。我对苹果也有很多问题，但我喜欢Safari。我喜欢我们有一个在首要操作系统上运行的首要浏览器，人们不能将网络变成只是Chrome体验的事实。

但我也认为开放网络需要这个万亿美元的冠军，或者至少从中受益。也许它不需要它，但它肯定从中受益。在技术中垄断形成的所有错误事情中，Chrome是最后一个。

这就是为什么我有时对反垄断斗争感到如此沮丧，有真正的问题。我们应该首先关注首要问题。比如我们手机上的收费站。它们是更大的问题。不是开放网络。不是我们用来访问开放网络的工具。如果我不想使用Chrome，如果我的在互联网上运行的业务的客户不想使用Chrome，他们不必这样做。

我们从来没有被迫通过它。开放互联网仍然是开放的。所以我认为司法部选择以这种方式追求谷歌真的很遗憾。我确实认为有其他事情你可以为谷歌钉住，他们的广告垄断也许，或者在控制广告分类账的两边所做的恶作剧，他们既控制供应又控制需求。

有问题。Chrome不是吗？你最终会让网络变得更糟。这是我们在考虑立法时，当我们考虑垄断斗争时，我们总是必须记住的事情，你可能不喜欢今天的事情看起来如何。你可能想对此做些什么，但你也可能让它变得更糟。

欧洲GDPR背后的良好意图目前已经达到了什么？每个人在互联网上讨厌的Cookie横幅。这不能帮助任何人做任何更好的事情，任何更有效的事情，以任何方式、形状或形式保存任何隐私，这是一个完全的失败，只丰富了律师和会计师和官僚。

**Lex：** 是的，你说Cookie横幅是欧洲在技术方面做得最差的所有地区的纪念碑。

**DHH：** 这是良好意图直接通向地狱的纪念碑。欧洲实际上在良好意图直接通向地狱方面是世界级的。

**Lex：** 所以地狱看起来像Cookie接受按钮，你必须接受所有Cookie。那就是地狱的样子，一遍又一遍。你实际上永远不会到达网页。

**DHH：** 只是在人类规模上，试着想象每天有多少小时浪费在点击那个上面。我们对网络作为人们享受的平台造成了多少伤害，因为它们。互联网部分是丑陋的，因为Cookie横幅。

Cookie横幅应该拯救我们免受广告，广告可以让网络变得丑陋。有很多这样的例子，但Cookie横幅在一次感觉中让整个互联网变得丑陋。这是一个完全的悲剧。但更糟糕的是，这就是为什么我称之为欧盟搞错的一切的纪念碑，是我们已经知道这一点十年了。

没有任何地方认真的人相信Cookie横幅对任何人做任何好事。然而我们一直无法摆脱它。有这一个我认为现在10或12年的立法。在每个可以想象的指标上都是完全失败的。每个人都普遍讨厌它，但我们似乎无法对此做任何事情。

这是任何假装或假装为不仅仅是公民，而是世界各地的人们让事情变得更好的官僚机构的破产声明。这就是Cookie横幅真正让我恼火的地方。这不仅仅是欧盟。这是整个世界。你在这个星球上的任何地方都无法躲避Cookie横幅。

如果你去到该死的火星乘坐埃隆的火箭之一，试图访问网页，你仍然会看到Cookie横幅。宇宙中没有人能逃脱这种荒谬。

**Lex：** 可能是火箭上的界面。

**DHH：** 它会更慢。你基本上会有150秒的ping时间。所以从火星通过Cookie横幅需要45秒。

**Lex：** 好吧，让我们回到我们一直在进行的这些递归切线的堆栈。所以Chrome，我们应该说，至少在我看来，不是不公平地获胜。它通过公平的方式获胜，只是更好。

**DHH：** 是的，如果我要为另一边偷人一半秒，人们会说，好吧，也许是的，大多数人确实有点勉强同意这是一个相当不错的浏览器。

但然后他们会说它获得主导地位的原因是分发。它获得分发的原因是因为谷歌也控制Android，因此可以使Chrome成为所有这些手机上的默认浏览器。现在我不买那个。

我不买那个的原因是因为在Android上，你实际上被允许发布一个具有与Chrome不同的浏览器引擎的不同浏览器。与iOS不同，如果你想发布浏览器，Chrome，例如，为iOS发布，但它不是Chrome，它是穿着裙子的Safari。iOS上的每个替代浏览器都必须使用Safari Web引擎。那不是竞争。那不是Android上发生的事情。

再次，我认为有一些细微差别，但如果你缩小并查看我们与大技术的所有问题，Chrome不是它。Chrome凭借优点获胜。我勉强地基于那个认识单独切换到Chrome。作为Web开发者，我只是更喜欢它。我在许多方面喜欢Firefox。我喜欢它的精神，但Chrome是比Firefox更好的浏览器，完全停止。

**Lex：** 顺便说一下，我们从来没有提到Edge。Edge也是一个好浏览器。

**DHH：** 因为它也是穿着裙子的Chrome。

**Lex：** 但它从来没有得到爱。我不认为我曾经使用过Bing，我确信Bing真的很好。

**DHH：** 也许你有，因为你知道什么？Bing穿着裙子吗？

**Lex：** 什么？

**DHH：** DuckDuckGo。这实际上是我使用的搜索引擎。DuckDuckGo从Bing获得其搜索结果，或者至少它曾经这样做。如果他们改变了那个，那对我来说是新闻。

**Lex：** 好吧，也许一切都只是一个包装或裙子。一切都在下面穿着裙子。有一些其他的。

**DHH：** 有一些那个。

**Lex：** 乌龟，所有的裙子一直向下。

好吧，我们在谈论什么？他们，我们从JavaScript和你学习如何编程到达那里。所以最终，大成功故事是当你用PHP构建了一堆东西，你实际上在发布东西。那就是Ruby故事出现的时候。所以你与编程的大爱情故事从那里开始了吗？你能带我去那里吗？什么是Ruby？告诉我Ruby的故事。

解释Ruby给我。

**DHH：** PHP是将我从只能摆弄HTML并制作一些网页转换为实际能够自己制作Web应用程序的东西。所以我对PHP在这方面欠下巨大的感激。但我从来没有把PHP看作是一个召唤。我从来没有想过，我是一个写PHP的专业程序员，那就是我是谁，那就是我做的。

我把PHP看作是我需要用来敲击计算机直到它产生我想要的Web应用程序的工具。这非常是达到目的的手段。我没有爱上PHP。我非常感激它教会了我编程的基础知识。我非常感激它为经济学设定了标准。但直到Ruby我才开始把自己想象成程序员。

这发生的方式是我第一次被雇佣为专业程序员写代码实际上是由杰森·弗里德，我的商业伙伴。一直回到2001年，我在那时已经在PHP上工作这些游戏网站基本上18个月。没有人为我做代码付钱。我通过从哥本哈根，丹麦发送到芝加哥，伊利诺伊州的电子邮件与杰森·弗里德联系，发送给一个不知道我是谁的人。

我只是提供主动建议。杰森在互联网上问了一个问题，我发送了答案，他在PHP中问。我发送了那个问题的答案。我们开始交谈，然后我们开始工作。顺便说一下，这是互联网可以允许的奇迹。

哥本哈根的一个孩子从未见过芝加哥的这个人如何能够通过电子邮件连接并开始一起工作？顺便说一下，我们现在仍然在24年后一起工作。这太不可思议了。但我们开始一起工作，我们开始在一些客户项目上一起工作。杰森会做设计，37signals会做设计，我会带来编程PHP。

在我们一起在PHP中工作了我认为两个或三个客户项目后，我们一直遇到同样的问题。每当你与客户合作时，你从电子邮件开始那个项目。哦，是的，让我们一起工作。这是我们正在建设的。你开始交易越来越多的电子邮件。在几周过去之前，你必须向项目添加某人。

他们没有电子邮件。他们没有上下文。你发送它，最新文件在哪里？哦，我已经在FTP上上传了。它就像finalfinal_v06_2.0，对吧？那是要得到的。这只是一团糟。在某些方面是美丽的混乱，在某种程度上仍然运行今天绝大多数项目的混乱。电子邮件是最低公分母。这很棒。

但我们已经以严重的方式与客户失球几次，我们想我们可以做得更好。我们知道如何制作Web应用程序。我们不能只是制作一个比电子邮件更好的管理项目的系统吗？这不能那么难。我们一直在做博客。我们一直在做待办事项列表。

让我们把其中一些放在一起，只是制作一个系统，其中参与项目的任何人需要的一切都在一个页面上。它必须足够简单，我不会举办研讨会教你如何使用系统。我只是给你登录代码。你要跳进去。所以那是Basecamp。当我们开始在Basecamp上工作时，我第一次在与杰森的经历中有技术选择的自由。没有客户告诉我，"是的，PHP，听起来不错。

我们知道PHP。你能在PHP中构建它吗？"我有自由统治。在那个时候，我一直在阅读IEEE杂志和2000年代初的其他几本杂志，戴夫·托马斯和马丁·福勒一直在写关于编程模式和如何编写更好代码的文章。这两个人，特别是，都在使用Ruby来解释他们的概念，因为Ruby看起来像伪代码。

无论你是在C或Java或PHP中编程，所有三个选区都能理解Ruby，因为它基本上只是读链接英语。所以这些人使用Ruby来描述概念。首先，我会阅读这些文章只是为了他们解释的概念。我会想，我喜欢你解释的概念，但我也想看看编程语言。

我为什么没有听说过这个？所以我开始研究Ruby，我意识到在那个时候，Ruby可能不被任何人知道，但它实际上已经存在很长时间了。Matz，Ruby的日本创造者，早在93年就开始在Ruby上工作。在互联网甚至是一个东西之前。这里我在2003年，10年后，拿起似乎是这个隐藏的宝石，只是躺在默默无闻中，在众目睽睽之下。

但戴夫·托马斯和马丁·福勒，我认为成功地让我和其他一些人走上了一种编程语言的轨道，这种语言在西方没有被大量使用，但可能是。所以我拿起Ruby，我想，这非常不同。首先，所有分号在哪里？我一直在PHP、ASP中编程，我甚至做了一些Pascal，我看了一些C。到处都有分号。

这是第一个打击我的事情，该死的分号在哪里？我开始想，实际上，为什么我们在编程中有分号？它们是告诉解释器有新的指令行，但我作为人类不需要它们。怎么样？哦，有人在这里照顾人类，而不是机器。所以这真的让我感兴趣。

然后我对自己想，你知道吗？我很了解PHP。我不是一个了不起的程序员。我没有在编程中工作那么长时间，但也许我能弄清楚。我要给自己两周时间。我要写一个概念证明，我与数据库交谈，我拉一些记录，我格式化它们一点，我在HTML页面上显示它们。

我能在几周内弄清楚吗？大约花了一个周末，我完全着迷了。我完全被震撼了，因为Ruby是为我的大脑制作的，就像一个完美的定制手套，由我从未见过的人制作。这怎么可能？

**Lex：** 我们应该说也许像画Ruby具有的某些品质的图片，也许甚至与PHP相比。我们也应该说有一个荒谬的事情，我习惯于我忘记的，PHP到处都有美元符号。

**DHH：** 是的，是的，有线噪声。这就是我喜欢称呼的。

**Lex：** 有线噪声，这是一个如此美丽的短语。是的，所以有所有这些看起来像程序的东西。用Ruby，我的意思是Python有一些相似之处。它只是看起来像自然语言。

你可以正常阅读它。

**DHH：** 这里有一个做五次迭代的野生循环。你可以字面上输入数字五，点，现在我在数字五下调用方法。顺便说一下，这是Ruby的美丽方面之一，像整数这样的原语也是对象。你可以调用5.times，开始括号。

现在你在那个括号中的代码上迭代五次，就是这样。

**Lex：** 好吧，这很好。

**DHH：** 这不仅仅是好的，这是例外的。字面上没有其他我知道的编程语言能够将线噪声煮沸，几乎每个其他编程语言会注入到五次迭代在代码块上到那种程度。

**Lex：** 哇，这真的很好，好吧，谢谢你给那个例子。

那是一个美丽的例子。哇，我不认为我知道什么编程语言做那个。这真的很好。

**DHH：** Ruby充满了那个。所以让我深入几个例子。因为我真的认为它有助于画图片。让我通过说我实际上，我喜欢Python的精神来为此做前缀。

我认为Ruby和Python社区分享很多相似之处。它们都是动态解释语言。它们都专注于即时性和生产力以及在很多方面的易用性。但然后它们在许多其他方面也非常不同。它们非常不同的一种方式是美学上。Python，对我来说，我希望我不会太冒犯人们。我以前说过这个，它很丑。

它在其空间中很丑，因为它充满了多余的指令，这些指令对于Guido在87年制作Python时的遗留原因是必要的，这些原因仍然在2025年这里。我的大脑无法应对那个。让我给你一个基本例子。当你在Python中制作类时，初始化方法，起始方法是def。

好吧，公平。这实际上与Ruby相同，D-E-F，方法的定义。然后它是下划线，不是一个，下划线，两个，init，下划线，下划线，括号开始，self，逗号，然后第一个参数。

**Lex：** 是的，整个self事情。

**DHH：** 我看着那个说，对不起，我出去了。我做不到。

关于它的一切都冒犯了我的核心敏感性。这里你有所有新对象或类必须实现的最重要方法。它是我在任何地方见过的最美学上冒犯的输入初始化方式之一。你们对此没问题吗？

**Lex：** 嘿，你让我，你知道，你就像在谈论我的婚姻或类似的东西，我没有意识到我一直在一个有毒的关系中。然而，我只是习惯了它。

**DHH：** 对我来说，顺便说一下，那是Ruby的魔力。它打开了我的眼睛，帮助美丽的程序可能是。我不知道我一直在ASP中工作。我一直在PHP中工作。我甚至没有美学美丽代码是我们可以优化的东西的概念。我们可以追求的东西。甚至超过那个，我们可以追求它超过其他目标。Ruby之所以如此美丽，这不是意外，也不容易。Ruby本身是在C中实现的。解析Ruby代码非常困难，因为Ruby是为人类编写的，人类是混乱的生物。他们喜欢以正确的方式的东西。我无法完全解释为什么__init__让我反感，但它确实如此。当我看Ruby替代品时，它真的很有指导意义。

所以它是def，同样的部分，D-E-F，空格，初始化，括号。甚至不是括号。如果你不需要在参数内调用它，甚至没有括号。这本身实际上也是一个主要部分。如果人类不需要额外的字符，我们不会只是把它们放进去，因为解析计算机会更好。

我们要摆脱分号。我们要摆脱括号。我们要摆脱下划线。我们要摆脱所有那些丑陋，所有线噪声，并将其煮沸到其纯粹的本质。同时，我们不会缩写。

这是Ruby和Python之间美学的关键差异。init，短类型，只有五个字符。初始化要长得多，但它看起来好得多，你不经常输入它。所以你应该看一些漂亮的东西。如果你不必一直这样做，它很长是可以的。那些美学评估在Ruby语言中到处都是。

但让我给你一个更好的例子。if条件。这是所有编程语言的基石。他们有if条件。如果你采用大多数编程语言，它们都有if。这在几乎每种语言中基本上是相同的。空格，开始括号，我们都这样做。

然后你有也许，让我们说你调用一个叫做user.isadmin的对象，关闭括号，关闭括号，开始括号，这是我们要做的，如果用户是管理员，对吧？那将是一个正常的编程语言。Ruby不这样做。Ruby几乎煮沸了所有这些。我们从if开始。好吧，那是一样的。

没有括号必要，因为人类没有歧义来区分下一部分只是一个单一语句。所以你做if，空格，用户，点，管理员，问号。没有开括号，没有括号，什么都没有。下一个，开行。这是你的条件。那个问号对计算机没有意义，但它对人类意味着什么。

Ruby纯粹作为人类之间的沟通工具放入谓词方法样式。解释器实际上更多的工作能够看到这个问号在这里。为什么这个问号在这里？因为它读得如此好。如果user.admin？