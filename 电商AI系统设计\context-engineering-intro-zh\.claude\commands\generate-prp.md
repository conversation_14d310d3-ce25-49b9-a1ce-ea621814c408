# 創建 PRP

## 功能文件：$ARGUMENTS

為一般功能實作產生完整的 PRP，並進行深入研究。確保將 context 傳遞給 AI agent，以啟用自我驗證和迭代優化。首先閱讀功能文件以了解需要創建什麼、提供的範例如何幫助，以及任何其他考量。

AI agent 只會獲得您附加到 PRP 的 context 和訓練資料。假設 AI agent 可以存取程式碼庫並具有與您相同的知識截止日期，因此將您的研究結果包含或引用在 PRP 中很重要。Agent 具有 Websearch 功能，因此請傳遞文件和範例的 URL。

## 研究流程

1. **程式碼庫分析**
   - 在程式碼庫中搜尋相似的功能/模式
   - 識別要在 PRP 中引用的檔案
   - 注意要遵循的現有慣例
   - 檢查測試模式以了解驗證方法

2. **外部研究**
   - 在線上搜尋相似的功能/模式
   - 程式庫文件（包含具體的 URL）
   - 實作範例（GitHub/StackOverflow/部落格）
   - 最佳實務和常見陷阱

3. **使用者釐清**（如果需要）
   - 要模仿的具體模式以及在哪裡找到它們？
   - 整合需求以及在哪裡找到它們？

## PRP 生成

使用 PRPs/templates/prp_base.md 作為範本：

### 要包含並傳遞給 AI agent 作為 PRP 一部分的關鍵 Context
- **文件**：具有特定章節的 URL
- **程式碼範例**：來自程式碼庫的真實程式碼片段
- **陷阱**：程式庫怪癖、版本問題
- **模式**：要遵循的現有方法

### 實作藍圖
- 從顯示方法的偽代碼開始
- 引用真實檔案的模式
- 包含錯誤處理策略
- 列出完成 PRP 所需完成的任務，按應完成的順序排列

### 驗證關卡（必須可執行）例如對於 python
```bash
# 語法/風格
ruff check --fix && mypy .

# 單元測試
uv run pytest tests/ -v

```

*** 在您完成研究和探索程式碼庫之後，開始撰寫 PRP 之前的關鍵步驟 ***

*** 對 PRP 進行 ULTRATHINK 並規劃您的方法，然後開始撰寫 PRP ***

## 輸出
儲存為：`PRPs/{feature-name}.md`

## 品質檢查清單
- [ ] 包含所有必要的 context
- [ ] 驗證關卡可由 AI 執行
- [ ] 引用現有模式
- [ ] 清晰的實作路徑
- [ ] 已記錄錯誤處理

以 1-10 的等級為 PRP 評分（使用 claude codes 在一次性實作中成功的信心水平）

記住：目標是透過全面的 context 達成一次性實作成功。