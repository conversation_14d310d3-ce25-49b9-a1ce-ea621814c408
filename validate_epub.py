#!/usr/bin/env python3
"""
验证EPUB文件的脚本
"""

import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

def validate_epub(epub_path):
    """验证EPUB文件"""
    print(f"验证EPUB文件: {epub_path}")
    
    try:
        with zipfile.ZipFile(epub_path, 'r') as epub:
            # 检查必需文件
            required_files = [
                'mimetype',
                'META-INF/container.xml',
                'OEBPS/content.opf',
                'OEBPS/toc.ncx'
            ]
            
            missing_files = []
            for file in required_files:
                if file not in epub.namelist():
                    missing_files.append(file)
            
            if missing_files:
                print(f"❌ 缺少必需文件: {missing_files}")
                return False
            
            # 检查mimetype
            mimetype = epub.read('mimetype').decode('utf-8').strip()
            if mimetype != 'application/epub+zip':
                print(f"❌ mimetype错误: {mimetype}")
                return False
            
            # 检查章节文件
            chapter_files = [f for f in epub.namelist() if f.startswith('OEBPS/Text/chapter') and f.endswith('.xhtml')]
            if len(chapter_files) != 12:
                print(f"❌ 章节文件数量错误: {len(chapter_files)}")
                return False
            
            # 检查XML格式
            try:
                container_xml = epub.read('META-INF/container.xml')
                ET.fromstring(container_xml)
                
                content_opf = epub.read('OEBPS/content.opf')
                ET.fromstring(content_opf)
                
                toc_ncx = epub.read('OEBPS/toc.ncx')
                ET.fromstring(toc_ncx)
                
            except ET.ParseError as e:
                print(f"❌ XML格式错误: {e}")
                return False
            
            print("✅ EPUB文件验证通过!")
            print(f"📊 文件统计:")
            print(f"   - 总文件数: {len(epub.namelist())}")
            print(f"   - 章节数: {len(chapter_files)}")
            print(f"   - 文件大小: {Path(epub_path).stat().st_size / 1024:.1f} KB")
            
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    epub_file = "DHH谈编程未来、AI、Ruby on Rails、生产力与育儿.epub"
    validate_epub(epub_file)
