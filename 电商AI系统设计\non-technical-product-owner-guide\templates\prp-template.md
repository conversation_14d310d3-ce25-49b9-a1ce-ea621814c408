# PRP (Product Requirements Prompt) 模板

> 此模板帮助非技术产品需求方创建结构化的产品需求文档，确保AI能够准确理解和实现您的需求

## 📋 使用说明

1. **复制此模板**到您的项目`.augment/prps/active/`目录中
2. **重命名文件**为您的具体项目名称，如`ecommerce-copywriter.md`
3. **逐项填写**每个部分的内容，删除示例和说明文字
4. **保持结构完整**，不要删除章节标题
5. **使用具体数据**，避免模糊表述

---

# PRP: [项目名称]

## 📋 基本信息
- **项目名称**: [项目的完整名称]
- **项目类型**: [如：Web应用、移动应用、API服务等]
- **目标用户**: [主要用户群体]
- **优先级**: [高/中/低]
- **预估工作量**: [预期的开发时间，如：2周、1个月等]
- **项目负责人**: [产品负责人姓名]
- **创建日期**: [YYYY-MM-DD]
- **目标完成日期**: [YYYY-MM-DD]

---

## 🎯 项目背景和目标

### 业务背景
#### 当前痛点
```markdown
详细描述现状存在的问题：
- 问题1：[具体描述问题现象和影响]
- 问题2：[具体描述问题现象和影响]
- 问题3：[具体描述问题现象和影响]

数据支撑：
- 当前效率：[具体数据，如：每个任务需要X小时]
- 错误率：[具体数据，如：Y%的错误率]
- 用户满意度：[具体数据，如：满意度Z分]
```

#### 市场机会
```markdown
说明市场需求和商业机会：
- 市场规模：[目标市场的规模数据]
- 增长趋势：[市场增长的趋势分析]
- 用户需求：[用户对解决方案的需求程度]
- 竞争优势：[我们的独特优势]
```

#### 竞争态势
```markdown
分析竞品情况：
- 主要竞品：[列出2-3个主要竞争对手]
- 竞品优势：[竞品的主要优势]
- 竞品劣势：[竞品的不足之处]
- 差异化机会：[我们可以差异化的方向]
```

### 项目目标
#### 业务目标（量化指标）
- **效率提升**: [具体数值，如：提升80%的工作效率]
- **成本节约**: [具体数值，如：节约60%的人力成本]
- **收入增长**: [具体数值，如：增加15%的收入]
- **用户增长**: [具体数值，如：获得1000个新用户]

#### 用户目标（体验改善）
- **易用性**: [具体描述，如：3步完成核心任务]
- **效率**: [具体描述，如：任务完成时间减少X分钟]
- **满意度**: [具体目标，如：用户满意度达到4.5/5.0]
- **学习成本**: [具体描述，如：新用户5分钟内上手]

#### 技术目标（系统性能）
- **响应时间**: [具体要求，如：API响应时间<3秒]
- **并发能力**: [具体要求，如：支持100并发用户]
- **可用性**: [具体要求，如：系统可用性≥99.5%]
- **扩展性**: [具体要求，如：支持10倍用户增长]

---

## 👥 用户需求分析

### 目标用户画像
#### 主要用户群体1：[用户角色名称]
```markdown
基本信息：
- 年龄范围：[如：25-35岁]
- 教育背景：[如：大专及以上]
- 工作经验：[如：1-5年相关经验]
- 技术水平：[如：熟悉基本操作，编程能力有限]

工作特征：
- 日常任务：[具体的工作内容]
- 工作压力：[面临的主要压力]
- 效率需求：[对效率的具体要求]
- 质量要求：[对质量的具体标准]

使用习惯：
- 设备偏好：[如：主要使用PC，偶尔使用手机]
- 界面偏好：[如：简洁易用，功能明确]
- 操作偏好：[如：快捷键、批量操作]
- 学习偏好：[如：视频教程、文档说明]
```

#### 次要用户群体2：[用户角色名称]
```markdown
[按照同样的结构描述次要用户群体]
```

### 使用场景分析
#### 高频场景（每日使用）
```markdown
场景1：[场景名称]
- 触发条件：[什么情况下会使用]
- 使用频率：[每天X次]
- 操作流程：[详细的操作步骤]
- 期望结果：[用户期望达到的效果]
- 成功标准：[如何判断任务成功完成]

场景2：[场景名称]
[按照同样的结构描述]
```

#### 中频场景（每周使用）
```markdown
[按照高频场景的结构描述中频场景]
```

#### 低频场景（每月使用）
```markdown
[按照高频场景的结构描述低频场景]
```

### 用户旅程设计
#### 现状流程（当前用户如何完成任务）
```markdown
步骤1：[当前的第一步] → 耗时：[X分钟] → 痛点：[存在的问题]
步骤2：[当前的第二步] → 耗时：[X分钟] → 痛点：[存在的问题]
步骤3：[当前的第三步] → 耗时：[X分钟] → 痛点：[存在的问题]
...
总耗时：[总计时间] → 总体问题：[主要痛点总结]
```

#### 期望流程（理想的用户操作流程）
```markdown
步骤1：[优化后的第一步] → 耗时：[X分钟] → 改进：[解决的问题]
步骤2：[优化后的第二步] → 耗时：[X分钟] → 改进：[解决的问题]
步骤3：[优化后的第三步] → 耗时：[X分钟] → 改进：[解决的问题]
...
总耗时：[总计时间] → 总体改进：[整体提升效果]
```

---

## 🔧 功能需求定义

### 核心功能（P0 - 必须有）
#### 功能1：[功能名称]
```markdown
功能描述：
[详细描述这个功能是什么，解决什么问题]

用户需求：
- 用户输入：[用户需要提供什么信息]
- 用户期望：[用户期望得到什么结果]
- 使用场景：[在什么情况下使用这个功能]

功能要求：
- 基本要求：[功能的基本实现要求]
- 性能要求：[响应时间、处理能力等]
- 质量要求：[准确性、可靠性等]
- 兼容要求：[平台、设备兼容性]

用户体验要求：
- 操作简单：[操作步骤不超过X步]
- 响应快速：[X秒内完成]
- 结果清晰：[结果展示的要求]
- 错误处理：[出错时的处理方式]

验收标准：
- [ ] [具体的验收条件1]
- [ ] [具体的验收条件2]
- [ ] [具体的验收条件3]
```

#### 功能2：[功能名称]
```markdown
[按照功能1的结构描述]
```

### 重要功能（P1 - 重要）
#### 功能3：[功能名称]
```markdown
[按照核心功能的结构描述，但可以适当简化]
```

### 可选功能（P2 - 可选）
#### 功能4：[功能名称]
```markdown
[按照核心功能的结构描述，但可以适当简化]
```

### 非功能需求
#### 性能要求
- **响应时间**: [具体要求，如：页面加载<2秒，API响应<3秒]
- **并发量**: [具体要求，如：支持100并发用户]
- **吞吐量**: [具体要求，如：每小时处理1000个请求]
- **资源使用**: [具体要求，如：内存使用<500MB]

#### 可用性要求
- **系统稳定性**: [具体要求，如：可用性≥99.5%]
- **故障恢复**: [具体要求，如：故障恢复时间<5分钟]
- **数据备份**: [具体要求，如：每日自动备份]
- **容错能力**: [具体要求，如：单点故障不影响整体服务]

#### 安全要求
- **数据安全**: [具体要求，如：敏感数据加密存储]
- **访问控制**: [具体要求，如：基于角色的权限管理]
- **隐私保护**: [具体要求，如：符合GDPR要求]
- **审计日志**: [具体要求，如：完整的操作日志记录]

---

## 📏 业务规则和约束

### 业务规则
#### 平台规则
```markdown
平台1（如：淘宝）：
- 内容长度：[具体限制]
- 格式要求：[具体格式]
- 禁用内容：[禁用词汇列表]
- 审核标准：[审核要求]

平台2（如：天猫）：
[按照同样结构描述]
```

#### 合规要求
```markdown
法律法规：
- [相关法律条款]
- [行业监管要求]
- [数据保护法规]

行业标准：
- [行业协会标准]
- [质量认证要求]
- [安全标准要求]
```

#### 质量标准
```markdown
内容质量：
- 准确性：[准确性要求和验证方法]
- 相关性：[相关性标准]
- 可读性：[可读性要求]

技术质量：
- 代码质量：[代码规范和质量要求]
- 测试覆盖：[测试覆盖率要求]
- 文档完整：[文档要求]
```

### 技术约束
#### 集成要求
```markdown
必须集成的系统：
- 系统1：[系统名称] - [集成目的] - [接口要求]
- 系统2：[系统名称] - [集成目的] - [接口要求]

可选集成的系统：
- 系统3：[系统名称] - [集成目的] - [接口要求]
```

#### 数据要求
```markdown
数据来源：
- 数据源1：[数据来源] - [数据格式] - [更新频率]
- 数据源2：[数据来源] - [数据格式] - [更新频率]

数据处理：
- 数据清洗：[数据清洗要求]
- 数据验证：[数据验证规则]
- 数据存储：[数据存储要求]
```

#### 技术栈约束
```markdown
必须使用的技术：
- 前端：[指定的前端技术栈]
- 后端：[指定的后端技术栈]
- 数据库：[指定的数据库]
- 部署：[指定的部署方式]

技术限制：
- 不能使用：[禁用的技术或工具]
- 版本要求：[特定的版本要求]
- 兼容性：[兼容性要求]
```

---

## ✅ 成功标准和验收条件

### 量化指标
#### 效率指标
```markdown
基准数据：
- 当前效率：[现状的具体数据]
- 当前成本：[现状的成本数据]
- 当前质量：[现状的质量数据]

目标数据：
- 效率提升：[具体的提升目标，如：提升80%]
- 成本节约：[具体的节约目标，如：节约60%]
- 质量改善：[具体的质量目标，如：错误率<5%]

测量方法：
- 效率测量：[如何测量效率提升]
- 成本测量：[如何测量成本节约]
- 质量测量：[如何测量质量改善]
```

#### 质量指标
```markdown
用户满意度：
- 目标：[具体目标，如：≥4.5/5.0]
- 测量方法：[如：用户调研问卷]
- 测量频率：[如：每月一次]

系统质量：
- 可用性：[如：≥99.5%]
- 性能：[如：响应时间<3秒]
- 准确性：[如：准确率≥95%]
```

#### 业务指标
```markdown
用户采用率：
- 目标：[如：70%的目标用户采用]
- 测量方法：[如何统计采用率]
- 时间周期：[如：上线后3个月内]

业务价值：
- 收入影响：[对收入的具体影响]
- 成本影响：[对成本的具体影响]
- 效率影响：[对效率的具体影响]
```

### 验收条件
#### 功能验收
```markdown
核心功能验收：
- [ ] [功能1的具体验收标准]
- [ ] [功能2的具体验收标准]
- [ ] [功能3的具体验收标准]

性能验收：
- [ ] [性能指标1的验收标准]
- [ ] [性能指标2的验收标准]
- [ ] [性能指标3的验收标准]

质量验收：
- [ ] [质量指标1的验收标准]
- [ ] [质量指标2的验收标准]
- [ ] [质量指标3的验收标准]
```

#### 用户验收
```markdown
用户测试计划：
- 测试用户：[测试用户的选择标准]
- 测试场景：[具体的测试场景]
- 测试时间：[测试的时间安排]
- 成功标准：[用户测试的成功标准]

验收标准：
- [ ] [用户能够独立完成核心任务]
- [ ] [用户满意度达到目标]
- [ ] [用户愿意推荐给他人]
- [ ] [用户认为系统有价值]
```

---

## 🚨 风险评估和缓解策略

### 技术风险
```markdown
风险1：[技术风险描述]
- 影响程度：[高/中/低]
- 发生概率：[高/中/低]
- 缓解措施：[具体的缓解方案]
- 应急预案：[风险发生时的应急处理]

风险2：[技术风险描述]
[按照同样结构描述]
```

### 业务风险
```markdown
风险1：[业务风险描述]
- 影响程度：[高/中/低]
- 发生概率：[高/中/低]
- 缓解措施：[具体的缓解方案]
- 应急预案：[风险发生时的应急处理]
```

### 用户接受度风险
```markdown
风险评估：
- 用户习惯改变：[用户是否愿意改变现有习惯]
- 学习成本：[用户学习新系统的成本]
- 价值认知：[用户是否认可系统价值]

缓解策略：
- 用户培训：[用户培训计划]
- 渐进式推广：[分阶段推广策略]
- 反馈收集：[用户反馈收集机制]
```

---

## 📖 参考资料和依赖

### 业务参考资料
- **行业报告**: [相关行业报告的链接或文档]
- **竞品分析**: [竞品分析报告]
- **用户调研**: [用户调研报告]
- **市场数据**: [市场数据来源]

### 技术参考资料
- **API文档**: [相关API的文档链接]
- **技术标准**: [需要遵循的技术标准]
- **最佳实践**: [行业最佳实践文档]
- **开源项目**: [可以参考的开源项目]

### 法规和标准
- **法律法规**: [相关法律法规文档]
- **行业标准**: [行业标准文档]
- **安全标准**: [安全相关标准]
- **隐私政策**: [隐私保护相关政策]

---

## 📅 项目里程碑和交付物

### 主要里程碑
#### 里程碑1：[里程碑名称] - [目标日期]
```markdown
交付物：
- [ ] [具体交付物1]
- [ ] [具体交付物2]
- [ ] [具体交付物3]

验收标准：
- [ ] [验收标准1]
- [ ] [验收标准2]
```

#### 里程碑2：[里程碑名称] - [目标日期]
```markdown
[按照里程碑1的结构描述]
```

### 最终交付物清单
- [ ] **功能交付物**: [完整的系统功能]
- [ ] **文档交付物**: [用户手册、技术文档等]
- [ ] **培训交付物**: [用户培训材料]
- [ ] **运维交付物**: [部署和运维指南]

---

## 📝 附录

### 术语定义
- **术语1**: [定义和说明]
- **术语2**: [定义和说明]
- **术语3**: [定义和说明]

### 联系信息
- **产品负责人**: [姓名] - [联系方式]
- **业务专家**: [姓名] - [联系方式]
- **技术负责人**: [姓名] - [联系方式]

### 变更记录
| 日期 | 版本 | 变更内容 | 变更人 |
|------|------|----------|--------|
| [日期] | v1.0 | 初始版本 | [姓名] |
| [日期] | v1.1 | [变更内容] | [姓名] |

---

## 📋 PRP完成检查清单

在提交PRP之前，请确认以下项目已完成：

### 内容完整性检查
- [ ] 项目背景和目标已详细描述
- [ ] 用户需求分析已完成
- [ ] 功能需求已明确定义
- [ ] 业务规则和约束已说明
- [ ] 成功标准和验收条件已设定
- [ ] 风险评估已完成
- [ ] 参考资料已提供

### 质量检查
- [ ] 所有描述都具体明确，避免模糊表述
- [ ] 所有指标都可量化和验证
- [ ] 所有需求都有明确的验收标准
- [ ] 所有风险都有相应的缓解措施

### 可执行性检查
- [ ] AI能够理解所有业务规则
- [ ] 技术约束和要求明确
- [ ] 验收标准可操作
- [ ] 时间安排合理可行

---

*完成此PRP模板后，您将拥有一个完整、清晰、可执行的产品需求文档，确保AI能够准确理解并实现您的需求。*
