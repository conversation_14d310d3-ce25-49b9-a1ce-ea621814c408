<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第一章：编程学习之路与早期经历</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第01章</div>
        <h1 class="chapter-title">第一章：编程学习之路与早期经历</h1>
    </div>
    
    <div class="chapter-intro">
        <p>DHH的编程之路并非一帆风顺。从童年时期对Commodore 64的渴望，到多次学习编程的失败尝试，再到最终在互联网时代找到自己的方向，这段经历展现了一个传奇程序员的成长轨迹。本章将带您了解DHH如何从一个对编程一窍不通的孩子，逐步成长为Ruby on Rails的创造者。</p>
    </div>

    <div class="conversation">
        <p><strong>Lex：</strong> 对于一个成为传奇程序员的人来说，你正式开始编程的时间相对较晚，我想这是因为你曾经几次尝试学习编程但都失败了。你能告诉我你学习编程失败的完整故事吗？Commodore 64有参与其中吗？</p>

        <p><strong>DHH：</strong> Commodore 64是我的灵感来源。我真的很想要一台Commodore 64。那是我第一次坐在电脑前。我坐在它前面的方式是，我五岁的时候，我们街上有一个孩子有一台Commodore 64。</p>

        <p>没有其他人有电脑，所以我们所有的孩子都会去那里，我们都在玩"Yie Ar Kung-Fu"。我不知道你是否见过那个游戏。它是最早的格斗游戏之一，真的是一个很棒的游戏。我五岁时第一次玩那个游戏。</p>

        <p>我们大约有七个孩子坐在这个孩子的卧室里，轮流玩游戏。我觉得这非常有趣。我求了又求，求我爸爸，我能得到一台电脑吗？他终于回家了，说："我给你买了电脑。"我想，太好了，我自己的Commodore 64。然后他拿出这个黑绿蓝色的键盘。</p>

        <p>那是一台Amstrad 464。我说，爸爸，这是什么？</p>

        <p><strong>Lex：</strong> 失望啊。</p>

        <p><strong>DHH：</strong> 这不是Commodore 64。但它是一台电脑。所以我在六岁时得到了我的第一台电脑，那台Amstrad 464。当然，我想做的第一件事就是玩电子游戏。</p>

        <p>我想这台电脑，顺便说一下，他用电视和立体声录音机之类的东西换来的，带有大约两个游戏。一个是这个"Frogger"游戏，你必须从地下逃脱。实际上有点黑暗。就像这只青蛙，你试图让它从地下出来。我玩得很糟糕。我只有那两个游戏。</p>

        <p>然后我想要更多游戏。当你是一个没有很多钱的孩子时，获得更多游戏的一种方法，我不能只是买一堆游戏，就是自己输入它们。在84年、85年，杂志会在杂志的后面印刷源代码，你可以坐下来输入它。所以我试图这样做，将这个游戏输入到Amstrad中需要大约两个小时。</p>

        <p>当然，我会在途中犯一些拼写错误，有些东西不会工作。我的英语不是很好。我出生在丹麦。所以我真的很努力地想进入其中，因为我想要所有这些游戏。我没有钱买它们。</p>

        <p>我努力了很长时间，但它从来没有点击。然后我发现了盗版的魔力。在那之后，我基本上暂停了学习编程，因为现在我突然可以访问各种游戏。所以那是第一次尝试，大约在六、七岁的时候。有趣的是，我记得这些片段。</p>

        <p>我记得不理解变量的目的。如果有一个东西，你给它分配了什么，为什么你要给它分配另一个东西？出于某种原因，我理解常量。常量对我来说是有意义的，但变量不是。然后也许我11到12岁，我已经进入了Amiga。</p>

        <p>顺便说一下，Amiga仍然可能是我有史以来最喜欢的电脑。我的意思是，这是那些你变老了就会说"哦，80年代的音乐太棒了"的事情之一。对我来说，即使作为一个热爱电脑、热爱新电脑的人，Amiga是这台由生产Commodore 64的同一家公司制造的神奇机器。我在87年得到了Amiga 500。</p>

        <p><strong>Lex：</strong> 看看这个性感的东西，那是一台性感的机器。</p>

        <p><strong>DHH：</strong> 顺便说一下，这来自一个计算机不像现在这样全球化的时代。不同的地区有不同的流行电脑。Amiga在欧洲真的很受欢迎，但据我了解，它在美国根本不受欢迎。它在日本也不受欢迎。</p>

        <p>只是有不同的机器。Apple II在美国是一个大事件。我在80年代的哥本哈根甚至从未听说过苹果。但Amiga 500是让我想再次尝试的机器。你知道什么有趣吗？我想再次尝试的原因是我记得第一次学习。</p>

        <p>然后有这种编程语言，字面上叫做EasyAMOS，就像AMOS的简易版本。我想，如果它是EasyAMOS，能有多难？我应该能够弄清楚这一点。这次我更努力地尝试了。我进入了条件语句，我进入了循环，我进入了所有这些东西，但我仍然做不到。</p>

        <p>在第二次尝试中，我真的到了这样的地步，也许我不够聪明，也许编程对我来说不合适，也许它太多数学了。我喜欢数学，但只是表面上的。我不像我的一些可能稍微更书呆子的朋友那样深入地喜欢它，我对他们有极大的尊重。我不是那个会弄清楚一切的数学极客。</p>

        <p>所以在用EasyAMOS尝试失败后，甚至没有完成一个非常基本的游戏，我想编程对我来说不合适。我必须做别的事情。我仍然热爱电脑。我仍然热爱电子游戏。实际上在那个时候，我已经开始与知道如何编程的人交朋友，他们甚至不是在编程EasyAMOS。他们在编程该死的汇编语言。</p>

        <p>我会坐下来说，你怎么做，移动和内存和复制，你甚至怎么做这个？我甚至不明白你如何从这个到Amiga演示，例如。那是Amiga的大事。它在欧洲有这个精彩的演示场景。</p>

        <p>这是Amiga历史上一个非常有趣的时期，你有所有这些程序员主要分布在欧洲各地，他们会在图形竞赛中竞争，你可能会带来其中一个不同的...</p>

        <p><strong>Lex：</strong> 在那个东西上？</p>

        <p><strong>DHH：</strong> 在这个东西上，他们会制作这些小的几乎像音乐视频的东西，结合一些midi音乐，结合一些酷炫的图形。他们会在4K中完成所有这些，四千字节。</p>

        <p>那不是4K分辨率，而是四千字节的内存。我只是觉得那是一个如此酷的场景。这显然是互联网之前的时代。它甚至在某种程度上是BBS（电子公告板系统）之前的时代。你通过邮寄磁盘与其他人交换你的演示软件，就像3.5英寸软盘。我被整个场景迷住了。</p>

        <p>我被他们能够创造的东西迷住了，我只是想成为其中的一部分，即使我没有任何技能可以贡献。这就是我如何进入运行BBS的。我那时没有学习编程，直到很久以后，直到我快20岁时才学会编程。</p>

        <p>电子公告板系统存在于这个有趣的空间中，它们部分是为演示场景提供服务，允许所有这些演示组织分发他们令人惊叹的演示。然后它也是一个交易盗版软件的地方。我在14岁时在哥本哈根我的小卧室里开始了其中一个。我有我的，在那时，Amiga 4000。我有三条电话线进入我的小房间。</p>

        <p><strong>Lex：</strong> 不错。</p>

        <p><strong>DHH：</strong> 这很有趣，因为我只有14岁。当我安装第三条线路时，你必须让电话公司的人来做。我得到了这个人，他只是四处看看，"这是什么？为什么一个14岁的孩子在他们的小卧室里有三条电话线？这里发生了什么？为什么所有这些调制解调器都在闪烁红色和黑色并发出有趣的声音？"</p>

        <p><strong>Lex：</strong> 你的父母知道吗？</p>

        <p><strong>DHH：</strong> 他们知道也不知道。他们知道我有电话线。他们知道我有电脑。我不认为他们真正理解我在交易盗版软件，这既是非法的，也是其他正在进行的事情。</p>

        <p><strong>Lex：</strong> 哦，我们应该说在欧洲，也许你可以评论一下，特别是在东欧，但总的来说在欧洲，我认为盗版比在美国更容易被接受。我不知道，也许这只是我的成长经历。</p>

        <p><strong>DHH：</strong> 甚至那种对话都不存在。我在丹麦长大时从未与任何人交谈过。</p>

        <p><strong>Lex：</strong> 盗版是错误的。</p>

        <p><strong>DHH：</strong> 对盗版有任何道德疑虑。完全接受你是一个孩子，你想要很多游戏。你没有很多钱，你怎么办？你交易。</p>

        <p><strong>Lex：</strong> 是的。</p>

        <p><strong>DHH：</strong> 有些人偶尔会买游戏。我的意思是，我曾经买了一个世嘉Master System，我买了一个游戏。因为那是我能负担得起的。我得到了"After Burner II"。我不知道你是否玩过那个游戏。在世嘉Master System上的实现相当糟糕，但它花费了大约600克朗。我当时通过送报纸赚钱。我必须做一个月才能负担得起一个游戏。</p>

        <p>我太喜欢电子游戏了，不能等一个月才得到一个游戏。所以盗版就是你做的方式。这就是我如何进入运行这个电子公告板系统，成为演示场景的一部分，在某种程度上成为盗版场景的一部分。然后在某个时候意识到，哦，你实际上也可以在这上面赚钱，这可以资助购买更多电话线和购买更多调制解调器和购买更多Amiga。</p>

        <p>哦，是的，那是演示聚会之一。这些是令人惊叹的事情。</p>

        <p><strong>Lex：</strong> 我在看什么？</p>

        <p><strong>DHH：</strong> 这不是很棒吗？</p>

        <p><strong>Lex：</strong> 看看所有那些CRT显示器。</p>

        <p><strong>DHH：</strong> 所有这些CRT显示器。再次，当我14岁时，我不完全理解为什么我的父母允许这样做。但我从丹麦首都哥本哈根乘火车到日德兰半岛的这个小镇奥胡斯，和一群大约十几岁末和二十多岁的家伙一起。</p>

        <p>我14岁。我拖着我的14英寸CRT显示器和我的电脑背包去参加聚会。那就是它被称为的。那是当时最大的演示场景聚会。正如你在那张照片中看到的，成千上万的人只是排队带着他们的电脑，整天编程演示，并来回交易这些东西。</p>

        <p><strong>Lex：</strong> 这有点棒。不会撒谎，这有点荒谬。</p>

        <p><strong>DHH：</strong> 这完全棒。我以某种方式怀念它，互联网在某些方面连接了人们，但你从坐在另一个人旁边得到的连接，他有自己的CRT显示器，他拖着它穿越半个国家到达那里，这真的很特别。因为这也只是创造力的爆发。</p>

        <p>你不断地四处奔跑。你不断地被真正擅长他们所做的事情的人包围。他们真的很擅长编程计算机。这是有感染性的。这是我当时感到的痛苦的一部分，哦，天哪，为什么我不能弄清楚这一点？我的意思是，为什么我甚至不能弄清楚EasyAMOS？这有点令人沮丧。</p>

        <p><strong>Lex：</strong> 但在你的第三次尝试中，你更成功了吗？</p>

        <p><strong>DHH：</strong> 所以第三次尝试是我开始理解的时候，这是我开始帮助构建互联网的东西的时候。所以大约在95年，我想是，或者96年，我发现了互联网。实际上，在九年级。那是我的第一次经历。我去了丹麦的某个大学。</p>

        <p>在九年级，我们有这次远足，他们让我们坐在电脑前，电脑有Netscape Navigator，第一个版本，或者也许它甚至是那个的前身。他们有一个文本编辑器。我们这些孩子就得到了，"嘿，在互联网上建造一些东西。"</p>

        <p>它只是HTML，你做的第一件事就是，哦，我可以通过放入这个标签并保存它来让文本闪烁。那一刻，那实际上是我重新唤醒想要学习编程的冲动的时候，因为我得到了一个积极的体验。我与编程的所有其他经历是我会花几个小时输入一些东西，我会点击运行，它不会工作。</p>

        <p>我会得到一个对我作为一个孩子没有意义的错误消息，无论是在六岁或七岁还是在12岁。在这里，我坐在连接到互联网的电脑前，我让文本闪烁。我让它变大。我把它变成h1或h2。这些家伙在这里，"我们只是做了大约一个半小时。"</p>

        <p>突然我想，哦，我可以为互联网制作德国的某个人能够访问和看到的东西，我不必向任何人请求许可。这太酷了。我必须做更多这样的事情。所以我进入了互联网。我开始使用HTML，我仍然有所有这些来自这些演示聚会的朋友。我开始与他们一起创建游戏网站。</p>

        <p>我宁愿买电子游戏，我会评论它们。这是获得新电子游戏的另一种好方法，就是走到某个商店说，嘿，我是记者。我是这个15岁的孩子。他们看着你，"你是记者？"是的，我可以借一些游戏吗？因为这是游戏转移到PlayStation和其他这些东西的时候。</p>

        <p>你不能像以前那样容易地盗版，至少一开始不能。所以我去那里，做了所有这些，这开始了我的互联网之旅。它开始在这些游戏网站上工作，与程序员一起工作，弄清楚我可以做一些事情，我可以在HTML部分工作。</p>

        <p>这不是真正的编程，但它有点像。你在与电脑交谈。你让它在屏幕上放文本，你与世界另一端的人交流。所以这成为了我回到编程的途径。然后慢慢地我学会了越来越多。我与某人做的第一个动态网站，来自演示场景的这些程序之一是ASP.NET。</p>

        <p>我甚至实际上没有被称为.NET。那是我们开始的。然后我们转向PHP，PHP是我终于理解的时候。当它终于点击的时候。当条件语句和循环和变量以及所有这些东西开始对我有足够的意义，我想我可以做到这一点。</p>
    </div>
</body>
</html>