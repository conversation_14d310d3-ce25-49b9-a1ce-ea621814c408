# 非技术产品需求方的 Augment AI 项目开发指南

> 基于AI电商文案生成系统案例，为非技术背景的产品需求方提供完整的项目准备和协作指南

## 目录
1. [领域知识准备](#1-领域知识准备)
2. [产品描述深度](#2-产品描述深度)
3. [Context Engineering配置](#3-context-engineering配置)
4. [PRP文档创建](#4-prp文档创建)
5. [与AI协作方式](#5-与ai协作方式)
6. [质量验收标准](#6-质量验收标准)

---

## 1. 领域知识准备

### 1.1 业务规则和平台标准

作为产品需求方，您需要提供详细的业务规则。以电商文案生成为例：

#### **平台规则文档模板**
```markdown
# 电商平台规则清单

## 淘宝平台规则
### 标题规范
- 最大字符数：30个字符
- 必须包含：品牌名、产品名、核心卖点
- 禁止使用：最、第一、唯一、极品、顶级等极限词
- 推荐结构：品牌+产品+特性+规格

### 图片要求
- 主图尺寸：800x800像素
- 格式要求：JPG、PNG
- 文字占比：不超过图片面积的30%

### 内容合规
- 不得使用医疗、功效性描述
- 避免夸大宣传和虚假承诺
- 遵循广告法相关规定

## 天猫平台规则
### 标题规范
- 最大字符数：60个字符
- 品牌授权要求更严格
- 支持更丰富的营销词汇

### 质量要求
- 图片质量要求更高
- 详情页内容更丰富
- 客服响应时间要求
```

#### **行业标准收集清单**
- **法律法规**：广告法、消费者权益保护法相关条款
- **行业协会标准**：电商协会发布的行业规范
- **竞品分析**：主要竞争对手的文案风格和策略
- **平台政策**：各电商平台的最新政策更新

### 1.2 用户需求和行为分析

#### **目标用户画像模板**
```markdown
# 用户画像分析

## 主要用户群体：电商运营人员
### 基本信息
- 年龄：25-35岁
- 教育背景：大专及以上
- 工作经验：1-5年电商运营经验
- 技术水平：熟悉电商平台操作，但编程能力有限

### 工作场景
- 日常任务：商品上架、文案优化、活动策划
- 工作压力：文案创作耗时、质量不稳定、缺乏创意
- 效率需求：希望快速生成高质量文案
- 质量要求：符合平台规则、提升转化率

### 使用习惯
- 偏好简单易用的界面
- 需要即时预览和编辑功能
- 希望有模板和参考案例
- 重视批量处理能力
```

#### **业务流程梳理**
```markdown
# 电商文案创作流程

## 当前流程（手动创作）
1. 收集产品信息 → 30分钟
2. 研究竞品文案 → 20分钟
3. 撰写初稿 → 60分钟
4. 内部审核修改 → 30分钟
5. 平台合规检查 → 15分钟
6. 最终发布 → 5分钟
**总计：160分钟/个商品**

## 期望流程（AI辅助）
1. 输入产品信息 → 5分钟
2. AI生成多版本文案 → 2分钟
3. 选择和微调 → 10分钟
4. 自动合规检查 → 1分钟
5. 批量发布 → 2分钟
**总计：20分钟/个商品**
```

### 1.3 业务数据和指标

#### **关键业务指标**
```markdown
# 业务指标定义

## 效率指标
- 文案创作时间：目标减少80%（从160分钟降至20分钟）
- 批量处理能力：支持100个商品/小时
- 错误率：平台违规率<5%

## 质量指标
- 用户满意度：≥4.5/5.0
- 文案采用率：≥70%
- 转化率提升：≥15%

## 成本指标
- 人力成本节约：减少60%的文案创作人员
- 培训成本：新员工上手时间减少50%
- 违规成本：避免因违规导致的店铺处罚
```

---

## 2. 产品描述深度

### 2.1 功能描述框架

作为非技术人员，您应该专注于**业务需求和用户体验**，而非技术实现细节。

#### **功能描述模板**
```markdown
# 产品功能需求描述

## 核心功能1：智能标题生成
### 用户需求
- 用户输入：产品基本信息（类目、品牌、名称、特性）
- 用户期望：获得5-10个不同风格的标题选项
- 使用场景：新品上架、标题优化、A/B测试

### 功能要求
- 支持淘宝、天猫等不同平台
- 自动包含SEO关键词
- 符合平台字符限制
- 提供质量评分和改进建议

### 用户体验要求
- 操作简单：3步完成（输入→生成→选择）
- 响应快速：3秒内生成结果
- 结果可编辑：支持在线修改和保存
- 批量处理：支持Excel导入批量生成

### 业务规则
- 必须通过平台合规检查
- 自动过滤禁用词汇
- 保持品牌调性一致
- 支持不同营销风格（专业、时尚、促销等）
```

### 2.2 用户体验描述

#### **用户旅程地图**
```markdown
# 用户使用流程设计

## 场景1：单个商品标题生成
### 步骤1：信息输入
- 界面：简洁的表单界面
- 必填项：商品类目、品牌、产品名称
- 选填项：核心特性、目标受众、价格区间
- 辅助功能：历史记录、模板选择

### 步骤2：参数设置
- 平台选择：淘宝/天猫单选
- 风格选择：专业/时尚/促销/奢华
- 关键词：自动推荐+手动添加
- 高级选项：字符限制、特殊要求

### 步骤3：结果展示
- 生成5-10个标题选项
- 每个标题显示：内容、质量评分、SEO评分
- 实时预览：在不同平台的展示效果
- 操作按钮：编辑、收藏、复制、导出

### 步骤4：结果处理
- 在线编辑：支持实时修改
- 质量检查：自动合规验证
- 保存管理：历史记录、分类管理
- 批量操作：一键应用到多个商品
```

### 2.3 业务场景覆盖

#### **完整业务场景清单**
```markdown
# 业务场景需求

## 日常运营场景
1. **新品上架**：快速生成符合规范的商品文案
2. **季节性调整**：根据季节特点调整文案风格
3. **促销活动**：生成活动相关的营销文案
4. **竞品应对**：分析竞品文案并生成差异化内容

## 特殊需求场景
1. **品牌升级**：统一调整品牌相关文案
2. **平台迁移**：适配不同平台的文案规则
3. **国际化**：支持多语言文案生成
4. **合规整改**：批量检查和修正违规文案

## 团队协作场景
1. **权限管理**：不同角色的功能权限
2. **审核流程**：文案审核和批准机制
3. **模板共享**：团队内部模板和经验分享
4. **数据分析**：文案效果跟踪和优化建议
```

---

## 3. Context Engineering配置

### 3.1 项目上下文组织结构

作为非技术人员，您需要在`.augment/context/`目录中组织业务知识：

#### **目录结构建议**
```
.augment/context/
├── business-rules.md          # 业务规则和平台标准
├── user-requirements.md       # 用户需求和场景分析
├── domain-knowledge.md        # 行业知识和最佳实践
├── competitive-analysis.md    # 竞品分析和市场调研
├── brand-guidelines.md        # 品牌规范和风格指南
├── compliance-standards.md    # 合规要求和法律法规
└── success-metrics.md         # 成功指标和验收标准
```

### 3.2 业务规则文档编写

#### **business-rules.md 模板**
```markdown
# 电商文案生成业务规则

## 平台规则总览
### 淘宝规则
- 标题长度：≤30字符
- 禁用词汇：[详细列表]
- 图片要求：[具体规范]
- 内容限制：[合规要求]

### 天猫规则
- 标题长度：≤60字符
- 品牌要求：[授权要求]
- 质量标准：[更高要求]

## 文案创作原则
### FABE销售法则
- Feature（特性）：产品功能特点
- Advantage（优势）：相比竞品的优势
- Benefit（利益）：给用户带来的好处
- Evidence（证据）：支撑证据和案例

### 情感化表达
- 目标情感：信任、渴望、紧迫感
- 表达方式：具体场景、用户证言
- 避免方式：夸大宣传、虚假承诺

## 质量标准
### 内容质量
- 准确性：信息真实可靠
- 相关性：与产品高度相关
- 吸引力：能够引起用户兴趣
- 可读性：语言流畅易懂

### 技术质量
- 合规性：通过平台审核
- SEO友好：包含关键词
- 格式规范：符合平台要求
- 响应速度：快速生成结果
```

### 3.3 用户需求文档

#### **user-requirements.md 模板**
```markdown
# 用户需求分析

## 核心用户群体
### 电商运营专员
- 工作职责：商品上架、文案优化、数据分析
- 技能水平：熟悉平台操作，文案能力一般
- 痛点问题：创作耗时、质量不稳定、缺乏灵感
- 期望解决：快速生成、质量保证、批量处理

### 内容运营人员
- 工作职责：内容策划、文案创作、品牌传播
- 技能水平：文案能力强，但对平台规则不够熟悉
- 痛点问题：平台规则复杂、合规风险高
- 期望解决：自动合规检查、平台适配

## 使用场景分析
### 高频场景（每日使用）
1. 新品标题生成：每天10-50个商品
2. 文案优化：根据数据反馈调整
3. 合规检查：避免违规风险

### 中频场景（每周使用）
1. 活动文案：促销活动相关内容
2. 季节调整：根据季节特点更新
3. 竞品分析：参考竞品策略

### 低频场景（每月使用）
1. 品牌升级：统一调整品牌文案
2. 平台拓展：适配新的销售平台
3. 数据分析：文案效果评估

## 功能优先级
### P0（必须有）
- 标题生成：核心功能
- 合规检查：避免风险
- 批量处理：提升效率

### P1（重要）
- 质量评分：帮助选择
- 实时预览：提升体验
- 历史记录：便于管理

### P2（可选）
- 竞品分析：策略参考
- 数据统计：效果跟踪
- 团队协作：权限管理
```

---

## 4. PRP文档创建

### 4.1 PRP核心要素

Product Requirements Prompt应该包含以下关键信息：

#### **PRP文档结构模板**
```markdown
# PRP: [项目名称]

## 1. 项目背景和目标
### 业务背景
- 当前痛点：[具体描述现状问题]
- 市场机会：[说明市场需求和机会]
- 竞争态势：[分析竞品情况]

### 项目目标
- 业务目标：[量化的业务指标]
- 用户目标：[用户体验改善]
- 技术目标：[系统性能要求]

## 2. 用户需求分析
### 目标用户
- 用户画像：[详细的用户特征]
- 使用场景：[具体的使用情境]
- 需求优先级：[按重要性排序]

### 用户旅程
- 现状流程：[当前的操作流程]
- 期望流程：[理想的操作流程]
- 关键触点：[重要的交互节点]

## 3. 功能需求定义
### 核心功能
- 功能1：[详细描述和要求]
- 功能2：[详细描述和要求]
- 功能3：[详细描述和要求]

### 非功能需求
- 性能要求：[响应时间、并发量等]
- 可用性要求：[系统稳定性]
- 安全要求：[数据安全、隐私保护]

## 4. 业务规则和约束
### 业务规则
- 平台规则：[各平台的具体要求]
- 合规要求：[法律法规限制]
- 质量标准：[内容质量要求]

### 技术约束
- 集成要求：[需要集成的系统]
- 数据要求：[数据格式和来源]
- 接口要求：[API接口规范]

## 5. 成功标准和验收条件
### 量化指标
- 效率指标：[具体的数值目标]
- 质量指标：[质量评估标准]
- 用户满意度：[满意度目标]

### 验收条件
- 功能验收：[每个功能的验收标准]
- 性能验收：[性能测试标准]
- 用户验收：[用户测试标准]
```

### 4.2 需求描述最佳实践

#### **需求描述原则**
1. **具体化**：避免模糊表述，提供具体的数值和标准
2. **可验证**：每个需求都应该有明确的验收标准
3. **用户导向**：从用户角度描述需求，而非技术角度
4. **优先级明确**：区分必须有、重要、可选的功能

#### **需求描述示例对比**

**❌ 模糊描述**
```markdown
需求：系统要快
验收：用户觉得快就行
```

**✅ 具体描述**
```markdown
需求：标题生成响应时间
- 具体要求：用户点击"生成"按钮后，3秒内显示结果
- 验收标准：95%的请求在3秒内完成
- 测试方法：使用性能测试工具，模拟100并发用户
- 失败处理：超时显示友好提示，提供重试选项
```

### 4.3 业务场景用例

#### **用例描述模板**
```markdown
# 用例：商品标题生成

## 基本信息
- 用例名称：单个商品标题生成
- 参与者：电商运营人员
- 前置条件：用户已登录系统
- 触发条件：用户需要为新商品创建标题

## 主要流程
1. 用户进入标题生成页面
2. 用户填写商品基本信息
   - 商品类目（必填）
   - 品牌名称（必填）
   - 产品名称（必填）
   - 核心特性（选填，最多5个）
3. 用户选择目标平台（淘宝/天猫）
4. 用户选择文案风格（专业/时尚/促销）
5. 系统生成5-10个标题选项
6. 用户查看结果并选择合适的标题
7. 用户可以编辑选中的标题
8. 用户保存最终标题

## 异常流程
- 信息不完整：提示用户补充必填信息
- 生成失败：显示错误信息，提供重试选项
- 网络超时：显示超时提示，自动重试

## 验收标准
- 信息输入界面友好易用
- 生成结果在3秒内显示
- 生成的标题符合平台规则
- 用户可以顺利编辑和保存
```

---

## 5. 与AI协作方式

### 5.1 有效沟通策略

#### **与Augment AI沟通的最佳实践**

**1. 使用结构化的问题描述**
```markdown
# 好的沟通方式
我需要为电商文案生成系统设计用户界面，具体要求：

## 功能需求
- 支持商品信息输入（类目、品牌、名称、特性）
- 提供平台选择（淘宝/天猫）
- 显示生成的标题列表
- 支持标题编辑和保存

## 用户体验要求
- 界面简洁易用，适合非技术人员
- 操作流程不超过3步
- 提供实时预览功能
- 支持批量操作

## 技术约束
- 使用React框架
- 响应式设计，支持移动端
- 与后端API集成
- 支持数据导入导出

请帮我设计界面布局和交互流程。
```

**2. 提供具体的业务上下文**
```markdown
# 上下文信息提供示例
在设计这个功能时，请考虑以下业务背景：

## 用户特征
- 主要用户：电商运营人员，25-35岁
- 技术水平：熟悉电商平台，但编程能力有限
- 工作环境：快节奏，需要高效工具

## 使用场景
- 日常场景：每天需要处理10-50个商品
- 紧急场景：促销活动前批量更新文案
- 协作场景：团队成员需要共享和审核文案

## 业务规则
- 淘宝标题不超过30字符
- 必须避免使用禁用词汇
- 需要包含SEO关键词
- 要符合品牌调性
```

### 5.2 迭代反馈机制

#### **反馈提供框架**
```markdown
# 功能反馈模板

## 测试场景
- 测试时间：[具体时间]
- 测试用户：[用户角色]
- 测试环境：[测试条件]

## 功能表现
### 符合预期的部分
- [具体描述哪些功能工作正常]
- [用户体验好的地方]
- [性能表现满意的方面]

### 需要改进的部分
- [具体问题描述]
- [期望的改进方向]
- [优先级评估]

## 具体建议
- 建议1：[详细的改进建议]
- 建议2：[具体的功能调整]
- 建议3：[用户体验优化]

## 验收标准调整
- [是否需要调整原有的验收标准]
- [新的验收条件]
```

### 5.3 问题解决协作

#### **问题报告模板**
```markdown
# 问题报告

## 问题描述
- 问题现象：[具体描述遇到的问题]
- 发生频率：[偶发/频繁/必现]
- 影响范围：[影响的功能和用户]

## 重现步骤
1. [详细的操作步骤]
2. [每一步的具体操作]
3. [预期结果 vs 实际结果]

## 业务影响
- 对用户的影响：[用户体验影响]
- 对业务的影响：[业务流程影响]
- 紧急程度：[高/中/低]

## 期望解决方案
- 临时解决方案：[应急处理方法]
- 根本解决方案：[彻底解决建议]
- 预防措施：[避免再次发生]
```

---

## 6. 质量验收标准

### 6.1 功能验收清单

#### **功能验收模板**
```markdown
# 功能验收清单

## 核心功能验收

### 标题生成功能
- [ ] 用户可以输入完整的商品信息
- [ ] 系统在3秒内生成5-10个标题选项
- [ ] 生成的标题符合选定平台的字符限制
- [ ] 标题包含用户指定的关键词
- [ ] 系统自动过滤禁用词汇
- [ ] 提供质量评分和改进建议

### 平台适配功能
- [ ] 支持淘宝平台规则（30字符限制）
- [ ] 支持天猫平台规则（60字符限制）
- [ ] 正确识别和过滤各平台禁用词
- [ ] 适配不同平台的SEO规则

### 用户体验功能
- [ ] 界面简洁易用，操作直观
- [ ] 提供实时预览功能
- [ ] 支持标题编辑和保存
- [ ] 提供历史记录查看
- [ ] 支持批量操作

## 性能验收标准
- [ ] 页面加载时间 < 2秒
- [ ] 标题生成响应时间 < 3秒
- [ ] 支持100个并发用户
- [ ] 系统可用性 ≥ 99.5%
```

### 6.2 业务指标验收

#### **业务指标验收框架**
```markdown
# 业务指标验收

## 效率指标
### 时间效率
- 基准：手动创作平均160分钟/商品
- 目标：AI辅助创作20分钟/商品
- 验收：实际测试平均时间 ≤ 25分钟/商品

### 批量处理
- 目标：支持100个商品/小时批量处理
- 验收：实际测试批量处理能力 ≥ 80个商品/小时

## 质量指标
### 内容质量
- 目标：用户满意度 ≥ 4.5/5.0
- 验收：用户测试满意度评分
- 测试方法：邀请20名目标用户进行为期1周的使用测试

### 合规率
- 目标：平台合规率 ≥ 95%
- 验收：随机抽取1000个生成标题进行合规检查
- 测试方法：使用平台官方检测工具验证

## 采用率指标
### 用户采用率
- 目标：生成文案的最终采用率 ≥ 70%
- 验收：跟踪用户实际使用情况
- 测试周期：上线后连续4周数据统计

### 业务价值
- 目标：转化率提升 ≥ 15%
- 验收：A/B测试对比AI生成文案与人工文案的转化效果
- 测试周期：3个月业务数据对比
```

### 6.3 用户验收测试

#### **用户验收测试计划**
```markdown
# 用户验收测试（UAT）计划

## 测试准备
### 测试用户招募
- 目标用户：电商运营人员 10名
- 用户背景：1-5年电商运营经验
- 测试时间：每人2小时深度测试
- 测试环境：真实业务场景

### 测试场景设计
#### 场景1：新手用户首次使用
- 测试目标：验证系统易用性
- 测试任务：为5个不同类目商品生成标题
- 成功标准：无需培训即可完成任务

#### 场景2：熟练用户日常使用
- 测试目标：验证工作效率提升
- 测试任务：30分钟内完成20个商品标题生成
- 成功标准：比手动创作节省80%时间

#### 场景3：批量处理场景
- 测试目标：验证批量处理能力
- 测试任务：批量导入50个商品信息并生成标题
- 成功标准：1小时内完成所有任务

## 测试执行
### 测试流程
1. **前期准备**（30分钟）
   - 介绍系统功能和测试目标
   - 提供测试账号和测试数据
   - 说明测试流程和注意事项

2. **功能测试**（60分钟）
   - 按照测试场景执行任务
   - 记录操作过程和遇到的问题
   - 收集用户反馈和建议

3. **深度访谈**（30分钟）
   - 了解用户使用感受
   - 收集改进建议
   - 评估业务价值

### 数据收集
#### 定量数据
- 任务完成时间
- 操作错误次数
- 功能使用频率
- 满意度评分（1-5分）

#### 定性反馈
- 用户体验感受
- 功能改进建议
- 业务价值评估
- 推荐意愿

## 验收标准
### 通过标准
- 90%的用户能够独立完成基本任务
- 平均满意度评分 ≥ 4.0/5.0
- 80%的用户认为系统能提升工作效率
- 70%的用户愿意推荐给同事使用

### 改进要求
- 如果满意度 < 4.0，需要根据反馈进行改进
- 如果任务完成率 < 90%，需要优化用户体验
- 如果效率提升不明显，需要优化功能流程
```

### 6.4 持续改进机制

#### **上线后监控和优化**
```markdown
# 上线后质量监控

## 监控指标
### 系统性能监控
- API响应时间：实时监控，告警阈值3秒
- 系统可用性：目标99.5%，每月统计
- 错误率：目标<1%，每日监控

### 业务指标监控
- 日活跃用户数：每日统计
- 功能使用频率：每周分析
- 用户满意度：每月调研
- 业务价值：每季度评估

## 反馈收集机制
### 用户反馈渠道
- 系统内反馈：一键反馈功能
- 定期调研：月度用户满意度调研
- 客服反馈：用户问题和建议收集
- 数据分析：用户行为数据分析

### 改进流程
1. **问题识别**：通过监控和反馈发现问题
2. **优先级评估**：根据影响范围和严重程度排序
3. **解决方案设计**：制定具体的改进方案
4. **实施和验证**：实施改进并验证效果
5. **持续监控**：跟踪改进效果和新问题
```

---

## 总结

作为非技术背景的产品需求方，在使用 Augment AI 进行项目开发时，您的核心职责是：

### 🎯 **关键成功要素**
1. **深度的业务理解**：提供详细的行业规则、用户需求和业务流程
2. **清晰的需求表达**：用业务语言描述功能需求，避免技术术语
3. **结构化的信息组织**：在Context Engineering框架下系统性地组织项目信息
4. **有效的协作沟通**：与AI保持持续、具体、结构化的沟通
5. **明确的验收标准**：定义可量化、可验证的成功指标

### 📋 **行动清单**
- [ ] 收集和整理完整的业务规则和行业标准
- [ ] 创建详细的用户画像和使用场景分析
- [ ] 编写结构化的PRP文档
- [ ] 建立清晰的验收标准和测试计划
- [ ] 设计有效的反馈和改进机制

### 💡 **记住**
您不需要懂技术，但需要深度理解业务。Augment AI 会处理技术实现，而您的价值在于提供准确的业务需求和持续的反馈指导。通过遵循这个指南，您可以确保开发出真正满足业务需求的高质量产品。
