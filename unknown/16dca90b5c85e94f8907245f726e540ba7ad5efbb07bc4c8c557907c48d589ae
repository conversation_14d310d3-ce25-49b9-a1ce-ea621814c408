// AI电商文案生成系统 - 测试用例

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import {
  ContentGenerationService,
  PlatformRuleEngine,
  MockAIProvider,
  GenerationRequest,
  ProductInfo,
  Platform
} from '../src/core-implementation';

describe('AI电商文案生成系统测试', () => {
  let contentService: ContentGenerationService;
  let ruleEngine: PlatformRuleEngine;
  let mockProvider: MockAIProvider;

  // 测试数据
  const sampleProductInfo: ProductInfo = {
    category: '女装/女士精品',
    brand: '优衣库',
    productName: '羊毛混纺针织衫',
    keyFeatures: ['保暖', '舒适', '百搭', '不起球'],
    specifications: {
      material: '70%羊毛+30%聚酯纤维',
      colors: ['黑色', '白色', '灰色', '米色'],
      sizes: ['S', 'M', 'L', 'XL']
    },
    targetAudience: '25-35岁职场女性',
    priceRange: '199-299'
  };

  const taobaoplatform: Platform = {
    name: 'taobao',
    maxTitleLength: 30,
    forbiddenWords: ['最', '第一', '唯一', '极品'],
    seoRules: []
  };

  beforeEach(() => {
    contentService = new ContentGenerationService();
    ruleEngine = new PlatformRuleEngine();
    mockProvider = new MockAIProvider();
    contentService.registerProvider('mock', mockProvider);
    contentService.setCurrentProvider('mock');
  });

  describe('平台规则引擎测试', () => {
    test('应该正确验证符合规则的标题', () => {
      const validTitle = '优衣库羊毛针织衫保暖百搭';
      const result = ruleEngine.validateContent(validTitle, 'taobao');
      
      expect(result.isValid).toBe(true);
      expect(result.violations).toHaveLength(0);
      expect(result.score).toBeGreaterThan(80);
    });

    test('应该检测出超长标题', () => {
      const longTitle = '优衣库羊毛混纺针织衫保暖舒适百搭不起球职场女性必备款式多色可选';
      const result = ruleEngine.validateContent(longTitle, 'taobao');
      
      expect(result.isValid).toBe(false);
      expect(result.violations).toContain('内容长度超过30字符限制');
      expect(result.score).toBeLessThan(100);
    });

    test('应该检测出禁用词汇', () => {
      const titleWithForbiddenWord = '优衣库最好的羊毛针织衫';
      const result = ruleEngine.validateContent(titleWithForbiddenWord, 'taobao');
      
      expect(result.isValid).toBe(false);
      expect(result.violations.some(v => v.includes('禁用词汇'))).toBe(true);
    });

    test('应该支持不同平台的规则', () => {
      const platform = ruleEngine.getPlatform('taobao');
      expect(platform).toBeDefined();
      expect(platform?.maxTitleLength).toBe(30);
      
      const tmallPlatform = ruleEngine.getPlatform('tmall');
      expect(tmallPlatform).toBeDefined();
      expect(tmallPlatform?.maxTitleLength).toBe(60);
    });
  });

  describe('内容生成服务测试', () => {
    test('应该成功生成商品标题', async () => {
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫', '针织衫', '保暖', '百搭'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
      expect(results[0]).toHaveProperty('content');
      expect(results[0]).toHaveProperty('score');
      expect(results[0]).toHaveProperty('seoScore');
      expect(results[0]).toHaveProperty('complianceCheck');
    });

    test('生成的标题应该包含关键信息', async () => {
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'trendy',
        keywords: ['羊毛衫', '保暖'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      const firstTitle = results[0];
      
      // 检查是否包含品牌
      expect(firstTitle.content).toContain(sampleProductInfo.brand);
      
      // 检查是否包含产品名或关键特性
      const containsProductInfo = 
        firstTitle.content.includes(sampleProductInfo.productName) ||
        sampleProductInfo.keyFeatures.some(feature => 
          firstTitle.content.includes(feature)
        );
      expect(containsProductInfo).toBe(true);
    });

    test('应该按质量评分排序结果', async () => {
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫', '针织衫'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      
      // 检查是否按分数降序排列
      for (let i = 1; i < results.length; i++) {
        expect(results[i-1].score).toBeGreaterThanOrEqual(results[i].score);
      }
    });

    test('应该处理AI提供商错误', async () => {
      // 创建一个会抛出错误的提供商
      const errorProvider = {
        name: 'error-provider',
        generateContent: jest.fn().mockRejectedValue(new Error('API调用失败'))
      };
      
      contentService.registerProvider('error', errorProvider as any);
      contentService.setCurrentProvider('error');

      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫'],
        contentType: 'title'
      };

      await expect(contentService.generateTitles(request)).rejects.toThrow('API调用失败');
    });

    test('应该支持故障转移机制', async () => {
      // 注册一个失败的提供商和一个成功的提供商
      const failingProvider = {
        name: 'failing-provider',
        generateContent: jest.fn().mockRejectedValue(new Error('服务不可用'))
      };
      
      contentService.registerProvider('failing', failingProvider as any);
      contentService.registerProvider('backup', mockProvider);

      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫'],
        contentType: 'title'
      };

      // 测试故障转移
      const results = await contentService.generateWithFallback(request);
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
    });
  });

  describe('SEO优化测试', () => {
    test('应该正确计算关键词密度', () => {
      const content = '优衣库羊毛衫保暖针织衫';
      const keywords = ['羊毛衫', '针织衫'];
      
      // 模拟SEO评分计算
      const totalChars = content.length;
      let keywordChars = 0;
      keywords.forEach(keyword => {
        const matches = content.match(new RegExp(keyword, 'g'));
        if (matches) {
          keywordChars += matches.length * keyword.length;
        }
      });
      
      const density = (keywordChars / totalChars) * 100;
      expect(density).toBeGreaterThan(0);
      expect(density).toBeLessThan(50); // 合理的密度范围
    });

    test('应该识别缺失的关键词', async () => {
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫', '针织衫', '保暖', '特殊关键词'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      
      // 检查是否有改进建议
      const hasImprovements = results.some(result => 
        result.improvements && result.improvements.length > 0
      );
      expect(hasImprovements).toBe(true);
    });
  });

  describe('内容质量评估测试', () => {
    test('应该给高质量标题更高的评分', async () => {
      const highQualityRequest: GenerationRequest = {
        productInfo: {
          ...sampleProductInfo,
          keyFeatures: ['保暖', '舒适', '百搭', '高品质']
        },
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫', '针织衫', '保暖'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(highQualityRequest);
      
      // 高质量内容应该有较高的评分
      expect(results[0].score).toBeGreaterThan(70);
      expect(results[0].seoScore).toBeGreaterThan(60);
    });

    test('应该检测内容合规性', async () => {
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      
      // 所有结果都应该通过合规检查
      results.forEach(result => {
        expect(result.complianceCheck).toBe(true);
      });
    });

    test('应该提供有用的改进建议', async () => {
      const request: GenerationRequest = {
        productInfo: {
          ...sampleProductInfo,
          keyFeatures: ['特性1'] // 简化的特性
        },
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['关键词1', '关键词2', '关键词3'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      
      // 应该有改进建议
      const hasUsefulSuggestions = results.some(result => 
        result.improvements && 
        result.improvements.length > 0 &&
        result.improvements.some(suggestion => 
          suggestion.includes('关键词') || suggestion.includes('特性')
        )
      );
      expect(hasUsefulSuggestions).toBe(true);
    });
  });

  describe('性能测试', () => {
    test('内容生成应该在合理时间内完成', async () => {
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫'],
        contentType: 'title'
      };

      const startTime = Date.now();
      await contentService.generateTitles(request);
      const endTime = Date.now();
      
      const duration = endTime - startTime;
      expect(duration).toBeLessThan(5000); // 应该在5秒内完成
    });

    test('应该支持并发请求', async () => {
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['羊毛衫'],
        contentType: 'title'
      };

      // 并发执行多个请求
      const promises = Array(5).fill(null).map(() => 
        contentService.generateTitles(request)
      );

      const results = await Promise.all(promises);
      
      // 所有请求都应该成功
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.length).toBeGreaterThan(0);
      });
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空的产品信息', async () => {
      const emptyProductInfo: ProductInfo = {
        category: '',
        brand: '',
        productName: '',
        keyFeatures: [],
        specifications: {},
        targetAudience: ''
      };

      const request: GenerationRequest = {
        productInfo: emptyProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: [],
        contentType: 'title'
      };

      // 应该能处理空数据而不崩溃
      const results = await contentService.generateTitles(request);
      expect(results).toBeDefined();
    });

    test('应该处理超长的关键词列表', async () => {
      const longKeywordList = Array(50).fill(null).map((_, i) => `关键词${i}`);
      
      const request: GenerationRequest = {
        productInfo: sampleProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: longKeywordList,
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
    });

    test('应该处理特殊字符', async () => {
      const specialProductInfo: ProductInfo = {
        ...sampleProductInfo,
        productName: '特殊字符测试!@#$%^&*()',
        keyFeatures: ['特性1!', '特性2@', '特性3#']
      };

      const request: GenerationRequest = {
        productInfo: specialProductInfo,
        platform: taobaoplatform,
        style: 'professional',
        keywords: ['关键词!', '关键词@'],
        contentType: 'title'
      };

      const results = await contentService.generateTitles(request);
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
    });
  });
});

// ===== 集成测试 =====
describe('系统集成测试', () => {
  test('完整的文案生成工作流程', async () => {
    const contentService = new ContentGenerationService();
    const mockProvider = new MockAIProvider();
    contentService.registerProvider('mock', mockProvider);
    contentService.setCurrentProvider('mock');

    const productInfo: ProductInfo = {
      category: '数码/手机',
      brand: 'Apple',
      productName: 'iPhone 15 Pro',
      keyFeatures: ['A17芯片', '钛金属', '专业摄影', '5G'],
      specifications: {
        storage: ['128GB', '256GB', '512GB', '1TB'],
        colors: ['原色钛金属', '蓝色钛金属', '白色钛金属', '黑色钛金属']
      },
      targetAudience: '科技爱好者',
      priceRange: '7999-13999'
    };

    const platform: Platform = {
      name: 'tmall',
      maxTitleLength: 60,
      forbiddenWords: ['最', '第一'],
      seoRules: []
    };

    // 1. 生成标题
    const titleRequest: GenerationRequest = {
      productInfo,
      platform,
      style: 'professional',
      keywords: ['iPhone', '手机', '5G', '摄影'],
      contentType: 'title'
    };

    const titles = await contentService.generateTitles(titleRequest);
    expect(titles.length).toBeGreaterThan(0);
    expect(titles[0].complianceCheck).toBe(true);

    // 2. 生成卖点文案
    const sellingPointsRequest: GenerationRequest = {
      ...titleRequest,
      contentType: 'selling_points'
    };

    const sellingPoints = await contentService.generateSellingPoints(sellingPointsRequest);
    expect(sellingPoints).toBeDefined();

    // 3. 生成详情页文案
    const detailPageRequest: GenerationRequest = {
      ...titleRequest,
      contentType: 'detail_page'
    };

    const detailPage = await contentService.generateDetailPage(detailPageRequest);
    expect(detailPage).toBeDefined();

    console.log('集成测试完成 - 所有功能正常工作');
  });
});
