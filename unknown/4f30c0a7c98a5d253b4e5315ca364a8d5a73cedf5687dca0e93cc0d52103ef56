# Git & GitHub 完整教程 - 文件索引

> 快速导航到您需要的内容

## 🚀 快速开始

### 我是完全初学者
👉 **推荐路径**: [可视化指南](./visual-guide.md) → [主教程](./README.md) → [实际示例](./practical-examples.md)

### 我有编程经验但没用过Git
👉 **推荐路径**: [主教程](./README.md) → [实际示例](./practical-examples.md) → [快速参考](./quick-reference.md)

### 我想快速查找命令
👉 **直接查看**: [快速参考手册](./quick-reference.md)

### 我遇到了问题
👉 **问题解决**: [故障排除指南](./troubleshooting.md)

---

## 📚 完整文件列表

### 1. [README.md](./README.md) - 主教程 📖
**内容**: 完整的Git和GitHub学习指南  
**适合**: 所有学习者  
**时长**: 2-3小时  

**包含章节**:
- Git基础概念解释
- 安装和配置详细步骤
- 基本操作命令教学
- GitHub使用指南
- 团队协作流程
- 最佳实践建议

### 2. [quick-reference.md](./quick-reference.md) - 快速参考 ⚡
**内容**: 常用Git命令速查表  
**适合**: 需要快速查找命令的开发者  
**时长**: 随时查阅  

**包含内容**:
- 日常开发必备命令
- 问题解决命令集
- 配置和别名设置
- 移动端友好版本

### 3. [troubleshooting.md](./troubleshooting.md) - 故障排除 🔧
**内容**: 常见问题和解决方案  
**适合**: 遇到Git问题的用户  
**时长**: 按需查阅  

**包含内容**:
- 新手常见错误处理
- 紧急情况救援方法
- 数据恢复技巧
- 预防措施建议

### 4. [practical-examples.md](./practical-examples.md) - 实际示例 💼
**内容**: 真实项目场景演示  
**适合**: 想要实际练习的学习者  
**时长**: 1-2小时实践  

**包含项目**:
- 个人博客网站开发
- 团队协作项目流程
- 开源项目贡献指南
- 完整的操作演示

### 5. [visual-guide.md](./visual-guide.md) - 可视化指南 📊
**内容**: 图表和可视化说明  
**适合**: 视觉学习者和初学者  
**时长**: 30分钟理解  

**包含图表**:
- Git工作流程图解
- 分支操作可视化
- 命令执行过程图
- 状态转换示意图

---

## 🎯 按需求查找内容

### 📝 我想学习基础概念
- [README.md - Git基础概念](./README.md#1-git基础概念)
- [visual-guide.md - Git工作流程可视化](./visual-guide.md#git工作流程可视化)

### ⚙️ 我需要安装和配置Git
- [README.md - Git安装和配置](./README.md#2-git安装和配置)
- [quick-reference.md - 配置命令](./quick-reference.md#配置命令)

### 🔨 我想学习基本操作
- [README.md - 基本Git操作](./README.md#3-基本git操作)
- [practical-examples.md - 项目场景1](./practical-examples.md#项目场景1个人博客网站)

### 🌐 我要使用GitHub
- [README.md - GitHub使用指南](./README.md#4-github使用指南)
- [practical-examples.md - 团队协作](./practical-examples.md#项目场景2团队协作开发)

### 🌿 我想学习分支操作
- [README.md - 分支操作](./README.md#36-分支操作)
- [visual-guide.md - 分支可视化](./visual-guide.md#分支可视化)

### 🤝 我需要团队协作
- [practical-examples.md - 团队协作开发](./practical-examples.md#项目场景2团队协作开发)
- [practical-examples.md - 开源项目贡献](./practical-examples.md#项目场景3开源项目贡献)

### 🚨 我遇到了问题
- [troubleshooting.md - 紧急情况处理](./troubleshooting.md#紧急情况处理)
- [troubleshooting.md - 推送和拉取问题](./troubleshooting.md#推送和拉取问题)

### 📋 我需要命令参考
- [quick-reference.md - 基础命令](./quick-reference.md#基础命令)
- [README.md - Git命令速查表](./README.md#8-git命令速查表)

---

## 📱 移动端用户

### 简化版内容
- [quick-reference.md - 移动端友好版本](./quick-reference.md#移动端友好版本)
- [visual-guide.md - 移动端友好的简化图表](./visual-guide.md#移动端友好的简化图表)

### 最常用的10个命令
```bash
git status          # 查看状态
git add .           # 添加所有文件
git commit -m ""    # 提交
git push            # 推送
git pull            # 拉取
git checkout -b     # 创建分支
git merge           # 合并分支
git log --oneline   # 查看历史
git diff            # 查看差异
git stash           # 暂存修改
```

---

## 🖨️ 打印版本

### 适合打印的文件
1. **[quick-reference.md](./quick-reference.md)** - 命令速查表
2. **[troubleshooting.md](./troubleshooting.md)** - 问题解决清单

### 打印建议
- 使用A4纸张
- 双面打印节省纸张
- 可以装订成小册子
- 放在桌边随时查阅

---

## 🎓 学习进度跟踪

### 初级阶段 (第1-2周)
- [ ] 理解Git基本概念
- [ ] 完成Git安装和配置
- [ ] 学会基本的add、commit、push操作
- [ ] 创建第一个GitHub仓库

### 中级阶段 (第3-4周)
- [ ] 熟练使用分支操作
- [ ] 学会解决合并冲突
- [ ] 掌握团队协作流程
- [ ] 完成一个实际项目

### 高级阶段 (第5-6周)
- [ ] 掌握高级Git命令
- [ ] 学会代码审查流程
- [ ] 参与开源项目贡献
- [ ] 建立自己的最佳实践

---

## 🔗 外部资源

### 官方文档
- [Git官方网站](https://git-scm.com/)
- [GitHub官方指南](https://guides.github.com/)
- [Pro Git电子书](https://git-scm.com/book)

### 在线练习
- [Learn Git Branching](https://learngitbranching.js.org/) - 交互式Git学习
- [GitHub Skills](https://skills.github.com/) - GitHub官方技能课程

### 图形化工具
- [GitHub Desktop](https://desktop.github.com/) - GitHub官方桌面应用
- [GitKraken](https://www.gitkraken.com/) - 专业Git客户端
- [SourceTree](https://www.sourcetreeapp.com/) - Atlassian的Git工具

---

## 💬 获取帮助

### 社区支持
- [Stack Overflow](https://stackoverflow.com/questions/tagged/git) - Git相关问题
- [GitHub Community](https://github.community/) - GitHub社区论坛
- [Reddit r/git](https://www.reddit.com/r/git/) - Git讨论社区

### 本地帮助
```bash
git help <command>    # 查看命令帮助
git <command> --help  # 查看命令帮助
git <command> -h      # 查看简短帮助
```

---

**🎉 开始您的Git学习之旅吧！选择适合您的文件开始学习。**
