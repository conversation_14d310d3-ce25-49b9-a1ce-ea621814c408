// AI电商文案生成系统 - 核心实现代码示例

// ===== 类型定义 =====
export interface ProductInfo {
  category: string;
  brand: string;
  productName: string;
  keyFeatures: string[];
  specifications: Record<string, any>;
  targetAudience: string;
  priceRange?: string;
  competitorInfo?: string[];
}

export interface Platform {
  name: 'taobao' | 'tmall';
  maxTitleLength: number;
  forbiddenWords: string[];
  seoRules: SEORule[];
}

export interface GenerationRequest {
  productInfo: ProductInfo;
  platform: Platform;
  style: 'professional' | 'trendy' | 'promotional' | 'luxury';
  keywords: string[];
  contentType: 'title' | 'selling_points' | 'detail_page' | 'marketing';
}

export interface GeneratedContent {
  content: string;
  score: number;
  seoScore: number;
  complianceCheck: boolean;
  reasoning: string;
  improvements: string[];
}

// ===== AI服务提供商抽象层 =====
export abstract class AIProvider {
  abstract name: string;
  abstract generateContent(request: GenerationRequest): Promise<GeneratedContent[]>;
  
  protected buildPrompt(request: GenerationRequest): string {
    const { productInfo, platform, style, keywords, contentType } = request;
    
    const baseContext = `
你是一个专业的电商文案专家，专门为${platform.name}平台创作${contentType}。

产品信息：
- 类目：${productInfo.category}
- 品牌：${productInfo.brand}
- 产品名称：${productInfo.productName}
- 核心特性：${productInfo.keyFeatures.join('、')}
- 目标受众：${productInfo.targetAudience}
- SEO关键词：${keywords.join('、')}

平台要求：
- 最大长度：${platform.maxTitleLength}字符
- 风格要求：${style}
- 禁用词汇：避免使用${platform.forbiddenWords.slice(0, 10).join('、')}等词汇
`;

    return this.getContentTypePrompt(contentType, baseContext, request);
  }
  
  private getContentTypePrompt(contentType: string, baseContext: string, request: GenerationRequest): string {
    switch (contentType) {
      case 'title':
        return `${baseContext}
请生成5个不同风格的商品标题，要求：
1. 符合平台字符限制
2. 包含核心SEO关键词
3. 突出产品卖点
4. 吸引目标受众
5. 遵循平台规则

输出JSON格式：
{
  "titles": [
    {
      "content": "标题内容",
      "reasoning": "创作理由",
      "seoKeywords": ["关键词1", "关键词2"],
      "appealPoints": ["卖点1", "卖点2"]
    }
  ]
}`;

      case 'selling_points':
        return `${baseContext}
请使用FABE销售法则生成产品卖点文案：
- Feature（特性）：产品的功能特点
- Advantage（优势）：相比竞品的优势
- Benefit（利益）：给用户带来的好处
- Evidence（证据）：支撑证据

输出JSON格式：
{
  "sellingPoints": [
    {
      "title": "卖点标题",
      "feature": "产品特性",
      "advantage": "竞争优势", 
      "benefit": "用户利益",
      "evidence": "支撑证据",
      "emotionalAppeal": "情感诉求"
    }
  ]
}`;

      case 'detail_page':
        return `${baseContext}
请生成详情页文案结构，包含：
1. 产品介绍
2. 核心卖点展示
3. 使用场景描述
4. 规格参数说明
5. 常见问题解答

输出JSON格式：
{
  "sections": [
    {
      "type": "intro|features|usage|specs|faq",
      "title": "章节标题",
      "content": "章节内容",
      "designSuggestions": ["设计建议1", "设计建议2"]
    }
  ]
}`;

      default:
        return baseContext;
    }
  }
}

// ===== OpenAI提供商实现 =====
export class OpenAIProvider extends AIProvider {
  name = 'openai';
  private client: any; // OpenAI client
  
  constructor(apiKey: string) {
    super();
    // this.client = new OpenAI({ apiKey });
  }
  
  async generateContent(request: GenerationRequest): Promise<GeneratedContent[]> {
    try {
      const prompt = this.buildPrompt(request);
      
      const response = await this.client.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的电商文案专家，擅长创作高转化率的商品文案。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        response_format: { type: 'json_object' }
      });
      
      const result = JSON.parse(response.choices[0].message.content);
      return this.parseResponse(result, request);
      
    } catch (error) {
      console.error('OpenAI API调用失败:', error);
      throw new Error(`内容生成失败: ${error.message}`);
    }
  }
  
  private parseResponse(result: any, request: GenerationRequest): GeneratedContent[] {
    const contents: GeneratedContent[] = [];
    
    if (request.contentType === 'title' && result.titles) {
      for (const title of result.titles) {
        contents.push({
          content: title.content,
          score: this.calculateQualityScore(title.content, request),
          seoScore: this.calculateSEOScore(title.content, request.keywords),
          complianceCheck: this.checkCompliance(title.content, request.platform),
          reasoning: title.reasoning,
          improvements: this.generateImprovements(title.content, request)
        });
      }
    }
    
    return contents;
  }
  
  private calculateQualityScore(content: string, request: GenerationRequest): number {
    let score = 70; // 基础分
    
    // 长度检查
    if (content.length <= request.platform.maxTitleLength) {
      score += 10;
    }
    
    // 关键词包含检查
    const keywordCount = request.keywords.filter(keyword => 
      content.includes(keyword)
    ).length;
    score += Math.min(keywordCount * 5, 15);
    
    // 品牌和产品名检查
    if (content.includes(request.productInfo.brand)) score += 5;
    if (content.includes(request.productInfo.productName)) score += 5;
    
    return Math.min(score, 100);
  }
  
  private calculateSEOScore(content: string, keywords: string[]): number {
    let score = 0;
    
    // 关键词密度计算
    const totalWords = content.length;
    let keywordChars = 0;
    
    keywords.forEach(keyword => {
      const matches = content.match(new RegExp(keyword, 'g'));
      if (matches) {
        keywordChars += matches.length * keyword.length;
      }
    });
    
    const density = (keywordChars / totalWords) * 100;
    
    // 理想密度2-8%
    if (density >= 2 && density <= 8) {
      score = 100;
    } else if (density < 2) {
      score = density * 50; // 密度不足
    } else {
      score = Math.max(0, 100 - (density - 8) * 10); // 密度过高
    }
    
    return Math.round(score);
  }
  
  private checkCompliance(content: string, platform: Platform): boolean {
    // 长度检查
    if (content.length > platform.maxTitleLength) {
      return false;
    }
    
    // 禁用词检查
    for (const forbiddenWord of platform.forbiddenWords) {
      if (content.includes(forbiddenWord)) {
        return false;
      }
    }
    
    return true;
  }
  
  private generateImprovements(content: string, request: GenerationRequest): string[] {
    const improvements: string[] = [];
    
    // 长度优化建议
    if (content.length > request.platform.maxTitleLength) {
      improvements.push(`标题过长，建议缩短至${request.platform.maxTitleLength}字符以内`);
    }
    
    // 关键词优化建议
    const missingKeywords = request.keywords.filter(keyword => 
      !content.includes(keyword)
    );
    if (missingKeywords.length > 0) {
      improvements.push(`建议添加关键词：${missingKeywords.join('、')}`);
    }
    
    // 卖点优化建议
    if (!request.productInfo.keyFeatures.some(feature => content.includes(feature))) {
      improvements.push('建议突出产品核心特性');
    }
    
    return improvements;
  }
}

// ===== 平台规则引擎 =====
export class PlatformRuleEngine {
  private platforms: Map<string, Platform> = new Map();
  
  constructor() {
    this.initializePlatforms();
  }
  
  private initializePlatforms() {
    // 淘宝平台规则
    this.platforms.set('taobao', {
      name: 'taobao',
      maxTitleLength: 30,
      forbiddenWords: [
        '最', '第一', '唯一', '极品', '顶级', '至尊', '独家', '限量',
        '秒杀', '抢购', '特价', '包邮', '正品', '原装', '进口'
      ],
      seoRules: [
        { type: 'keyword_density', min: 2, max: 8 },
        { type: 'title_structure', required: ['brand', 'product', 'feature'] }
      ]
    });
    
    // 天猫平台规则
    this.platforms.set('tmall', {
      name: 'tmall',
      maxTitleLength: 60,
      forbiddenWords: [
        '最', '第一', '唯一', '极品', '顶级', '至尊', '独家'
      ],
      seoRules: [
        { type: 'keyword_density', min: 2, max: 8 },
        { type: 'title_structure', required: ['brand', 'product', 'feature'] }
      ]
    });
  }
  
  getPlatform(name: string): Platform | undefined {
    return this.platforms.get(name);
  }
  
  validateContent(content: string, platformName: string): ValidationResult {
    const platform = this.getPlatform(platformName);
    if (!platform) {
      throw new Error(`不支持的平台: ${platformName}`);
    }
    
    const violations: string[] = [];
    
    // 长度检查
    if (content.length > platform.maxTitleLength) {
      violations.push(`内容长度超过${platform.maxTitleLength}字符限制`);
    }
    
    // 禁用词检查
    for (const forbiddenWord of platform.forbiddenWords) {
      if (content.includes(forbiddenWord)) {
        violations.push(`包含禁用词汇: ${forbiddenWord}`);
      }
    }
    
    return {
      isValid: violations.length === 0,
      violations,
      score: this.calculateComplianceScore(content, platform)
    };
  }
  
  private calculateComplianceScore(content: string, platform: Platform): number {
    let score = 100;
    
    // 长度扣分
    if (content.length > platform.maxTitleLength) {
      const excess = content.length - platform.maxTitleLength;
      score -= Math.min(excess * 2, 30);
    }
    
    // 禁用词扣分
    const forbiddenWordCount = platform.forbiddenWords.filter(word => 
      content.includes(word)
    ).length;
    score -= forbiddenWordCount * 20;
    
    return Math.max(score, 0);
  }
}

// ===== 内容生成服务 =====
export class ContentGenerationService {
  private aiProviders: Map<string, AIProvider> = new Map();
  private ruleEngine: PlatformRuleEngine;
  private currentProvider: string = 'openai';
  
  constructor() {
    this.ruleEngine = new PlatformRuleEngine();
  }
  
  registerProvider(name: string, provider: AIProvider) {
    this.aiProviders.set(name, provider);
  }
  
  setCurrentProvider(name: string) {
    if (!this.aiProviders.has(name)) {
      throw new Error(`AI提供商不存在: ${name}`);
    }
    this.currentProvider = name;
  }
  
  async generateTitles(request: GenerationRequest): Promise<GeneratedContent[]> {
    const provider = this.aiProviders.get(this.currentProvider);
    if (!provider) {
      throw new Error(`AI提供商未初始化: ${this.currentProvider}`);
    }
    
    try {
      // 生成内容
      const contents = await provider.generateContent({
        ...request,
        contentType: 'title'
      });
      
      // 验证合规性
      const validatedContents = contents.map(content => ({
        ...content,
        ...this.ruleEngine.validateContent(content.content, request.platform.name)
      }));
      
      // 按质量评分排序
      return validatedContents.sort((a, b) => b.score - a.score);
      
    } catch (error) {
      console.error('标题生成失败:', error);
      throw error;
    }
  }
  
  async generateSellingPoints(request: GenerationRequest): Promise<GeneratedContent[]> {
    const provider = this.aiProviders.get(this.currentProvider);
    if (!provider) {
      throw new Error(`AI提供商未初始化: ${this.currentProvider}`);
    }
    
    return provider.generateContent({
      ...request,
      contentType: 'selling_points'
    });
  }
  
  async generateDetailPage(request: GenerationRequest): Promise<GeneratedContent[]> {
    const provider = this.aiProviders.get(this.currentProvider);
    if (!provider) {
      throw new Error(`AI提供商未初始化: ${this.currentProvider}`);
    }
    
    return provider.generateContent({
      ...request,
      contentType: 'detail_page'
    });
  }
  
  async generateWithFallback(request: GenerationRequest): Promise<GeneratedContent[]> {
    const providers = Array.from(this.aiProviders.keys());
    let lastError: Error | null = null;
    
    for (const providerName of providers) {
      try {
        this.setCurrentProvider(providerName);
        return await this.generateTitles(request);
      } catch (error) {
        console.warn(`提供商 ${providerName} 失败:`, error);
        lastError = error as Error;
        continue;
      }
    }
    
    throw new Error(`所有AI提供商都失败了。最后错误: ${lastError?.message}`);
  }
}

// ===== 辅助类型定义 =====
interface SEORule {
  type: string;
  min?: number;
  max?: number;
  required?: string[];
}

interface ValidationResult {
  isValid: boolean;
  violations: string[];
  score: number;
}

// ===== 使用示例 =====
export async function exampleUsage() {
  // 初始化服务
  const contentService = new ContentGenerationService();

  // 注册AI提供商
  const openaiProvider = new OpenAIProvider('your-openai-api-key');
  contentService.registerProvider('openai', openaiProvider);

  // 准备请求数据
  const request: GenerationRequest = {
    productInfo: {
      category: '女装/女士精品',
      brand: '优衣库',
      productName: '羊毛混纺针织衫',
      keyFeatures: ['保暖', '舒适', '百搭', '不起球'],
      specifications: {
        material: '70%羊毛+30%聚酯纤维',
        colors: ['黑色', '白色', '灰色', '米色'],
        sizes: ['S', 'M', 'L', 'XL']
      },
      targetAudience: '25-35岁职场女性',
      priceRange: '199-299'
    },
    platform: {
      name: 'taobao',
      maxTitleLength: 30,
      forbiddenWords: ['最', '第一', '唯一'],
      seoRules: []
    },
    style: 'professional',
    keywords: ['羊毛衫', '针织衫', '保暖', '百搭', '职场'],
    contentType: 'title'
  };

  try {
    // 生成标题
    const titles = await contentService.generateTitles(request);
    console.log('生成的标题:', titles);

    // 生成卖点文案
    const sellingPoints = await contentService.generateSellingPoints(request);
    console.log('生成的卖点:', sellingPoints);

  } catch (error) {
    console.error('生成失败:', error);
  }
}

// ===== Mock AI Provider for Testing =====
export class MockAIProvider extends AIProvider {
  name = 'mock';

  async generateContent(request: GenerationRequest): Promise<GeneratedContent[]> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    if (request.contentType === 'title') {
      return [
        {
          content: `${request.productInfo.brand} ${request.productInfo.productName} ${request.productInfo.keyFeatures[0]}`,
          score: 85,
          seoScore: 78,
          complianceCheck: true,
          reasoning: '包含品牌、产品名和核心特性，符合平台规则',
          improvements: ['建议添加更多关键词']
        },
        {
          content: `${request.productInfo.keyFeatures.join(' ')} ${request.productInfo.productName}`,
          score: 82,
          seoScore: 85,
          complianceCheck: true,
          reasoning: '突出产品特性，SEO友好',
          improvements: ['可以添加品牌词提升权威性']
        }
      ];
    }

    return [];
  }
}
