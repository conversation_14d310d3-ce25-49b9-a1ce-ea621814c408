# PRP 示例：用户认证 API

> 这是一个完整的 PRP 示例，展示如何在 Augment AI 环境中实施 Context Engineering

## 📋 基本信息
- **功能名称**: 用户认证 API
- **优先级**: 高
- **预估工作量**: 16 小时
- **负责人**: 开发团队
- **创建日期**: 2024-01-15
- **目标完成日期**: 2024-01-20

## 🎯 功能概述
**目标**: 实现一个安全的用户认证 API，支持注册、登录、密码重置和 JWT 令牌管理

**业务价值**: 为应用程序提供安全的用户身份验证基础设施，支持用户账户管理和访问控制

**用户故事**: 
- 作为新用户，我希望能够注册账户，以便使用应用程序的功能
- 作为已注册用户，我希望能够安全登录，以便访问我的个人数据
- 作为用户，我希望能够重置忘记的密码，以便重新获得账户访问权限

## ✅ 成功标准

### 功能标准
- [ ] 用户可以使用邮箱和密码注册新账户
- [ ] 用户可以使用邮箱和密码登录
- [ ] 用户可以请求密码重置并通过邮件重置密码
- [ ] 系统生成和验证 JWT 访问令牌
- [ ] 支持令牌刷新机制
- [ ] 实现基本的用户资料管理

### 质量标准
- [ ] API 响应时间 < 200ms (95th percentile)
- [ ] 代码覆盖率 ≥ 85%
- [ ] 所有静态分析检查通过
- [ ] 安全扫描无高危漏洞
- [ ] API 文档完整且准确

### 安全标准
- [ ] 密码使用 bcrypt 加密存储
- [ ] 实现速率限制防止暴力攻击
- [ ] JWT 令牌包含适当的过期时间
- [ ] 输入验证防止注入攻击
- [ ] 敏感操作需要邮件确认

## 📚 上下文信息

### 相关代码模式查询
```markdown
使用 codebase-retrieval 查询以下信息：

1. "查找项目中现有的 API 路由定义模式，包括：
   - 路由组织结构
   - 中间件使用方式
   - 错误处理模式
   - 输入验证方法"

2. "分析项目中的数据库访问模式，包括：
   - ORM 或数据库客户端使用
   - 数据模型定义方式
   - 事务处理模式
   - 连接管理方法"

3. "查找项目中的安全实现模式，包括：
   - 认证中间件实现
   - 密码处理方法
   - JWT 令牌管理
   - 输入验证和清理"

4. "分析项目中的测试模式，包括：
   - API 测试结构
   - Mock 和 Fixture 使用
   - 测试数据管理
   - 集成测试方法"
```

### 架构约束
- **框架**: Express.js + TypeScript
- **数据库**: PostgreSQL with Prisma ORM
- **认证**: JWT with refresh tokens
- **邮件服务**: SendGrid
- **缓存**: Redis (for rate limiting)
- **部署**: Docker containers

### 依赖关系
- **内部依赖**: 
  - 数据库连接模块
  - 邮件服务模块
  - 日志记录模块
- **外部依赖**: 
  - bcryptjs (密码加密)
  - jsonwebtoken (JWT 处理)
  - joi (输入验证)
  - express-rate-limit (速率限制)

## 🔧 技术实现计划

### 阶段1：准备和设计 (预估: 4小时)
- [ ] 分析现有代码模式和架构
- [ ] 设计用户数据模型和数据库 schema
- [ ] 设计 API 接口规范 (OpenAPI)
- [ ] 创建认证流程图和安全策略文档

### 阶段2：数据层实现 (预估: 3小时)
- [ ] 创建用户数据模型 (Prisma schema)
- [ ] 实现数据库迁移脚本
- [ ] 创建用户数据访问层 (Repository pattern)
- [ ] 实现数据验证和约束

### 阶段3：业务逻辑层 (预估: 4小时)
- [ ] 实现用户注册逻辑
- [ ] 实现用户登录验证
- [ ] 实现密码重置流程
- [ ] 实现 JWT 令牌生成和验证
- [ ] 实现用户资料管理

### 阶段4：API 层实现 (预估: 3小时)
- [ ] 创建认证相关路由
- [ ] 实现输入验证中间件
- [ ] 实现认证中间件
- [ ] 实现速率限制中间件
- [ ] 实现统一错误处理

### 阶段5：测试和文档 (预估: 2小时)
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 生成 API 文档
- [ ] 创建使用示例和 Postman 集合

## 📡 API 规范

### 端点定义
```yaml
paths:
  /api/auth/register:
    post:
      summary: 用户注册
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password, name]
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
                name:
                  type: string
                  minLength: 2
      responses:
        201:
          description: 注册成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  tokens:
                    $ref: '#/components/schemas/TokenPair'
        400:
          description: 请求参数错误
        409:
          description: 邮箱已存在

  /api/auth/login:
    post:
      summary: 用户登录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
      responses:
        200:
          description: 登录成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  tokens:
                    $ref: '#/components/schemas/TokenPair'
        401:
          description: 认证失败

  /api/auth/refresh:
    post:
      summary: 刷新访问令牌
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [refreshToken]
              properties:
                refreshToken:
                  type: string
      responses:
        200:
          description: 令牌刷新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenPair'

  /api/auth/forgot-password:
    post:
      summary: 请求密码重置
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email]
              properties:
                email:
                  type: string
                  format: email
      responses:
        200:
          description: 重置邮件已发送

  /api/auth/reset-password:
    post:
      summary: 重置密码
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [token, password]
              properties:
                token:
                  type: string
                password:
                  type: string
                  minLength: 8
      responses:
        200:
          description: 密码重置成功

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        name:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    TokenPair:
      type: object
      properties:
        accessToken:
          type: string
        refreshToken:
          type: string
        expiresIn:
          type: integer
```

## ✅ 验证检查点

### 开发阶段验证
```bash
# 代码质量检查
npm run lint                 # ESLint 检查
npm run type-check          # TypeScript 类型检查
npm run format              # Prettier 格式化检查

# 安全检查
npm audit                   # 依赖漏洞检查
npm run security-scan       # 代码安全扫描
```

### 测试阶段验证
```bash
# 单元测试
npm run test:unit           # 运行单元测试
npm run test:coverage       # 检查测试覆盖率

# 集成测试
npm run test:integration    # API 集成测试
npm run test:e2e           # 端到端测试

# 性能测试
npm run test:performance    # API 性能测试
```

### 功能验证测试用例
```bash
# 使用 curl 进行手动测试

# 1. 用户注册
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "name": "Test User"
  }'

# 2. 用户登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'

# 3. 令牌刷新
curl -X POST http://localhost:3000/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "your_refresh_token_here"
  }'

# 4. 密码重置请求
curl -X POST http://localhost:3000/api/auth/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

## 🚨 风险和注意事项

### 安全风险
- **密码安全**: 确保使用足够强度的 bcrypt 加密
- **JWT 安全**: 设置适当的过期时间和签名密钥
- **速率限制**: 防止暴力攻击和 API 滥用
- **输入验证**: 防止 SQL 注入和 XSS 攻击

### 性能风险
- **数据库查询**: 优化用户查询和索引
- **令牌验证**: 考虑令牌验证的性能影响
- **邮件发送**: 异步处理邮件发送避免阻塞

### 业务风险
- **邮件送达**: 确保密码重置邮件能够正常送达
- **用户体验**: 提供清晰的错误消息和反馈
- **数据一致性**: 确保用户数据的一致性和完整性

## 📖 参考资料
- [Express.js 官方文档](https://expressjs.com/)
- [Prisma ORM 文档](https://www.prisma.io/docs/)
- [JWT 最佳实践](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [OWASP 认证备忘单](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
- [Node.js 安全最佳实践](https://nodejs.org/en/docs/guides/security/)

## 📝 实施记录

### 关键决策
- **密码策略**: 最少8位，包含字母和数字
- **JWT 过期时间**: 访问令牌15分钟，刷新令牌7天
- **速率限制**: 登录尝试每分钟最多5次
- **邮件模板**: 使用 HTML 模板提供更好的用户体验

### 实施过程中的问题
- [记录实施过程中遇到的问题和解决方案]

### 性能优化
- [记录性能优化的措施和效果]

### 安全加固
- [记录额外的安全措施和配置]

---

*这个 PRP 示例展示了如何在 Augment AI 环境中应用 Context Engineering 方法论来实现一个完整的功能。使用时请根据具体项目需求进行调整。*
