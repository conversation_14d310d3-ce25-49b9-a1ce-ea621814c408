#!/usr/bin/env python3
"""
创建剩余章节的脚本
"""

chapters = [
    {
        "num": "05",
        "title": "创业历程与37signals",
        "intro": "从一个小型设计咨询公司到创造出影响数百万人的产品，37signals的发展历程体现了DHH对小团队、简单解决方案和可持续发展的信念。本章探讨了Basecamp和HEY的创建过程，以及DHH对现代创业文化的独特见解。",
        "content": """
        <p><strong>Lex：</strong> 让我问你关于小团队。所以你多次提到杰森，杰森·弗里德，你们已经是合作伙伴很长，很长时间。</p>

        <p><strong>DHH：</strong> 是的，我们已经合作了20多年。杰森雇佣我作为网页设计师，但他也知道我可以做一些编程。我们有这个想法，我们想要建立一个项目管理工具。</p>

        <p>当时我们正在与客户合作，我们正在做咨询工作，我们一直在通过电子邮件管理项目。这是一团糟。你有这些长长的电子邮件链，人们回复所有人，没有人知道最新版本的文件在哪里。</p>

        <p>所以我们决定建立自己的工具。那就是Basecamp的开始。我们不知道它会变成什么。我们只是想解决我们自己的问题。</p>

        <p>我认为这是创建伟大产品的最好方式。不是试图猜测市场想要什么，而是解决你自己有的真实问题。如果你有问题，很可能其他人也有同样的问题。</p>
        """
    },
    {
        "num": "06",
        "title": "与苹果的斗争",
        "intro": "DHH与苹果App Store的冲突成为了科技界的一个标志性事件，揭示了大科技公司的垄断行为。本章详述了HEY应用被拒绝的经过，以及这场斗争对整个开发者生态系统的影响。",
        "content": """
        <p><strong>Lex：</strong> 既然你提到苹果，我必须问互联网上某人提交的问题。DHH仍然讨厌苹果吗？我相信问题是。</p>

        <p><strong>DHH：</strong> 我不讨厌苹果。我对苹果极其失望。我花了20年传播苹果的福音。我是苹果的巨大粉丝。我购买了他们的所有产品。我为他们辩护。</p>

        <p>然后当我们试图在App Store上发布HEY时，他们基本上试图勒索我们。他们说，"你必须给我们你收入的30%，否则你不能在我们的平台上。"</p>

        <p>这不是关于遵循规则。这是关于苹果在我们已经建立了整个业务之后改变规则。我们按照他们的指导方针建立了HEY，然后他们说，"哦，顺便说一下，你现在欠我们30%。"</p>

        <p>这就像黑手党的保护费。这是不对的，我不会为此保持沉默。</p>
        """
    },
    {
        "num": "07",
        "title": "育儿体验与家庭价值观",
        "intro": "成为父亲彻底改变了DHH的人生观。本章探讨了育儿如何影响他的工作方式、生活优先级，以及对传统家庭价值观的重新思考。DHH分享了关于工作与生活平衡的深刻见解。",
        "content": """
        <p><strong>Lex：</strong> 我必须回到，你之前提到它，你是父母。你能说到成为父亲对你生活的影响吗？</p>

        <p><strong>DHH：</strong> 我认为关于父亲身份有趣的是，对我来说，我甚至不确定它是我想要的东西。它花了遇到正确的女人并让她说服我这是正确的想法，在我们甚至开始之前。</p>

        <p>我在20多岁后期或甚至30多岁早期没有开始我自己的家庭在优先级列表上。它真的是遇到我的妻子杰米并她告诉我这是我想要的推动力。</p>

        <p>现在回顾它，它几乎似乎疯狂。就像现实中有这个分叉。如果那没有发生，我坐在这里现在，不是父亲，没有家庭，知道我现在知道关于有那个家庭的快乐的后悔水平会是存在的。</p>

        <p>最终得到我现在有的家庭，最终得到我的三个男孩只是变革性体验。</p>
        """
    },
    {
        "num": "08",
        "title": "赛车运动的激情",
        "intro": "从25岁才获得驾照到在勒芒24小时耐力赛中获得组别冠军，DHH的赛车之路展现了他对追求卓越的执着。本章探讨了赛车运动如何影响他的人生哲学和风险管理理念。",
        "content": """
        <p><strong>Lex：</strong> 好吧，就像你提到的，你喜欢赛车，你在世界级竞争水平做它，这令人难以置信。所以你如何进入它？什么吸引你赛车？你喜欢它什么？</p>

        <p><strong>DHH：</strong> 关于进入赛车的有趣事情是我直到25岁才得到我的驾驶执照。我在丹麦哥本哈根长大，汽车税基本上超过200%。所以你为三辆车付钱，你得到一辆。</p>

        <p>但当我25岁时，我意识到我想在美国花更多时间。我需要有驾驶执照。然后两年后，我在芝加哥遇到的朋友带我到芝加哥Autobahn乡村俱乐部。我坐在赛车中，我第一次驾驶赛车。</p>

        <p>我有同样类型的伪宗教体验，当我开始在Ruby上工作时。在有那个体验后，首先它只是有史以来最令人惊叹的事情。驾驶赛车的物理感觉真的独特。</p>

        <p>那种危险和技能的平衡如此令人陶醉。</p>
        """
    },
    {
        "num": "09",
        "title": "编程工具与开发环境",
        "intro": "从Mac到Linux的转变，从TextMate到Neovim的选择，DHH对编程工具的偏好反映了他对简洁和效率的追求。本章探讨了工具选择如何影响编程体验和生产力。",
        "content": """
        <p><strong>Lex：</strong> 我必须问你关于你编程设置的细节，IDE，所有那种东西。让我们描绘完美编程设置的图片。</p>

        <p><strong>DHH：</strong> 有趣，因为如果你问我，让我看，一年半前，我会给你与我基本上20年给任何人的同样答案。我想要Mac。我喜欢Magic键盘。我喜欢单显示器。</p>

        <p>但我今天有的设置是Linux。我在一年多前切换，在我最终对苹果足够厌倦，我不能再那样做之后。然后我使用这个叫Lofree Flow84的低轮廓机械键盘，这只是我听过最光荣声音键盘。</p>

        <p>我知道有很多机械键盘鉴赏家可能会在这上面与我争论。但对我来说，Lofree Flow84只是我甚至不知道存在的喜悦。</p>

        <p>我们作为程序员花如此多时间与那些键互动。它真的有点重要，以我没有完全欣赏的方式。</p>
        """
    },
    {
        "num": "10",
        "title": "编程语言的选择与观点",
        "intro": "DHH对编程语言有着强烈而明确的观点。本章探讨了他对不同编程语言的看法，特别是他对TypeScript的批评和对Ruby、JavaScript、Go的推荐。",
        "content": """
        <p><strong>Lex：</strong> 荒谬问题，我们谈论了一堆编程语言。你告诉我们你多爱JavaScript。它是你第二喜欢编程语言。TypeScript会是第三吗？</p>

        <p><strong>DHH：</strong> TypeScript甚至不会在这个宇宙中。我讨厌TypeScript就像我喜欢JavaScript一样多。</p>

        <p><strong>Lex：</strong> 所以你讨厌什么，哦男人，我不够聪明理解那个数学。好吧，在我问关于其他编程语言之前，如果你可以将你对TypeScript的仇恨封装成可以人类解释的东西，推理会是什么？</p>

        <p><strong>DHH：</strong> JavaScript在其元编程某些方面闻起来很像Ruby。TypeScript只是将那个复杂化到令人愤怒程度。我看不到好处。我只看到成本。我看到额外输入。</p>

        <p>我不想要那种体验，但我也不想从美学的角度。我讨厌重复。TypeScript让我重复用户三次。我没有时间做这个。</p>
        """
    },
    {
        "num": "11",
        "title": "开源项目的管理哲学",
        "intro": "作为Ruby on Rails的创造者和维护者，DHH对开源项目管理有着独特的见解。本章探讨了他的仁慈独裁者理念，以及对WordPress争议的分析。",
        "content": """
        <p><strong>Lex：</strong> 我必须问关于开源。运行成功开源项目需要什么？你说过那个。开源是民主的是误解。它实际上是精英制。</p>

        <p><strong>DHH：</strong> 这会是这里有点偏见证据，但我认为我很早学到在开源中燃尽的快速方式是将它作为业务对待，好像你的用户是客户，好像他们对你的时间和你的注意和你的方向有合法性声明。</p>

        <p>我不会做你告诉我的。我在这里作为礼物带来者。我分享我在我自己时间，我自己意志写的代码。你不必说谢谢。你可以拿代码并用它做任何你想要的。如果你想要，你可以贡献回来，但你不能告诉我做什么或去哪里或如何行动。</p>

        <p>我不是供应商。我们没有交易关系。</p>
        """
    },
    {
        "num": "12",
        "title": "人生哲学与成功定义",
        "intro": "在事业成功的背后，DHH有着深刻的人生哲学。本章探讨了他对金钱、成功、幸福的看法，以及对未来的乐观态度。从Coco Chanel的名言到对人类文明的思考，DHH分享了他的人生智慧。",
        "content": """
        <p><strong>Lex：</strong> 你经常回到的一个引用，我很享受，是可可·香奈儿引用，生活中最好事情是免费的。第二好事情非常，非常昂贵。</p>

        <p><strong>DHH：</strong> 我会容易说你可以关心其他东西。只是知道优先级顺序。如果你被祝福有你爱的伴侣，一些你崇拜的孩子，你已经赢得大多数人类能够实现的最伟大奖品。</p>

        <p>当你追逐第二好事情时，容易失去那个视线。因为你知道什么？他们也非常好。我真的喜欢那个帕加尼Zonda。但它只是不像你认为的那样有趣那么长或那么深。</p>

        <p><strong>Lex：</strong> 什么给你关于我们在这里进行的这整个事情未来的希望？人类文明。</p>

        <p><strong>DHH：</strong> 我发现乐观比悲观更容易，因为我两种方式都不知道。所以如果我得到选择，为什么不只是选择相信它会成功？</p>

        <p>我们是否有正确措施及时修复它，如果那甚至可能或不，完全悬而未决，我们不知道。但希望大概某人仍然，至少在可预见的未来，必须理解AI产生的是否实际工作。</p>
        """
    }
]

template = '''<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第{num}章：{title}</title>
    <link rel="stylesheet" type="text/css" href="../Styles/style.css"/>
</head>
<body>
    <div class="chapter-title-page">
        <div class="chapter-number">第{num}章</div>
        <h1 class="chapter-title">{title}</h1>
    </div>
    
    <div class="chapter-intro">
        <p>{intro}</p>
    </div>

    <div class="conversation">
        {content}
    </div>
</body>
</html>'''

for chapter in chapters:
    filename = f"DHH访谈EPUB/OEBPS/Text/chapter{chapter['num']}.xhtml"
    content = template.format(**chapter)
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Created {filename}")

print("All chapters created successfully!")
