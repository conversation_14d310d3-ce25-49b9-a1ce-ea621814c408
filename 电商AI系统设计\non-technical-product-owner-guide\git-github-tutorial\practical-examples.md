# Git实际操作示例集

> 通过真实的项目场景学习Git的实际应用

## 🎯 项目场景1：个人博客网站

### 项目背景
您要创建一个个人博客网站，包含HTML、CSS、JavaScript文件，并希望使用Git进行版本管理。

### 完整操作流程

#### 步骤1：项目初始化
```bash
# 创建项目目录
mkdir my-blog
cd my-blog

# 初始化Git仓库
git init

# 查看状态
git status
```

#### 步骤2：创建基础文件
```bash
# 创建HTML文件
cat > index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的个人博客</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <h1>欢迎来到我的博客</h1>
        <nav>
            <ul>
                <li><a href="#home">首页</a></li>
                <li><a href="#about">关于我</a></li>
                <li><a href="#contact">联系方式</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <article>
            <h2>我的第一篇博客</h2>
            <p>今天开始学习Git版本控制！</p>
            <p>这是一个激动人心的开始。</p>
        </article>
    </main>
    
    <footer>
        <p>&copy; 2024 我的博客. 保留所有权利.</p>
    </footer>
    
    <script src="js/main.js"></script>
</body>
</html>
EOF

# 创建CSS目录和文件
mkdir css
cat > css/style.css << 'EOF'
/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    text-align: center;
}

header h1 {
    margin-bottom: 1rem;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

nav a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

main {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

article {
    background: white;
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

article h2 {
    color: #667eea;
    margin-bottom: 1rem;
}

footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}
EOF

# 创建JavaScript目录和文件
mkdir js
cat > js/main.js << 'EOF'
// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('博客页面加载完成！');
    
    // 添加导航点击事件
    const navLinks = document.querySelectorAll('nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('href');
            console.log('点击了导航链接:', target);
            
            // 这里可以添加页面滚动或其他交互逻辑
            if (target === '#home') {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });
    });
    
    // 添加文章点击事件
    const articles = document.querySelectorAll('article');
    articles.forEach(article => {
        article.addEventListener('click', function() {
            this.style.transform = 'scale(1.02)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    });
});

// 添加滚动效果
window.addEventListener('scroll', function() {
    const header = document.querySelector('header');
    if (window.scrollY > 100) {
        header.style.opacity = '0.9';
    } else {
        header.style.opacity = '1';
    }
});
EOF

# 创建README文件
cat > README.md << 'EOF'
# 我的个人博客

这是我的个人博客网站项目，使用HTML、CSS和JavaScript构建。

## 项目结构

```
my-blog/
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   └── main.js        # JavaScript文件
└── README.md          # 项目说明
```

## 功能特性

- 响应式设计
- 现代化的渐变背景
- 平滑滚动效果
- 交互式导航

## 如何运行

1. 克隆或下载项目
2. 在浏览器中打开 `index.html`

## 开发计划

- [x] 基础页面结构
- [x] 样式设计
- [x] 基础交互
- [ ] 添加更多页面
- [ ] 集成博客系统
- [ ] 添加评论功能

## 版本历史

- v1.0.0 - 初始版本，基础功能完成
EOF

# 创建.gitignore文件
cat > .gitignore << 'EOF'
# 临时文件
*.tmp
*.temp
.cache/

# 日志文件
*.log
logs/

# 操作系统文件
.DS_Store
Thumbs.db

# 编辑器配置
.vscode/
.idea/
*.swp
*.swo

# 依赖文件（如果后续添加）
node_modules/
npm-debug.log*

# 构建输出（如果后续添加构建工具）
dist/
build/
EOF
```

#### 步骤3：第一次提交
```bash
# 查看文件状态
git status

# 添加所有文件
git add .

# 查看暂存状态
git status

# 创建第一次提交
git commit -m "初始提交：创建个人博客基础结构

- 添加HTML主页面结构
- 实现响应式CSS样式
- 添加JavaScript交互功能
- 创建项目文档和配置文件"

# 查看提交历史
git log --oneline
```

#### 步骤4：连接GitHub并推送
```bash
# 在GitHub创建仓库 my-blog

# 添加远程仓库
git remote add origin https://github.com/your-username/my-blog.git

# 推送到GitHub
git push -u origin main
```

#### 步骤5：开发新功能 - 添加关于页面
```bash
# 创建功能分支
git checkout -b feature/about-page

# 创建关于页面
cat > about.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我 - 我的个人博客</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <h1>关于我</h1>
        <nav>
            <ul>
                <li><a href="index.html">首页</a></li>
                <li><a href="about.html">关于我</a></li>
                <li><a href="contact.html">联系方式</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <article>
            <h2>个人简介</h2>
            <p>我是一名热爱编程的开发者，正在学习Git版本控制。</p>
            <p>我的技能包括：</p>
            <ul>
                <li>HTML/CSS</li>
                <li>JavaScript</li>
                <li>Git版本控制</li>
                <li>持续学习新技术</li>
            </ul>
        </article>
        
        <article>
            <h2>我的目标</h2>
            <p>通过这个博客分享我的学习经验和技术心得。</p>
        </article>
    </main>
    
    <footer>
        <p>&copy; 2024 我的博客. 保留所有权利.</p>
    </footer>
    
    <script src="js/main.js"></script>
</body>
</html>
EOF

# 更新主页导航链接
sed -i 's|<li><a href="#about">关于我</a></li>|<li><a href="about.html">关于我</a></li>|' index.html

# 添加新的CSS样式
cat >> css/style.css << 'EOF'

/* 关于页面特殊样式 */
article ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

article li {
    margin: 0.5rem 0;
}
EOF
```

#### 步骤6：提交新功能
```bash
# 查看修改
git status
git diff

# 添加修改的文件
git add .

# 提交新功能
git commit -m "添加关于页面功能

- 创建about.html页面
- 更新主页导航链接
- 添加关于页面专用样式
- 完善个人信息展示"

# 推送功能分支
git push -u origin feature/about-page
```

#### 步骤7：合并功能到主分支
```bash
# 切换到主分支
git checkout main

# 合并功能分支
git merge feature/about-page

# 推送合并后的主分支
git push

# 删除功能分支
git branch -d feature/about-page
git push origin --delete feature/about-page
```

#### 步骤8：修复bug - 样式问题
```bash
# 发现导航在小屏幕上显示有问题，创建修复分支
git checkout -b bugfix/mobile-navigation

# 修复CSS
cat >> css/style.css << 'EOF'

/* 移动端响应式修复 */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    nav a {
        display: block;
        margin: 0.2rem 0;
    }
    
    main {
        padding: 0 0.5rem;
    }
    
    article {
        padding: 1rem;
    }
}
EOF

# 提交修复
git add css/style.css
git commit -m "修复：改善移动端导航显示

- 添加移动端媒体查询
- 修复导航在小屏幕上的布局问题
- 优化移动端内容间距"

# 推送并合并
git push -u origin bugfix/mobile-navigation
git checkout main
git merge bugfix/mobile-navigation
git push
git branch -d bugfix/mobile-navigation
git push origin --delete bugfix/mobile-navigation
```

---

## 🎯 项目场景2：团队协作开发

### 项目背景
您加入了一个团队项目，需要与其他开发者协作开发一个待办事项应用。

### 协作流程演示

#### 步骤1：克隆团队项目
```bash
# 克隆团队仓库
git clone https://github.com/team/todo-app.git
cd todo-app

# 查看项目结构
ls -la

# 查看分支
git branch -a

# 查看最近的提交
git log --oneline -5
```

#### 步骤2：设置开发环境
```bash
# 创建自己的开发分支
git checkout -b feature/user-authentication

# 查看当前分支
git branch

# 确保从最新的main分支开始
git checkout main
git pull origin main
git checkout feature/user-authentication
git merge main
```

#### 步骤3：开发新功能
```bash
# 创建用户认证相关文件
mkdir auth
cat > auth/login.js << 'EOF'
// 用户登录功能
class LoginManager {
    constructor() {
        this.users = new Map();
        this.currentUser = null;
    }
    
    // 注册用户
    register(username, password, email) {
        if (this.users.has(username)) {
            throw new Error('用户名已存在');
        }
        
        const user = {
            username,
            password: this.hashPassword(password),
            email,
            createdAt: new Date(),
            todos: []
        };
        
        this.users.set(username, user);
        return { success: true, message: '注册成功' };
    }
    
    // 用户登录
    login(username, password) {
        const user = this.users.get(username);
        if (!user) {
            throw new Error('用户不存在');
        }
        
        if (user.password !== this.hashPassword(password)) {
            throw new Error('密码错误');
        }
        
        this.currentUser = user;
        return { success: true, user: username };
    }
    
    // 用户登出
    logout() {
        this.currentUser = null;
        return { success: true, message: '已登出' };
    }
    
    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }
    
    // 简单的密码哈希（实际项目中应使用更安全的方法）
    hashPassword(password) {
        return btoa(password); // 仅用于演示
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LoginManager;
}
EOF

# 创建登录界面
cat > auth/login.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 待办事项应用</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-form">
            <h2>用户登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" required>
                </div>
                <button type="submit">登录</button>
            </form>
            <p>还没有账户？ <a href="register.html">立即注册</a></p>
        </div>
    </div>
    
    <script src="login.js"></script>
    <script>
        const loginForm = document.getElementById('loginForm');
        const loginManager = new LoginManager();
        
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const result = loginManager.login(username, password);
                if (result.success) {
                    alert('登录成功！');
                    window.location.href = '../index.html';
                }
            } catch (error) {
                alert('登录失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
EOF

# 创建认证样式
cat > auth/auth.css << 'EOF'
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-form {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

button {
    width: 100%;
    padding: 0.75rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background: #5a6fd8;
}

.auth-form p {
    text-align: center;
    margin-top: 1rem;
}

.auth-form a {
    color: #667eea;
    text-decoration: none;
}

.auth-form a:hover {
    text-decoration: underline;
}
EOF
```

#### 步骤4：提交开发进度
```bash
# 查看修改
git status

# 添加新文件
git add auth/

# 提交进度
git commit -m "开发用户认证功能 - 第一阶段

- 实现LoginManager类的核心功能
- 添加用户注册、登录、登出方法
- 创建登录页面HTML结构
- 设计认证页面样式

待完成：
- 注册页面
- 与主应用的集成
- 数据持久化"

# 推送到远程
git push -u origin feature/user-authentication
```

#### 步骤5：同步团队更新
```bash
# 获取远程更新
git fetch origin

# 查看远程分支
git branch -r

# 切换到main分支并更新
git checkout main
git pull origin main

# 回到功能分支并合并最新更改
git checkout feature/user-authentication
git merge main

# 如果有冲突，解决冲突
# 编辑冲突文件，然后：
# git add .
# git commit -m "解决合并冲突"
```

#### 步骤6：完成功能开发
```bash
# 创建注册页面
cat > auth/register.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 待办事项应用</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-form">
            <h2>用户注册</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" required minlength="3">
                </div>
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码:</label>
                    <input type="password" id="confirmPassword" required>
                </div>
                <button type="submit">注册</button>
            </form>
            <p>已有账户？ <a href="login.html">立即登录</a></p>
        </div>
    </div>
    
    <script src="login.js"></script>
    <script>
        const registerForm = document.getElementById('registerForm');
        const loginManager = new LoginManager();
        
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                alert('密码确认不匹配！');
                return;
            }
            
            try {
                const result = loginManager.register(username, password, email);
                if (result.success) {
                    alert('注册成功！请登录。');
                    window.location.href = 'login.html';
                }
            } catch (error) {
                alert('注册失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
EOF

# 更新README文档
cat >> README.md << 'EOF'

## 用户认证功能

### 功能特性
- 用户注册和登录
- 密码安全处理
- 会话管理
- 响应式认证界面

### 使用方法
1. 访问 `auth/register.html` 注册新用户
2. 访问 `auth/login.html` 登录现有用户
3. 登录成功后自动跳转到主应用

### 技术实现
- 使用ES6类实现用户管理
- 本地存储用户数据（演示用）
- 表单验证和错误处理
- 现代化的UI设计
EOF

# 提交完整功能
git add .
git commit -m "完成用户认证功能开发

- 添加用户注册页面和功能
- 完善表单验证和错误处理
- 更新项目文档
- 实现完整的认证流程

功能已完成，准备代码审查"

# 推送最终版本
git push origin feature/user-authentication
```

#### 步骤7：创建Pull Request
```bash
# 在GitHub上创建Pull Request
# 1. 访问GitHub仓库页面
# 2. 点击 "Compare & pull request"
# 3. 填写PR描述：

# Pull Request标题：
# 添加用户认证功能

# Pull Request描述：
# ## 功能描述
# 实现了完整的用户认证系统，包括注册、登录和会话管理功能。
# 
# ## 主要变更
# - 新增 `auth/` 目录包含认证相关文件
# - 实现 `LoginManager` 类处理用户管理
# - 创建响应式的登录和注册页面
# - 添加表单验证和错误处理
# 
# ## 测试说明
# - [x] 用户注册功能正常
# - [x] 用户登录功能正常
# - [x] 表单验证工作正常
# - [x] 响应式设计在移动端正常显示
# 
# ## 相关问题
# Closes #123
```

#### 步骤8：代码审查和合并
```bash
# 等待团队成员审查代码
# 根据反馈修改代码

# 如果需要修改：
git add .
git commit -m "根据代码审查反馈进行修改

- 修复密码验证逻辑
- 改善错误提示信息
- 优化代码注释"

git push origin feature/user-authentication

# 审查通过后，维护者会合并PR
# 合并后清理本地分支
git checkout main
git pull origin main
git branch -d feature/user-authentication
```

---

## 🎯 项目场景3：开源项目贡献

### 项目背景
您想为一个开源项目贡献代码，修复一个bug或添加新功能。

### 贡献流程

#### 步骤1：Fork项目
```bash
# 1. 在GitHub上Fork目标项目
# 2. 克隆您的Fork

git clone https://github.com/your-username/open-source-project.git
cd open-source-project

# 添加上游仓库
git remote add upstream https://github.com/original-owner/open-source-project.git

# 查看远程仓库
git remote -v
```

#### 步骤2：同步上游更新
```bash
# 获取上游更新
git fetch upstream

# 切换到main分支
git checkout main

# 合并上游更新
git merge upstream/main

# 推送到您的Fork
git push origin main
```

#### 步骤3：创建功能分支
```bash
# 创建功能分支
git checkout -b fix/button-click-issue

# 开始修复bug或开发功能
# ... 编写代码 ...
```

#### 步骤4：提交和推送
```bash
# 提交修改
git add .
git commit -m "修复：解决按钮点击事件无响应问题

- 修复事件监听器绑定时机问题
- 添加防抖处理避免重复点击
- 更新相关测试用例

Fixes #456"

# 推送到您的Fork
git push origin fix/button-click-issue
```

#### 步骤5：创建Pull Request
```bash
# 在GitHub上创建PR到原项目
# 遵循项目的贡献指南
# 等待维护者审查和反馈
```

---

## 💡 实用技巧总结

### 1. 提交信息最佳实践
```bash
# 好的提交信息格式
git commit -m "类型: 简短描述

详细说明修改内容和原因

相关问题: #123"

# 常用类型
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试
chore: 构建工具
```

### 2. 分支管理策略
```bash
# 功能开发
git checkout -b feature/feature-name

# Bug修复
git checkout -b bugfix/issue-description

# 热修复
git checkout -b hotfix/critical-fix

# 发布准备
git checkout -b release/v1.2.0
```

### 3. 代码审查检查清单
- [ ] 代码符合项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 提交信息清晰明确
- [ ] 没有引入新的警告或错误
- [ ] 功能按预期工作

### 4. 团队协作技巧
```bash
# 定期同步主分支
git checkout main
git pull origin main
git checkout feature-branch
git merge main

# 使用rebase保持历史整洁
git rebase main

# 压缩提交
git rebase -i HEAD~3
```

这些实际示例展示了Git在真实项目中的应用，帮助您理解版本控制在实际开发中的重要性和实用性。
